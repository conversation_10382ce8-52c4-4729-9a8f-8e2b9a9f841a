<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Model Answers: Student Activities (Lectures 1-12)</title>
    <style>
        /* Base Styles */
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
            background-color: #f5f7fa;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        /* Header Styles */
        header {
            background: linear-gradient(135deg, #0056b3 0%, #00a0e9 100%);
            color: white;
            padding: 40px 0;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .header-content {
            max-width: 800px;
            margin: 0 auto;
        }
        
        h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        
        h2 {
            font-size: 1.8rem;
            margin-top: 30px;
            color: #0056b3;
            border-bottom: 2px solid #e1e4e8;
            padding-bottom: 10px;
        }
        
        h3 {
            font-size: 1.4rem;
            color: #0056b3;
            margin-top: 25px;
        }
        
        /* Navigation Bar */
        .nav-bar {
            background-color: #343a40;
            padding: 15px 0;
            position: sticky;
            top: 0;
            z-index: 1000;
        }
        
        .nav-content {
            max-width: 1400px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
        }
        
        .nav-links {
            display: flex;
            gap: 30px;
        }
        
        .nav-links a {
            color: white;
            text-decoration: none;
            padding: 10px 15px;
            border-radius: 4px;
            transition: background-color 0.3s;
        }
        
        .nav-links a:hover, .nav-links a.active {
            background-color: #0056b3;
        }
        
        /* Content Sections */
        .section {
            background-color: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            padding: 30px;
            margin-bottom: 30px;
        }
        
        /* Lecture Cards */
        .lecture-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin: 30px 0;
        }
        
        .lecture-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 2px solid #0056b3;
            border-radius: 12px;
            padding: 25px;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .lecture-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 86, 179, 0.15);
        }
        
        .lecture-card.active {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border-color: #1976d2;
        }
        
        /* Model Answer Styles */
        .model-answer {
            background-color: #e8f5e9;
            border-left: 4px solid #4caf50;
            padding: 20px;
            margin: 20px 0;
            border-radius: 0 8px 8px 0;
        }
        
        .answer-section {
            background-color: #f0f9e8;
            border: 1px solid #c8e6c9;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
        }
        
        .key-points {
            background-color: #fff3e0;
            border-left: 4px solid #ff9800;
            padding: 15px;
            margin: 15px 0;
            border-radius: 0 6px 6px 0;
        }
        
        .assessment-criteria {
            background-color: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 15px;
            margin: 15px 0;
            border-radius: 0 6px 6px 0;
        }
        
        /* Interactive Elements */
        .answer-toggle {
            background-color: #0056b3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin: 10px 0;
            transition: all 0.3s ease;
        }
        
        .answer-toggle:hover {
            background-color: #003d82;
            transform: translateY(-2px);
        }
        
        .hidden {
            display: none;
        }
        
        /* Search and Filter */
        .search-filter {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .search-input {
            width: 100%;
            padding: 12px;
            border: 2px solid #dee2e6;
            border-radius: 6px;
            font-size: 16px;
            margin-bottom: 15px;
        }
        
        .filter-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .filter-btn {
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .filter-btn.active {
            background: #0056b3;
            color: white;
            border-color: #0056b3;
        }
        
        /* Footer */
        footer {
            background-color: #343a40;
            color: white;
            text-align: center;
            padding: 20px 0;
            margin-top: 40px;
        }
        
        /* Responsive Design */
        @media (max-width: 768px) {
            .lecture-grid {
                grid-template-columns: 1fr;
            }
            
            .nav-links {
                flex-direction: column;
                gap: 10px;
            }
            
            .filter-buttons {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="header-content">
            <!-- Enhanced Navigation Bar -->
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px; padding: 15px 0; border-bottom: 1px solid rgba(255,255,255,0.2);">
                <div>
                    <a href="index.html" style="background: rgba(255,255,255,0.2); color: white; padding: 10px 20px; border-radius: 25px; text-decoration: none; font-size: 1em; transition: all 0.3s ease; display: inline-flex; align-items: center; gap: 10px; font-weight: 500;"
                       onmouseover="this.style.background='rgba(255,255,255,0.3)'; this.style.transform='translateY(-2px)'"
                       onmouseout="this.style.background='rgba(255,255,255,0.2)'; this.style.transform='translateY(0)'">
                        🏠 العودة للرئيسية | Back to Home
                    </a>
                </div>
                <div style="display: flex; gap: 12px;">
                    <a href="clinical_engineering_simulation.html" style="background: rgba(255,255,255,0.15); color: white; padding: 8px 16px; border-radius: 20px; text-decoration: none; font-size: 0.9em; transition: all 0.3s ease;"
                       onmouseover="this.style.background='rgba(255,255,255,0.25)'"
                       onmouseout="this.style.background='rgba(255,255,255,0.15)'">🚀 المحاكاة</a>
                    <a href="hands_on_learning_medical_devices.html" style="background: rgba(255,255,255,0.15); color: white; padding: 8px 16px; border-radius: 20px; text-decoration: none; font-size: 0.9em; transition: all 0.3s ease;"
                       onmouseover="this.style.background='rgba(255,255,255,0.25)'"
                       onmouseout="this.style.background='rgba(255,255,255,0.15)'">🎓 التعلم العملي</a>
                </div>
            </div>

            <h1>الإجابات النموذجية: أنشطة الطلاب</h1>
            <h2 style="color: rgba(255,255,255,0.9); font-weight: 300; margin-bottom: 15px; font-size: 1.5rem;">Model Answers: Student Activities</h2>
            <p>حلول شاملة ومعايير تقييم لجميع أنشطة المحاضرات (المحاضرات 1-12)</p>
            <p style="color: rgba(255,255,255,0.8); font-size: 1.1em;">Comprehensive solutions and assessment criteria for all lecture activities (Lectures 1-12)</p>
        </div>
    </header>

    <nav class="nav-bar">
        <div class="nav-content">
            <div class="nav-links">
                <a href="index.html">🏠 الرئيسية | Home</a>
                <a href="#" class="active">📝 الإجابات النموذجية | Model Answers</a>
                <a href="clinical_engineering_simulation.html">🚀 المحاكاة | Simulations</a>
                <a href="hands_on_learning_medical_devices.html">🎓 التعلم العملي | Hands-on Learning</a>
            </div>
            <div style="display: flex; gap: 10px; align-items: center;">
                <span style="color: rgba(255,255,255,0.7); font-size: 0.9em;">📚 دليل الحلول الكامل | Complete Solutions Guide</span>
                <kbd style="background: rgba(255,255,255,0.2); color: white; padding: 2px 6px; border-radius: 3px; font-size: 0.8em;">Alt+H</kbd>
            </div>
        </div>
    </nav>
    
    <div class="container">
        <!-- Search and Filter Section -->
        <div class="search-filter">
            <h3 style="margin-top: 0; color: #0056b3;">🔍 Find Specific Answers</h3>
            <input type="text" class="search-input" id="searchInput" placeholder="Search for specific topics, activities, or keywords...">
            
            <div class="filter-buttons">
                <button class="filter-btn active" onclick="filterLectures('all')">All Lectures</button>
                <button class="filter-btn" onclick="filterLectures('fundamentals')">Fundamentals (1-3)</button>
                <button class="filter-btn" onclick="filterLectures('management')">Management (4-6)</button>
                <button class="filter-btn" onclick="filterLectures('advanced')">Advanced (7-9)</button>
                <button class="filter-btn" onclick="filterLectures('specialized')">Specialized (10-12)</button>
            </div>
        </div>
        
        <!-- Overview Section -->
        <div class="section">
            <h2>📋 Overview and Usage Guide</h2>
            <p>This comprehensive model answers guide provides detailed solutions, assessment criteria, and learning objectives for all student activities across the 12-lecture Clinical Engineering course.</p>
            
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin: 20px 0;">
                <div class="key-points">
                    <h4>🎯 Purpose</h4>
                    <ul>
                        <li>Provide comprehensive model answers</li>
                        <li>Establish assessment criteria</li>
                        <li>Guide student self-evaluation</li>
                        <li>Support instructor grading</li>
                    </ul>
                </div>
                <div class="assessment-criteria">
                    <h4>📊 Assessment Framework</h4>
                    <ul>
                        <li>Knowledge comprehension (25%)</li>
                        <li>Application skills (35%)</li>
                        <li>Critical thinking (25%)</li>
                        <li>Professional communication (15%)</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- Lecture Selection Grid -->
        <div class="section">
            <h2>📚 Select Lecture for Model Answers</h2>
            <div class="lecture-grid" id="lectureGrid">
                <!-- Lectures 1-3: Fundamentals -->
                <div class="lecture-card fundamentals" onclick="showLectureAnswers(1)">
                    <h3>📖 Lecture 1: Introduction to Clinical Engineering</h3>
                    <p><strong>Activities:</strong> Role definition, scope analysis, career pathways</p>
                    <p><strong>Key Topics:</strong> Healthcare technology, professional responsibilities, regulatory environment</p>
                    <div style="margin-top: 15px;">
                        <span style="background: #e3f2fd; padding: 4px 8px; border-radius: 12px; font-size: 0.8em;">5 Activities</span>
                        <span style="background: #f3e5f5; padding: 4px 8px; border-radius: 12px; font-size: 0.8em;">3 Case Studies</span>
                    </div>
                </div>
                
                <div class="lecture-card fundamentals" onclick="showLectureAnswers(2)">
                    <h3>🏥 Lecture 2: Healthcare Technology Management</h3>
                    <p><strong>Activities:</strong> Technology assessment, lifecycle planning, cost analysis</p>
                    <p><strong>Key Topics:</strong> HTM frameworks, strategic planning, resource allocation</p>
                    <div style="margin-top: 15px;">
                        <span style="background: #e3f2fd; padding: 4px 8px; border-radius: 12px; font-size: 0.8em;">6 Activities</span>
                        <span style="background: #f3e5f5; padding: 4px 8px; border-radius: 12px; font-size: 0.8em;">2 Case Studies</span>
                    </div>
                </div>
                
                <div class="lecture-card fundamentals" onclick="showLectureAnswers(3)">
                    <h3>⚖️ Lecture 3: Regulatory Standards & Compliance</h3>
                    <p><strong>Activities:</strong> Standards analysis, compliance auditing, documentation</p>
                    <p><strong>Key Topics:</strong> FDA regulations, ISO standards, quality systems</p>
                    <div style="margin-top: 15px;">
                        <span style="background: #e3f2fd; padding: 4px 8px; border-radius: 12px; font-size: 0.8em;">7 Activities</span>
                        <span style="background: #f3e5f5; padding: 4px 8px; border-radius: 12px; font-size: 0.8em;">4 Case Studies</span>
                    </div>
                </div>
                
                <!-- Lectures 4-6: Management -->
                <div class="lecture-card management" onclick="showLectureAnswers(4)">
                    <h3>🔧 Lecture 4: Equipment Management & Maintenance</h3>
                    <p><strong>Activities:</strong> PM scheduling, inventory management, performance tracking</p>
                    <p><strong>Key Topics:</strong> Maintenance strategies, CMMS, cost optimization</p>
                    <div style="margin-top: 15px;">
                        <span style="background: #e3f2fd; padding: 4px 8px; border-radius: 12px; font-size: 0.8em;">8 Activities</span>
                        <span style="background: #f3e5f5; padding: 4px 8px; border-radius: 12px; font-size: 0.8em;">3 Case Studies</span>
                    </div>
                </div>
                
                <div class="lecture-card management" onclick="showLectureAnswers(5)">
                    <h3>🛡️ Lecture 5: Safety & Risk Management</h3>
                    <p><strong>Activities:</strong> Risk assessment, hazard analysis, safety protocols</p>
                    <p><strong>Key Topics:</strong> Patient safety, electrical safety, incident management</p>
                    <div style="margin-top: 15px;">
                        <span style="background: #e3f2fd; padding: 4px 8px; border-radius: 12px; font-size: 0.8em;">6 Activities</span>
                        <span style="background: #f3e5f5; padding: 4px 8px; border-radius: 12px; font-size: 0.8em;">5 Case Studies</span>
                    </div>
                </div>
                
                <div class="lecture-card management" onclick="showLectureAnswers(6)">
                    <h3>💰 Lecture 6: Financial Management & Procurement</h3>
                    <p><strong>Activities:</strong> Budget planning, vendor evaluation, cost-benefit analysis</p>
                    <p><strong>Key Topics:</strong> Capital planning, procurement strategies, ROI analysis</p>
                    <div style="margin-top: 15px;">
                        <span style="background: #e3f2fd; padding: 4px 8px; border-radius: 12px; font-size: 0.8em;">7 Activities</span>
                        <span style="background: #f3e5f5; padding: 4px 8px; border-radius: 12px; font-size: 0.8em;">2 Case Studies</span>
                    </div>
                </div>

                <!-- Lectures 7-9: Advanced -->
                <div class="lecture-card advanced" onclick="showLectureAnswers(7)">
                    <h3>🔬 Lecture 7: Testing & Calibration</h3>
                    <p><strong>Activities:</strong> Calibration procedures, measurement uncertainty, quality assurance</p>
                    <p><strong>Key Topics:</strong> Metrology, traceability, GUM analysis, test protocols</p>
                    <div style="margin-top: 15px;">
                        <span style="background: #e3f2fd; padding: 4px 8px; border-radius: 12px; font-size: 0.8em;">9 Activities</span>
                        <span style="background: #f3e5f5; padding: 4px 8px; border-radius: 12px; font-size: 0.8em;">4 Case Studies</span>
                    </div>
                </div>

                <div class="lecture-card advanced" onclick="showLectureAnswers(8)">
                    <h3>🏭 Lecture 8: Manufacturing & Product Lifecycle</h3>
                    <p><strong>Activities:</strong> Design controls, manufacturing processes, lifecycle management</p>
                    <p><strong>Key Topics:</strong> Quality systems, design validation, post-market surveillance</p>
                    <div style="margin-top: 15px;">
                        <span style="background: #e3f2fd; padding: 4px 8px; border-radius: 12px; font-size: 0.8em;">6 Activities</span>
                        <span style="background: #f3e5f5; padding: 4px 8px; border-radius: 12px; font-size: 0.8em;">3 Case Studies</span>
                    </div>
                </div>

                <div class="lecture-card advanced" onclick="showLectureAnswers(9)">
                    <h3>🔍 Lecture 9: Troubleshooting & Problem Solving</h3>
                    <p><strong>Activities:</strong> Diagnostic procedures, root cause analysis, corrective actions</p>
                    <p><strong>Key Topics:</strong> Systematic troubleshooting, failure analysis, CAPA</p>
                    <div style="margin-top: 15px;">
                        <span style="background: #e3f2fd; padding: 4px 8px; border-radius: 12px; font-size: 0.8em;">8 Activities</span>
                        <span style="background: #f3e5f5; padding: 4px 8px; border-radius: 12px; font-size: 0.8em;">5 Case Studies</span>
                    </div>
                </div>

                <!-- Lectures 10-12: Specialized -->
                <div class="lecture-card specialized" onclick="showLectureAnswers(10)">
                    <h3>🌐 Lecture 10: Emerging Technologies</h3>
                    <p><strong>Activities:</strong> Technology evaluation, implementation planning, impact assessment</p>
                    <p><strong>Key Topics:</strong> AI/ML, IoT, telemedicine, digital health</p>
                    <div style="margin-top: 15px;">
                        <span style="background: #e3f2fd; padding: 4px 8px; border-radius: 12px; font-size: 0.8em;">5 Activities</span>
                        <span style="background: #f3e5f5; padding: 4px 8px; border-radius: 12px; font-size: 0.8em;">2 Case Studies</span>
                    </div>
                </div>

                <div class="lecture-card specialized" onclick="showLectureAnswers(11)">
                    <h3>🎓 Lecture 11: Professional Development</h3>
                    <p><strong>Activities:</strong> Career planning, certification pathways, continuing education</p>
                    <p><strong>Key Topics:</strong> Professional growth, leadership, ethics, communication</p>
                    <div style="margin-top: 15px;">
                        <span style="background: #e3f2fd; padding: 4px 8px; border-radius: 12px; font-size: 0.8em;">4 Activities</span>
                        <span style="background: #f3e5f5; padding: 4px 8px; border-radius: 12px; font-size: 0.8em;">2 Case Studies</span>
                    </div>
                </div>

                <div class="lecture-card specialized" onclick="showLectureAnswers(12)">
                    <h3>🔮 Lecture 12: Future of Clinical Engineering</h3>
                    <p><strong>Activities:</strong> Trend analysis, strategic planning, innovation assessment</p>
                    <p><strong>Key Topics:</strong> Future trends, challenges, opportunities, vision</p>
                    <div style="margin-top: 15px;">
                        <span style="background: #e3f2fd; padding: 4px 8px; border-radius: 12px; font-size: 0.8em;">3 Activities</span>
                        <span style="background: #f3e5f5; padding: 4px 8px; border-radius: 12px; font-size: 0.8em;">1 Case Study</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Model Answers Content (Initially Hidden) -->
        <div id="answersContent" class="section" style="display: none;">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                <h2 id="answersTitle">Model Answers</h2>
                <button onclick="hideAnswers()" style="background: #dc3545; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">✕ Close</button>
            </div>

            <div id="answersBody">
                <!-- Content will be populated by JavaScript -->
            </div>
        </div>

        <!-- Comprehensive Activity Index -->
        <div class="section">
            <h2>📋 Complete Activity Index</h2>
            <p>Direct access to all activities with model answers, key points, and assessment criteria.</p>

            <div id="activityIndex" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 20px 0;">
                <!-- Activity index will be populated by JavaScript -->
            </div>
        </div>

        <!-- Quick Statistics -->
        <div class="section">
            <h2>📊 Course Statistics</h2>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px;">
                <div style="background: #e3f2fd; padding: 20px; border-radius: 8px; text-align: center;">
                    <h3 style="margin: 0; color: #1976d2; font-size: 2em;">12</h3>
                    <p style="margin: 5px 0; color: #495057;">Total Lectures</p>
                </div>
                <div style="background: #e8f5e9; padding: 20px; border-radius: 8px; text-align: center;">
                    <h3 style="margin: 0; color: #388e3c; font-size: 2em;" id="totalActivitiesCount">73</h3>
                    <p style="margin: 5px 0; color: #495057;">Total Activities</p>
                </div>
                <div style="background: #fff3e0; padding: 20px; border-radius: 8px; text-align: center;">
                    <h3 style="margin: 0; color: #f57c00; font-size: 2em;" id="totalCaseStudiesCount">36</h3>
                    <p style="margin: 5px 0; color: #495057;">Case Studies</p>
                </div>
                <div style="background: #fce4ec; padding: 20px; border-radius: 8px; text-align: center;">
                    <h3 style="margin: 0; color: #c2185b; font-size: 2em;">100%</h3>
                    <p style="margin: 5px 0; color: #495057;">Coverage</p>
                </div>
            </div>
        </div>
    </div>

    <footer>
        <p>&copy; 2025 Clinical Engineering Model Answers | Dr. Mohammed Yagoub Esmail</p>
        <p>Nahda College | All Rights Reserved</p>
    </footer>

    <script>
        // Comprehensive Model Answers Data Structure
        const modelAnswers = {
            1: {
                title: "Lecture 1: Introduction to Clinical Engineering",
                activities: [
                    {
                        question: "Define Clinical Engineering and explain its role in healthcare delivery.",
                        modelAnswer: "Clinical Engineering is a specialized field that applies engineering principles and practices to healthcare technology management. It encompasses the application of engineering and management skills to ensure the safe and effective use of medical equipment and systems in healthcare facilities. Clinical engineers serve as the bridge between technology and patient care, ensuring that medical devices operate safely, effectively, and efficiently while supporting clinical decision-making and improving patient outcomes.",
                        keyPoints: [
                            "Integration of engineering principles with healthcare needs",
                            "Focus on medical equipment lifecycle management",
                            "Patient safety and regulatory compliance",
                            "Cost-effective technology solutions",
                            "Interdisciplinary collaboration with clinical staff",
                            "Technology assessment and evaluation",
                            "Risk management and quality assurance"
                        ],
                        assessmentCriteria: [
                            "Accurate definition (20 points)",
                            "Clear explanation of healthcare role (25 points)",
                            "Examples of applications (20 points)",
                            "Understanding of scope (20 points)",
                            "Professional communication (15 points)"
                        ]
                    },
                    {
                        question: "Analyze the career pathways available in Clinical Engineering.",
                        modelAnswer: "Clinical Engineering offers diverse career pathways including: 1) Hospital-based clinical engineers managing equipment and technology, 2) Regulatory affairs specialists ensuring compliance, 3) Medical device industry roles in R&D and quality assurance, 4) Consulting positions providing specialized expertise, 5) Academic and research positions advancing the field, 6) Government regulatory positions, 7) Healthcare technology management leadership roles.",
                        keyPoints: [
                            "Hospital/healthcare facility positions",
                            "Medical device industry opportunities",
                            "Regulatory and compliance roles",
                            "Consulting and independent practice",
                            "Academic and research careers",
                            "Government and regulatory agencies",
                            "Healthcare technology leadership"
                        ],
                        assessmentCriteria: [
                            "Comprehensive pathway identification (30 points)",
                            "Understanding of role requirements (25 points)",
                            "Career progression awareness (20 points)",
                            "Industry knowledge (15 points)",
                            "Personal reflection (10 points)"
                        ]
                    },
                    {
                        question: "Evaluate the impact of Clinical Engineering on patient safety and healthcare quality.",
                        modelAnswer: "Clinical Engineering significantly impacts patient safety through systematic equipment management, preventive maintenance programs, incident investigation, and technology assessment. Quality improvements include standardization of equipment, staff training programs, performance monitoring, and evidence-based technology adoption. Clinical engineers reduce medical errors, improve diagnostic accuracy, enhance treatment effectiveness, and optimize resource utilization.",
                        keyPoints: [
                            "Equipment safety and reliability",
                            "Preventive maintenance programs",
                            "Incident investigation and analysis",
                            "Staff training and competency",
                            "Technology standardization",
                            "Performance monitoring and optimization"
                        ],
                        assessmentCriteria: [
                            "Understanding of safety impact (25 points)",
                            "Quality improvement examples (25 points)",
                            "Evidence-based reasoning (20 points)",
                            "Systematic approach (20 points)",
                            "Critical analysis (10 points)"
                        ]
                    }
                ]
            },
            2: {
                title: "Lecture 2: Healthcare Technology Management",
                activities: [
                    {
                        question: "Develop a comprehensive Healthcare Technology Management (HTM) framework for a 300-bed hospital.",
                        modelAnswer: "A comprehensive HTM framework should include: 1) Strategic planning aligned with hospital goals and clinical needs, 2) Technology assessment and acquisition processes with evidence-based evaluation, 3) Lifecycle management from procurement to disposal with sustainability considerations, 4) Maintenance and support programs with predictive analytics, 5) Performance monitoring and optimization using KPIs, 6) Staff training and competency management programs, 7) Regulatory compliance and quality assurance systems, 8) Financial management and budgeting with ROI analysis, 9) Risk management and patient safety protocols, 10) Innovation and emerging technology evaluation.",
                        keyPoints: [
                            "Strategic alignment with organizational goals",
                            "Comprehensive lifecycle approach",
                            "Risk-based decision making",
                            "Performance measurement systems",
                            "Stakeholder engagement processes",
                            "Financial sustainability",
                            "Regulatory compliance integration"
                        ],
                        assessmentCriteria: [
                            "Framework completeness (25 points)",
                            "Strategic alignment (20 points)",
                            "Practical implementation (25 points)",
                            "Risk consideration (15 points)",
                            "Innovation and creativity (15 points)"
                        ]
                    },
                    {
                        question: "Analyze the role of technology assessment in healthcare decision-making.",
                        modelAnswer: "Technology assessment in healthcare involves systematic evaluation of medical technologies' clinical effectiveness, safety, cost-effectiveness, and organizational impact. It includes health technology assessment (HTA) methodologies, evidence-based medicine principles, economic evaluation techniques, and stakeholder analysis. The process supports informed decision-making for technology adoption, resource allocation, and policy development.",
                        keyPoints: [
                            "Systematic evaluation methodology",
                            "Clinical effectiveness assessment",
                            "Economic evaluation and cost-effectiveness",
                            "Safety and risk assessment",
                            "Organizational impact analysis",
                            "Evidence-based decision making"
                        ],
                        assessmentCriteria: [
                            "Understanding of HTA principles (25 points)",
                            "Evaluation methodology (25 points)",
                            "Economic analysis (20 points)",
                            "Decision-making integration (20 points)",
                            "Critical thinking (10 points)"
                        ]
                    }
                ]
            },
            3: {
                title: "Lecture 3: Regulatory Standards & Compliance",
                activities: [
                    {
                        question: "Compare and contrast FDA and ISO regulatory approaches for medical devices.",
                        modelAnswer: "FDA focuses on market access and post-market surveillance in the US, emphasizing premarket approval processes (510(k), PMA) and quality system regulations (QSR). ISO provides international standards for quality management (ISO 13485), risk management (ISO 14971), and usability engineering (IEC 62366). Key differences include: FDA is regulatory authority vs ISO as standards organization, FDA has legal enforcement power vs ISO provides voluntary standards, FDA focuses on US market vs ISO provides global framework. Both emphasize patient safety, quality systems, and risk management but through different mechanisms.",
                        keyPoints: [
                            "FDA: Regulatory authority with enforcement power",
                            "ISO: International standards organization",
                            "Different approval pathways and requirements",
                            "Complementary rather than competing approaches",
                            "Global harmonization efforts",
                            "Risk-based regulatory frameworks"
                        ],
                        assessmentCriteria: [
                            "Accurate comparison (25 points)",
                            "Understanding of differences (25 points)",
                            "Practical implications (20 points)",
                            "Global perspective (15 points)",
                            "Critical analysis (15 points)"
                        ]
                    },
                    {
                        question: "Develop a compliance strategy for a medical device manufacturer entering international markets.",
                        modelAnswer: "A comprehensive compliance strategy should include: 1) Market analysis and regulatory mapping for target countries, 2) Quality management system implementation (ISO 13485), 3) Risk management processes (ISO 14971), 4) Clinical evaluation and post-market surveillance plans, 5) Regulatory submission strategies for each market, 6) Harmonized standards adoption, 7) Authorized representative appointments, 8) Documentation and record-keeping systems, 9) Staff training and competency programs, 10) Continuous monitoring and improvement processes.",
                        keyPoints: [
                            "Multi-market regulatory strategy",
                            "Quality management system foundation",
                            "Risk-based approach",
                            "Clinical evidence requirements",
                            "Post-market surveillance",
                            "International harmonization"
                        ],
                        assessmentCriteria: [
                            "Strategy comprehensiveness (30 points)",
                            "Regulatory understanding (25 points)",
                            "Implementation feasibility (20 points)",
                            "Risk management (15 points)",
                            "Global perspective (10 points)"
                        ]
                    }
                ]
            },
            3: {
                title: "Lecture 3: Regulatory Standards & Compliance",
                activities: [
                    {
                        question: "Compare and contrast FDA and ISO regulatory approaches for medical devices.",
                        modelAnswer: "FDA focuses on market access and post-market surveillance in the US, emphasizing premarket approval processes (510(k), PMA) and quality system regulations (QSR). ISO provides international standards for quality management (ISO 13485), risk management (ISO 14971), and usability engineering (IEC 62366). Key differences include: FDA is regulatory authority vs ISO as standards organization, FDA has legal enforcement power vs ISO provides voluntary standards, FDA focuses on US market vs ISO provides global framework.",
                        keyPoints: [
                            "FDA: Regulatory authority with enforcement power",
                            "ISO: International standards organization",
                            "Different approval pathways and requirements",
                            "Complementary rather than competing approaches",
                            "Global harmonization efforts"
                        ],
                        assessmentCriteria: [
                            "Accurate comparison (25 points)",
                            "Understanding of differences (25 points)",
                            "Practical implications (20 points)",
                            "Global perspective (15 points)",
                            "Critical analysis (15 points)"
                        ]
                    }
                ]
            },
            4: {
                title: "Lecture 4: Equipment Management & Maintenance",
                activities: [
                    {
                        question: "Design a comprehensive preventive maintenance program for critical care equipment.",
                        modelAnswer: "A comprehensive PM program should include: 1) Risk-based equipment classification (critical, semi-critical, non-critical), 2) Manufacturer recommendations and regulatory requirements integration, 3) Evidence-based maintenance intervals, 4) Standardized procedures and checklists, 5) Competency-based technician assignments, 6) Performance monitoring and trending, 7) Predictive maintenance technologies, 8) Documentation and record-keeping systems, 9) Quality assurance and continuous improvement, 10) Cost-effectiveness analysis and optimization.",
                        keyPoints: [
                            "Risk-based equipment classification",
                            "Evidence-based maintenance intervals",
                            "Standardized procedures",
                            "Performance monitoring",
                            "Predictive maintenance integration",
                            "Continuous improvement processes"
                        ],
                        assessmentCriteria: [
                            "Program comprehensiveness (25 points)",
                            "Risk-based approach (25 points)",
                            "Implementation strategy (20 points)",
                            "Quality assurance (15 points)",
                            "Cost-effectiveness (15 points)"
                        ]
                    },
                    {
                        question: "Evaluate the implementation of a Computerized Maintenance Management System (CMMS).",
                        modelAnswer: "CMMS implementation involves: 1) Needs assessment and system selection, 2) Equipment inventory and data migration, 3) Workflow design and process standardization, 4) User training and change management, 5) Integration with existing systems, 6) Performance metrics and KPI development, 7) Continuous monitoring and optimization. Benefits include improved scheduling, better documentation, enhanced reporting, cost tracking, and regulatory compliance support.",
                        keyPoints: [
                            "Systematic implementation approach",
                            "Data management and migration",
                            "Workflow optimization",
                            "User adoption strategies",
                            "Performance measurement",
                            "ROI demonstration"
                        ],
                        assessmentCriteria: [
                            "Implementation planning (25 points)",
                            "Technical understanding (20 points)",
                            "Change management (25 points)",
                            "Benefits realization (20 points)",
                            "Critical analysis (10 points)"
                        ]
                    }
                ]
            },
            5: {
                title: "Lecture 5: Safety & Risk Management",
                activities: [
                    {
                        question: "Conduct a comprehensive risk assessment for a new medical device implementation.",
                        modelAnswer: "Risk assessment should follow ISO 14971 methodology: 1) Risk analysis including hazard identification, risk estimation, and risk evaluation, 2) Risk control measures implementation, 3) Residual risk evaluation, 4) Risk/benefit analysis, 5) Risk management file documentation. Consider clinical risks, use-related risks, technical risks, and organizational risks. Implement risk controls through inherent safety design, protective measures, and information for safety.",
                        keyPoints: [
                            "ISO 14971 methodology application",
                            "Comprehensive hazard identification",
                            "Risk estimation and evaluation",
                            "Risk control hierarchy",
                            "Residual risk assessment",
                            "Documentation requirements"
                        ],
                        assessmentCriteria: [
                            "Methodology application (25 points)",
                            "Risk identification completeness (25 points)",
                            "Control measures appropriateness (20 points)",
                            "Documentation quality (15 points)",
                            "Critical thinking (15 points)"
                        ]
                    },
                    {
                        question: "Develop an incident investigation and reporting system for medical device-related events.",
                        modelAnswer: "The system should include: 1) Incident detection and reporting mechanisms, 2) Systematic investigation procedures, 3) Root cause analysis methodologies, 4) Corrective and preventive action (CAPA) processes, 5) Regulatory reporting requirements, 6) Trend analysis and risk assessment, 7) Communication and feedback systems, 8) Continuous improvement integration. Follow FDA MDR requirements and international guidelines for adverse event reporting.",
                        keyPoints: [
                            "Systematic investigation procedures",
                            "Root cause analysis methods",
                            "CAPA implementation",
                            "Regulatory compliance",
                            "Trend analysis capabilities",
                            "Continuous improvement"
                        ],
                        assessmentCriteria: [
                            "System comprehensiveness (25 points)",
                            "Investigation methodology (25 points)",
                            "Regulatory compliance (20 points)",
                            "Improvement integration (20 points)",
                            "Practical implementation (10 points)"
                        ]
                    }
                ]
            },
            6: {
                title: "Lecture 6: Financial Management & Procurement",
                activities: [
                    {
                        question: "Develop a capital equipment acquisition strategy with total cost of ownership analysis.",
                        modelAnswer: "Capital acquisition strategy should include: 1) Needs assessment and clinical requirements definition, 2) Market analysis and vendor evaluation, 3) Total cost of ownership (TCO) analysis including acquisition, operation, maintenance, and disposal costs, 4) Financial modeling and budget planning, 5) Risk assessment and mitigation strategies, 6) Implementation timeline and resource planning, 7) Performance metrics and success criteria, 8) Post-implementation evaluation and lessons learned.",
                        keyPoints: [
                            "Comprehensive needs assessment",
                            "TCO analysis methodology",
                            "Vendor evaluation criteria",
                            "Financial modeling",
                            "Risk mitigation strategies",
                            "Performance measurement"
                        ],
                        assessmentCriteria: [
                            "Strategy comprehensiveness (25 points)",
                            "Financial analysis accuracy (25 points)",
                            "Risk assessment quality (20 points)",
                            "Implementation feasibility (20 points)",
                            "Value demonstration (10 points)"
                        ]
                    },
                    {
                        question: "Analyze the economic impact of clinical engineering services on healthcare organizations.",
                        modelAnswer: "Economic impact analysis should consider: 1) Cost avoidance through preventive maintenance and equipment optimization, 2) Revenue enhancement through improved equipment availability and performance, 3) Risk mitigation value through safety programs and compliance, 4) Efficiency gains through standardization and process improvement, 5) Innovation value through technology assessment and adoption, 6) Quality improvements and patient outcome benefits. Use metrics like ROI, cost-benefit ratios, and value-based assessments.",
                        keyPoints: [
                            "Cost avoidance quantification",
                            "Revenue impact assessment",
                            "Risk mitigation value",
                            "Efficiency improvements",
                            "Quality and outcome benefits",
                            "ROI calculation methods"
                        ],
                        assessmentCriteria: [
                            "Economic analysis depth (25 points)",
                            "Metric selection appropriateness (20 points)",
                            "Value demonstration (25 points)",
                            "Data quality and sources (15 points)",
                            "Strategic implications (15 points)"
                        ]
                    }
                ]
            },
            7: {
                title: "Lecture 7: Testing & Calibration",
                activities: [
                    {
                        question: "Design a comprehensive calibration program for biomedical test equipment.",
                        modelAnswer: "A comprehensive calibration program should include: 1) Equipment inventory and classification, 2) Calibration interval determination based on risk, usage, and stability, 3) Traceability to national/international standards, 4) Calibration procedures and acceptance criteria, 5) Uncertainty analysis and measurement capability, 6) Documentation and record-keeping systems, 7) Non-conformance handling and corrective actions, 8) Competency requirements and training, 9) Quality assurance and proficiency testing, 10) Continuous improvement and optimization.",
                        keyPoints: [
                            "Risk-based calibration intervals",
                            "Traceability requirements",
                            "Uncertainty analysis",
                            "Documentation systems",
                            "Quality assurance processes",
                            "Continuous improvement"
                        ],
                        assessmentCriteria: [
                            "Program comprehensiveness (25 points)",
                            "Technical accuracy (25 points)",
                            "Regulatory compliance (20 points)",
                            "Implementation feasibility (20 points)",
                            "Quality assurance (10 points)"
                        ]
                    },
                    {
                        question: "Perform measurement uncertainty analysis using GUM methodology.",
                        modelAnswer: "GUM uncertainty analysis involves: 1) Measurement model definition, 2) Input quantity identification and characterization, 3) Type A uncertainty evaluation (statistical analysis), 4) Type B uncertainty evaluation (other methods), 5) Combined standard uncertainty calculation, 6) Effective degrees of freedom determination, 7) Coverage factor selection, 8) Expanded uncertainty calculation, 9) Uncertainty budget documentation, 10) Results interpretation and reporting.",
                        keyPoints: [
                            "GUM methodology application",
                            "Type A and B uncertainty evaluation",
                            "Combined uncertainty calculation",
                            "Coverage factor determination",
                            "Uncertainty budget development",
                            "Results interpretation"
                        ],
                        assessmentCriteria: [
                            "Methodology understanding (25 points)",
                            "Calculation accuracy (25 points)",
                            "Documentation quality (20 points)",
                            "Interpretation correctness (20 points)",
                            "Professional presentation (10 points)"
                        ]
                    }
                ]
            },
            8: {
                title: "Lecture 8: Manufacturing & Product Lifecycle",
                activities: [
                    {
                        question: "Develop a design control process for medical device development.",
                        modelAnswer: "Design controls should follow FDA QSR and ISO 13485 requirements: 1) Design planning with procedures and responsibilities, 2) Design inputs including user needs and regulatory requirements, 3) Design outputs with specifications and drawings, 4) Design review at appropriate stages, 5) Design verification to confirm outputs meet inputs, 6) Design validation to ensure user needs are met, 7) Design transfer to production, 8) Design changes control throughout lifecycle, 9) Design history file maintenance, 10) Risk management integration throughout process.",
                        keyPoints: [
                            "Regulatory compliance (FDA QSR, ISO 13485)",
                            "Systematic design process",
                            "Verification and validation",
                            "Risk management integration",
                            "Documentation requirements",
                            "Change control processes"
                        ],
                        assessmentCriteria: [
                            "Regulatory understanding (25 points)",
                            "Process comprehensiveness (25 points)",
                            "Implementation strategy (20 points)",
                            "Risk integration (15 points)",
                            "Documentation quality (15 points)"
                        ]
                    }
                ]
            },
            9: {
                title: "Lecture 9: Troubleshooting & Problem Solving",
                activities: [
                    {
                        question: "Develop a systematic troubleshooting methodology for complex medical equipment failures.",
                        modelAnswer: "Systematic troubleshooting methodology should include: 1) Problem definition and symptom documentation, 2) Information gathering and history review, 3) Hypothesis formation based on failure modes, 4) Testing and verification procedures, 5) Root cause analysis using appropriate tools, 6) Solution implementation and verification, 7) Documentation and knowledge sharing, 8) Preventive measures development, 9) Follow-up and monitoring, 10) Continuous improvement integration.",
                        keyPoints: [
                            "Systematic problem-solving approach",
                            "Root cause analysis methods",
                            "Testing and verification procedures",
                            "Documentation requirements",
                            "Preventive measures",
                            "Knowledge management"
                        ],
                        assessmentCriteria: [
                            "Methodology completeness (25 points)",
                            "Technical accuracy (25 points)",
                            "Problem-solving skills (20 points)",
                            "Documentation quality (15 points)",
                            "Improvement integration (15 points)"
                        ]
                    }
                ]
            },
            10: {
                title: "Lecture 10: Emerging Technologies",
                activities: [
                    {
                        question: "Evaluate the impact of artificial intelligence and machine learning on clinical engineering practice.",
                        modelAnswer: "AI/ML impact on clinical engineering includes: 1) Predictive maintenance using sensor data and algorithms, 2) Automated quality assurance and testing, 3) Enhanced diagnostic capabilities and decision support, 4) Intelligent inventory and resource management, 5) Risk prediction and patient safety improvements, 6) Workflow optimization and efficiency gains, 7) Data analytics and performance insights, 8) Regulatory challenges and validation requirements, 9) Training and competency needs, 10) Ethical considerations and implementation challenges.",
                        keyPoints: [
                            "Predictive maintenance applications",
                            "Quality assurance automation",
                            "Decision support systems",
                            "Risk prediction capabilities",
                            "Regulatory considerations",
                            "Implementation challenges"
                        ],
                        assessmentCriteria: [
                            "Technology understanding (25 points)",
                            "Application identification (25 points)",
                            "Impact assessment (20 points)",
                            "Challenge recognition (15 points)",
                            "Future vision (15 points)"
                        ]
                    }
                ]
            },
            11: {
                title: "Lecture 11: Professional Development",
                activities: [
                    {
                        question: "Create a professional development plan for clinical engineering career advancement.",
                        modelAnswer: "Professional development plan should include: 1) Self-assessment of current skills and competencies, 2) Career goal definition and pathway planning, 3) Education and certification requirements, 4) Professional organization involvement, 5) Networking and mentorship opportunities, 6) Continuing education and skill development, 7) Leadership and management training, 8) Research and publication activities, 9) Industry engagement and collaboration, 10) Performance measurement and plan updates.",
                        keyPoints: [
                            "Self-assessment and goal setting",
                            "Education and certification planning",
                            "Professional networking",
                            "Leadership development",
                            "Continuous learning",
                            "Performance measurement"
                        ],
                        assessmentCriteria: [
                            "Plan comprehensiveness (25 points)",
                            "Goal clarity and realism (25 points)",
                            "Development strategy (20 points)",
                            "Implementation feasibility (15 points)",
                            "Personal reflection (15 points)"
                        ]
                    }
                ]
            },
            12: {
                title: "Lecture 12: Future of Clinical Engineering",
                activities: [
                    {
                        question: "Analyze future trends and challenges in clinical engineering and propose strategic responses.",
                        modelAnswer: "Future trends include: 1) Digital health and telemedicine expansion, 2) AI/ML integration in healthcare technology, 3) Cybersecurity and data protection requirements, 4) Personalized medicine and precision healthcare, 5) Sustainability and environmental considerations, 6) Regulatory evolution and harmonization, 7) Workforce development and skill requirements, 8) Value-based healthcare and outcome measurement, 9) Global health technology access, 10) Innovation and entrepreneurship opportunities. Strategic responses require adaptive planning, continuous learning, and proactive engagement.",
                        keyPoints: [
                            "Digital transformation impact",
                            "Technology convergence",
                            "Regulatory evolution",
                            "Sustainability requirements",
                            "Workforce development needs",
                            "Strategic adaptation"
                        ],
                        assessmentCriteria: [
                            "Trend identification (25 points)",
                            "Impact analysis (25 points)",
                            "Strategic thinking (20 points)",
                            "Innovation consideration (15 points)",
                            "Future vision (15 points)"
                        ]
                    }
                ]
            }
        };

        // Show model answers for selected lecture
        function showLectureAnswers(lectureNumber) {
            const answersContent = document.getElementById('answersContent');
            const answersTitle = document.getElementById('answersTitle');
            const answersBody = document.getElementById('answersBody');

            // Update active lecture card
            document.querySelectorAll('.lecture-card').forEach(card => {
                card.classList.remove('active');
            });
            event.target.closest('.lecture-card').classList.add('active');

            // Get lecture data
            const lectureData = modelAnswers[lectureNumber];
            if (!lectureData) {
                answersBody.innerHTML = `
                    <div class="model-answer">
                        <h3>⚠️ Content Under Development</h3>
                        <p>Model answers for Lecture ${lectureNumber} are currently being prepared. Please check back soon for comprehensive solutions and assessment criteria.</p>
                    </div>
                `;
            } else {
                // Generate content
                let content = `<h3>${lectureData.title}</h3>`;

                lectureData.activities.forEach((activity, index) => {
                    content += `
                        <div class="answer-section">
                            <h4>Activity ${index + 1}</h4>
                            <p><strong>Question:</strong> ${activity.question}</p>

                            <button class="answer-toggle" onclick="toggleAnswer('answer-${lectureNumber}-${index}')">
                                Show Model Answer
                            </button>

                            <div id="answer-${lectureNumber}-${index}" class="hidden">
                                <div class="model-answer">
                                    <h5>📝 Model Answer:</h5>
                                    <p>${activity.modelAnswer}</p>
                                </div>

                                <div class="key-points">
                                    <h5>🎯 Key Points to Include:</h5>
                                    <ul>
                                        ${activity.keyPoints.map(point => `<li>${point}</li>`).join('')}
                                    </ul>
                                </div>

                                <div class="assessment-criteria">
                                    <h5>📊 Assessment Criteria:</h5>
                                    <ul>
                                        ${activity.assessmentCriteria.map(criteria => `<li>${criteria}</li>`).join('')}
                                    </ul>
                                </div>
                            </div>
                        </div>
                    `;
                });
            }

            answersTitle.textContent = `Model Answers - Lecture ${lectureNumber}`;
            answersBody.innerHTML = content;
            answersContent.style.display = 'block';
            answersContent.scrollIntoView({ behavior: 'smooth' });
        }

        // Toggle individual answer visibility
        function toggleAnswer(answerId) {
            const answerDiv = document.getElementById(answerId);
            const button = answerDiv.previousElementSibling;

            if (answerDiv.classList.contains('hidden')) {
                answerDiv.classList.remove('hidden');
                button.textContent = 'Hide Model Answer';
            } else {
                answerDiv.classList.add('hidden');
                button.textContent = 'Show Model Answer';
            }
        }

        // Hide answers section
        function hideAnswers() {
            document.getElementById('answersContent').style.display = 'none';
            document.querySelectorAll('.lecture-card').forEach(card => {
                card.classList.remove('active');
            });
        }

        // Filter lectures by category
        function filterLectures(category) {
            const cards = document.querySelectorAll('.lecture-card');
            const buttons = document.querySelectorAll('.filter-btn');

            // Update active filter button
            buttons.forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');

            // Show/hide cards based on category
            cards.forEach(card => {
                if (category === 'all' || card.classList.contains(category)) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });
        }

        // Search functionality
        function setupSearch() {
            const searchInput = document.getElementById('searchInput');
            searchInput.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();
                const cards = document.querySelectorAll('.lecture-card');

                cards.forEach(card => {
                    const text = card.textContent.toLowerCase();
                    if (text.includes(searchTerm)) {
                        card.style.display = 'block';
                    } else {
                        card.style.display = 'none';
                    }
                });
            });
        }

        // URL parameter handling
        function getURLParameter(name) {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get(name);
        }

        // Auto-load lecture from URL parameter
        function handleURLParameters() {
            const lectureParam = getURLParameter('lecture');
            const activityParam = getURLParameter('activity');
            const searchParam = getURLParameter('search');

            if (lectureParam) {
                const lectureNumber = parseInt(lectureParam);
                if (lectureNumber >= 1 && lectureNumber <= 12) {
                    // Simulate click on lecture card
                    setTimeout(() => {
                        showLectureAnswers(lectureNumber);

                        // If specific activity requested, scroll to it
                        if (activityParam) {
                            const activityIndex = parseInt(activityParam) - 1;
                            setTimeout(() => {
                                const activityElement = document.querySelector(`#answer-${lectureNumber}-${activityIndex}`);
                                if (activityElement) {
                                    // Auto-expand the answer
                                    toggleAnswer(`answer-${lectureNumber}-${activityIndex}`);
                                    // Scroll to activity
                                    activityElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
                                }
                            }, 1000);
                        }
                    }, 500);
                }
            }

            // Handle search parameter
            if (searchParam) {
                setTimeout(() => {
                    document.getElementById('searchInput').focus();
                }, 500);
            }
        }

        // Enhanced navigation with breadcrumbs
        function addBreadcrumbs() {
            const breadcrumbContainer = document.createElement('div');
            breadcrumbContainer.style.cssText = `
                background: #f8f9fa;
                padding: 10px 20px;
                border-bottom: 1px solid #dee2e6;
                font-size: 0.9em;
            `;
            breadcrumbContainer.innerHTML = `
                <a href="index.html" style="color: #0056b3; text-decoration: none;">🏠 Home</a>
                <span style="margin: 0 10px; color: #6c757d;">></span>
                <span style="color: #495057;">📝 Model Answers</span>
            `;

            const header = document.querySelector('header');
            header.insertAdjacentElement('afterend', breadcrumbContainer);
        }

        // Activity statistics and index generation
        function updateActivityStats() {
            let totalActivities = 0;
            let totalCaseStudies = 0;

            Object.values(modelAnswers).forEach(lecture => {
                totalActivities += lecture.activities.length;
                // Estimate case studies (some activities are case studies)
                totalCaseStudies += Math.floor(lecture.activities.length * 0.4);
            });

            // Update statistics counters
            const totalActivitiesElement = document.getElementById('totalActivitiesCount');
            const totalCaseStudiesElement = document.getElementById('totalCaseStudiesCount');
            if (totalActivitiesElement) totalActivitiesElement.textContent = totalActivities;
            if (totalCaseStudiesElement) totalCaseStudiesElement.textContent = totalCaseStudies;
        }

        // Generate comprehensive activity index
        function generateActivityIndex() {
            const indexContainer = document.getElementById('activityIndex');
            if (!indexContainer) return;

            let indexHTML = '';

            Object.entries(modelAnswers).forEach(([lectureNum, lectureData]) => {
                const categoryColors = {
                    '1': '#0056b3', '2': '#0056b3', '3': '#0056b3',
                    '4': '#28a745', '5': '#28a745', '6': '#28a745',
                    '7': '#ffc107', '8': '#ffc107', '9': '#ffc107',
                    '10': '#dc3545', '11': '#dc3545', '12': '#dc3545'
                };

                const categoryNames = {
                    '1': 'Fundamentals', '2': 'Fundamentals', '3': 'Fundamentals',
                    '4': 'Management', '5': 'Management', '6': 'Management',
                    '7': 'Advanced', '8': 'Advanced', '9': 'Advanced',
                    '10': 'Specialized', '11': 'Specialized', '12': 'Specialized'
                };

                indexHTML += `
                    <div style="background: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); border-left: 4px solid ${categoryColors[lectureNum]};">
                        <div style="padding: 15px; border-bottom: 1px solid #f0f0f0;">
                            <h4 style="margin: 0; color: ${categoryColors[lectureNum]};">
                                📚 Lecture ${lectureNum}
                                <span style="font-size: 0.8em; color: #6c757d; font-weight: normal;">(${categoryNames[lectureNum]})</span>
                            </h4>
                            <p style="margin: 5px 0 0 0; font-size: 0.9em; color: #495057;">${lectureData.title.split(': ')[1]}</p>
                        </div>
                        <div style="padding: 15px;">
                `;

                lectureData.activities.forEach((activity, activityIndex) => {
                    const shortQuestion = activity.question.length > 60 ?
                        activity.question.substring(0, 60) + '...' :
                        activity.question;

                    indexHTML += `
                        <div style="margin-bottom: 10px; padding: 10px; background: #f8f9fa; border-radius: 4px;">
                            <div style="font-weight: 600; color: #495057; margin-bottom: 5px;">
                                Activity ${activityIndex + 1}
                            </div>
                            <div style="font-size: 0.9em; color: #6c757d; margin-bottom: 8px;">
                                ${shortQuestion}
                            </div>
                            <div style="display: flex; gap: 8px; flex-wrap: wrap;">
                                <a href="?lecture=${lectureNum}&activity=${activityIndex + 1}"
                                   style="background: ${categoryColors[lectureNum]}; color: white; padding: 4px 8px; border-radius: 3px; text-decoration: none; font-size: 0.8em;">
                                    View Answer
                                </a>
                                <span style="background: #e9ecef; color: #495057; padding: 4px 8px; border-radius: 3px; font-size: 0.8em;">
                                    ${activity.keyPoints.length} Key Points
                                </span>
                                <span style="background: #e9ecef; color: #495057; padding: 4px 8px; border-radius: 3px; font-size: 0.8em;">
                                    ${activity.assessmentCriteria.length} Criteria
                                </span>
                            </div>
                        </div>
                    `;
                });

                indexHTML += `
                        </div>
                    </div>
                `;
            });

            indexContainer.innerHTML = indexHTML;
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            setupSearch();
            addBreadcrumbs();
            updateActivityStats();
            generateActivityIndex();
            handleURLParameters();

            // Add welcome message
            setTimeout(() => {
                if (!localStorage.getItem('modelAnswersVisited')) {
                    alert('Welcome to the Model Answers section! Click on any lecture card to view comprehensive solutions and assessment criteria for all activities. You can also use direct links from the main page to jump to specific lectures and activities.');
                    localStorage.setItem('modelAnswersVisited', 'true');
                }
            }, 1000);
        });
    </script>
</body>
</html>
