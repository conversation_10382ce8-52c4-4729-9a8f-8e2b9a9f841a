<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Model Answers: Student Activities (Lectures 1-12)</title>
    <style>
        /* Base Styles */
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
            background-color: #f5f7fa;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        /* Header Styles */
        header {
            background: linear-gradient(135deg, #0056b3 0%, #00a0e9 100%);
            color: white;
            padding: 40px 0;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .header-content {
            max-width: 800px;
            margin: 0 auto;
        }
        
        h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        
        h2 {
            font-size: 1.8rem;
            margin-top: 30px;
            color: #0056b3;
            border-bottom: 2px solid #e1e4e8;
            padding-bottom: 10px;
        }
        
        h3 {
            font-size: 1.4rem;
            color: #0056b3;
            margin-top: 25px;
        }
        
        /* Navigation Bar */
        .nav-bar {
            background-color: #343a40;
            padding: 15px 0;
            position: sticky;
            top: 0;
            z-index: 1000;
        }
        
        .nav-content {
            max-width: 1400px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
        }
        
        .nav-links {
            display: flex;
            gap: 30px;
        }
        
        .nav-links a {
            color: white;
            text-decoration: none;
            padding: 10px 15px;
            border-radius: 4px;
            transition: background-color 0.3s;
        }
        
        .nav-links a:hover, .nav-links a.active {
            background-color: #0056b3;
        }
        
        /* Content Sections */
        .section {
            background-color: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            padding: 30px;
            margin-bottom: 30px;
        }
        
        /* Lecture Cards */
        .lecture-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin: 30px 0;
        }
        
        .lecture-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 2px solid #0056b3;
            border-radius: 12px;
            padding: 25px;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .lecture-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 86, 179, 0.15);
        }
        
        .lecture-card.active {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border-color: #1976d2;
        }
        
        /* Model Answer Styles */
        .model-answer {
            background-color: #e8f5e9;
            border-left: 4px solid #4caf50;
            padding: 20px;
            margin: 20px 0;
            border-radius: 0 8px 8px 0;
        }
        
        .answer-section {
            background-color: #f0f9e8;
            border: 1px solid #c8e6c9;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
        }
        
        .key-points {
            background-color: #fff3e0;
            border-left: 4px solid #ff9800;
            padding: 15px;
            margin: 15px 0;
            border-radius: 0 6px 6px 0;
        }
        
        .assessment-criteria {
            background-color: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 15px;
            margin: 15px 0;
            border-radius: 0 6px 6px 0;
        }
        
        /* Interactive Elements */
        .answer-toggle {
            background-color: #0056b3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin: 10px 0;
            transition: all 0.3s ease;
        }
        
        .answer-toggle:hover {
            background-color: #003d82;
            transform: translateY(-2px);
        }
        
        .hidden {
            display: none;
        }
        
        /* Search and Filter */
        .search-filter {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .search-input {
            width: 100%;
            padding: 12px;
            border: 2px solid #dee2e6;
            border-radius: 6px;
            font-size: 16px;
            margin-bottom: 15px;
        }
        
        .filter-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .filter-btn {
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .filter-btn.active {
            background: #0056b3;
            color: white;
            border-color: #0056b3;
        }
        
        /* Footer */
        footer {
            background-color: #343a40;
            color: white;
            text-align: center;
            padding: 20px 0;
            margin-top: 40px;
        }
        
        /* Responsive Design */
        @media (max-width: 768px) {
            .lecture-grid {
                grid-template-columns: 1fr;
            }
            
            .nav-links {
                flex-direction: column;
                gap: 10px;
            }
            
            .filter-buttons {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="header-content">
            <h1>Model Answers: Student Activities</h1>
            <p>Comprehensive solutions and assessment criteria for all lecture activities (Lectures 1-12)</p>
        </div>
    </header>
    
    <nav class="nav-bar">
        <div class="nav-content">
            <div class="nav-links">
                <a href="index.html">🏠 Home</a>
                <a href="#" class="active">📝 Model Answers</a>
                <a href="clinical_engineering_simulation.html">🚀 Simulations</a>
                <a href="hands_on_learning_medical_devices.html">🎓 Hands-on Learning</a>
            </div>
            <div style="color: white; font-size: 0.9em;">
                📚 Complete Solutions Guide
            </div>
        </div>
    </nav>
    
    <div class="container">
        <!-- Search and Filter Section -->
        <div class="search-filter">
            <h3 style="margin-top: 0; color: #0056b3;">🔍 Find Specific Answers</h3>
            <input type="text" class="search-input" id="searchInput" placeholder="Search for specific topics, activities, or keywords...">
            
            <div class="filter-buttons">
                <button class="filter-btn active" onclick="filterLectures('all')">All Lectures</button>
                <button class="filter-btn" onclick="filterLectures('fundamentals')">Fundamentals (1-3)</button>
                <button class="filter-btn" onclick="filterLectures('management')">Management (4-6)</button>
                <button class="filter-btn" onclick="filterLectures('advanced')">Advanced (7-9)</button>
                <button class="filter-btn" onclick="filterLectures('specialized')">Specialized (10-12)</button>
            </div>
        </div>
        
        <!-- Overview Section -->
        <div class="section">
            <h2>📋 Overview and Usage Guide</h2>
            <p>This comprehensive model answers guide provides detailed solutions, assessment criteria, and learning objectives for all student activities across the 12-lecture Clinical Engineering course.</p>
            
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin: 20px 0;">
                <div class="key-points">
                    <h4>🎯 Purpose</h4>
                    <ul>
                        <li>Provide comprehensive model answers</li>
                        <li>Establish assessment criteria</li>
                        <li>Guide student self-evaluation</li>
                        <li>Support instructor grading</li>
                    </ul>
                </div>
                <div class="assessment-criteria">
                    <h4>📊 Assessment Framework</h4>
                    <ul>
                        <li>Knowledge comprehension (25%)</li>
                        <li>Application skills (35%)</li>
                        <li>Critical thinking (25%)</li>
                        <li>Professional communication (15%)</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- Lecture Selection Grid -->
        <div class="section">
            <h2>📚 Select Lecture for Model Answers</h2>
            <div class="lecture-grid" id="lectureGrid">
                <!-- Lectures 1-3: Fundamentals -->
                <div class="lecture-card fundamentals" onclick="showLectureAnswers(1)">
                    <h3>📖 Lecture 1: Introduction to Clinical Engineering</h3>
                    <p><strong>Activities:</strong> Role definition, scope analysis, career pathways</p>
                    <p><strong>Key Topics:</strong> Healthcare technology, professional responsibilities, regulatory environment</p>
                    <div style="margin-top: 15px;">
                        <span style="background: #e3f2fd; padding: 4px 8px; border-radius: 12px; font-size: 0.8em;">5 Activities</span>
                        <span style="background: #f3e5f5; padding: 4px 8px; border-radius: 12px; font-size: 0.8em;">3 Case Studies</span>
                    </div>
                </div>
                
                <div class="lecture-card fundamentals" onclick="showLectureAnswers(2)">
                    <h3>🏥 Lecture 2: Healthcare Technology Management</h3>
                    <p><strong>Activities:</strong> Technology assessment, lifecycle planning, cost analysis</p>
                    <p><strong>Key Topics:</strong> HTM frameworks, strategic planning, resource allocation</p>
                    <div style="margin-top: 15px;">
                        <span style="background: #e3f2fd; padding: 4px 8px; border-radius: 12px; font-size: 0.8em;">6 Activities</span>
                        <span style="background: #f3e5f5; padding: 4px 8px; border-radius: 12px; font-size: 0.8em;">2 Case Studies</span>
                    </div>
                </div>
                
                <div class="lecture-card fundamentals" onclick="showLectureAnswers(3)">
                    <h3>⚖️ Lecture 3: Regulatory Standards & Compliance</h3>
                    <p><strong>Activities:</strong> Standards analysis, compliance auditing, documentation</p>
                    <p><strong>Key Topics:</strong> FDA regulations, ISO standards, quality systems</p>
                    <div style="margin-top: 15px;">
                        <span style="background: #e3f2fd; padding: 4px 8px; border-radius: 12px; font-size: 0.8em;">7 Activities</span>
                        <span style="background: #f3e5f5; padding: 4px 8px; border-radius: 12px; font-size: 0.8em;">4 Case Studies</span>
                    </div>
                </div>
                
                <!-- Lectures 4-6: Management -->
                <div class="lecture-card management" onclick="showLectureAnswers(4)">
                    <h3>🔧 Lecture 4: Equipment Management & Maintenance</h3>
                    <p><strong>Activities:</strong> PM scheduling, inventory management, performance tracking</p>
                    <p><strong>Key Topics:</strong> Maintenance strategies, CMMS, cost optimization</p>
                    <div style="margin-top: 15px;">
                        <span style="background: #e3f2fd; padding: 4px 8px; border-radius: 12px; font-size: 0.8em;">8 Activities</span>
                        <span style="background: #f3e5f5; padding: 4px 8px; border-radius: 12px; font-size: 0.8em;">3 Case Studies</span>
                    </div>
                </div>
                
                <div class="lecture-card management" onclick="showLectureAnswers(5)">
                    <h3>🛡️ Lecture 5: Safety & Risk Management</h3>
                    <p><strong>Activities:</strong> Risk assessment, hazard analysis, safety protocols</p>
                    <p><strong>Key Topics:</strong> Patient safety, electrical safety, incident management</p>
                    <div style="margin-top: 15px;">
                        <span style="background: #e3f2fd; padding: 4px 8px; border-radius: 12px; font-size: 0.8em;">6 Activities</span>
                        <span style="background: #f3e5f5; padding: 4px 8px; border-radius: 12px; font-size: 0.8em;">5 Case Studies</span>
                    </div>
                </div>
                
                <div class="lecture-card management" onclick="showLectureAnswers(6)">
                    <h3>💰 Lecture 6: Financial Management & Procurement</h3>
                    <p><strong>Activities:</strong> Budget planning, vendor evaluation, cost-benefit analysis</p>
                    <p><strong>Key Topics:</strong> Capital planning, procurement strategies, ROI analysis</p>
                    <div style="margin-top: 15px;">
                        <span style="background: #e3f2fd; padding: 4px 8px; border-radius: 12px; font-size: 0.8em;">7 Activities</span>
                        <span style="background: #f3e5f5; padding: 4px 8px; border-radius: 12px; font-size: 0.8em;">2 Case Studies</span>
                    </div>
                </div>

                <!-- Lectures 7-9: Advanced -->
                <div class="lecture-card advanced" onclick="showLectureAnswers(7)">
                    <h3>🔬 Lecture 7: Testing & Calibration</h3>
                    <p><strong>Activities:</strong> Calibration procedures, measurement uncertainty, quality assurance</p>
                    <p><strong>Key Topics:</strong> Metrology, traceability, GUM analysis, test protocols</p>
                    <div style="margin-top: 15px;">
                        <span style="background: #e3f2fd; padding: 4px 8px; border-radius: 12px; font-size: 0.8em;">9 Activities</span>
                        <span style="background: #f3e5f5; padding: 4px 8px; border-radius: 12px; font-size: 0.8em;">4 Case Studies</span>
                    </div>
                </div>

                <div class="lecture-card advanced" onclick="showLectureAnswers(8)">
                    <h3>🏭 Lecture 8: Manufacturing & Product Lifecycle</h3>
                    <p><strong>Activities:</strong> Design controls, manufacturing processes, lifecycle management</p>
                    <p><strong>Key Topics:</strong> Quality systems, design validation, post-market surveillance</p>
                    <div style="margin-top: 15px;">
                        <span style="background: #e3f2fd; padding: 4px 8px; border-radius: 12px; font-size: 0.8em;">6 Activities</span>
                        <span style="background: #f3e5f5; padding: 4px 8px; border-radius: 12px; font-size: 0.8em;">3 Case Studies</span>
                    </div>
                </div>

                <div class="lecture-card advanced" onclick="showLectureAnswers(9)">
                    <h3>🔍 Lecture 9: Troubleshooting & Problem Solving</h3>
                    <p><strong>Activities:</strong> Diagnostic procedures, root cause analysis, corrective actions</p>
                    <p><strong>Key Topics:</strong> Systematic troubleshooting, failure analysis, CAPA</p>
                    <div style="margin-top: 15px;">
                        <span style="background: #e3f2fd; padding: 4px 8px; border-radius: 12px; font-size: 0.8em;">8 Activities</span>
                        <span style="background: #f3e5f5; padding: 4px 8px; border-radius: 12px; font-size: 0.8em;">5 Case Studies</span>
                    </div>
                </div>

                <!-- Lectures 10-12: Specialized -->
                <div class="lecture-card specialized" onclick="showLectureAnswers(10)">
                    <h3>🌐 Lecture 10: Emerging Technologies</h3>
                    <p><strong>Activities:</strong> Technology evaluation, implementation planning, impact assessment</p>
                    <p><strong>Key Topics:</strong> AI/ML, IoT, telemedicine, digital health</p>
                    <div style="margin-top: 15px;">
                        <span style="background: #e3f2fd; padding: 4px 8px; border-radius: 12px; font-size: 0.8em;">5 Activities</span>
                        <span style="background: #f3e5f5; padding: 4px 8px; border-radius: 12px; font-size: 0.8em;">2 Case Studies</span>
                    </div>
                </div>

                <div class="lecture-card specialized" onclick="showLectureAnswers(11)">
                    <h3>🎓 Lecture 11: Professional Development</h3>
                    <p><strong>Activities:</strong> Career planning, certification pathways, continuing education</p>
                    <p><strong>Key Topics:</strong> Professional growth, leadership, ethics, communication</p>
                    <div style="margin-top: 15px;">
                        <span style="background: #e3f2fd; padding: 4px 8px; border-radius: 12px; font-size: 0.8em;">4 Activities</span>
                        <span style="background: #f3e5f5; padding: 4px 8px; border-radius: 12px; font-size: 0.8em;">2 Case Studies</span>
                    </div>
                </div>

                <div class="lecture-card specialized" onclick="showLectureAnswers(12)">
                    <h3>🔮 Lecture 12: Future of Clinical Engineering</h3>
                    <p><strong>Activities:</strong> Trend analysis, strategic planning, innovation assessment</p>
                    <p><strong>Key Topics:</strong> Future trends, challenges, opportunities, vision</p>
                    <div style="margin-top: 15px;">
                        <span style="background: #e3f2fd; padding: 4px 8px; border-radius: 12px; font-size: 0.8em;">3 Activities</span>
                        <span style="background: #f3e5f5; padding: 4px 8px; border-radius: 12px; font-size: 0.8em;">1 Case Study</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Model Answers Content (Initially Hidden) -->
        <div id="answersContent" class="section" style="display: none;">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                <h2 id="answersTitle">Model Answers</h2>
                <button onclick="hideAnswers()" style="background: #dc3545; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">✕ Close</button>
            </div>

            <div id="answersBody">
                <!-- Content will be populated by JavaScript -->
            </div>
        </div>

        <!-- Quick Statistics -->
        <div class="section">
            <h2>📊 Course Statistics</h2>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px;">
                <div style="background: #e3f2fd; padding: 20px; border-radius: 8px; text-align: center;">
                    <h3 style="margin: 0; color: #1976d2; font-size: 2em;">12</h3>
                    <p style="margin: 5px 0; color: #495057;">Total Lectures</p>
                </div>
                <div style="background: #e8f5e9; padding: 20px; border-radius: 8px; text-align: center;">
                    <h3 style="margin: 0; color: #388e3c; font-size: 2em;">73</h3>
                    <p style="margin: 5px 0; color: #495057;">Total Activities</p>
                </div>
                <div style="background: #fff3e0; padding: 20px; border-radius: 8px; text-align: center;">
                    <h3 style="margin: 0; color: #f57c00; font-size: 2em;">36</h3>
                    <p style="margin: 5px 0; color: #495057;">Case Studies</p>
                </div>
                <div style="background: #fce4ec; padding: 20px; border-radius: 8px; text-align: center;">
                    <h3 style="margin: 0; color: #c2185b; font-size: 2em;">100%</h3>
                    <p style="margin: 5px 0; color: #495057;">Coverage</p>
                </div>
            </div>
        </div>
    </div>

    <footer>
        <p>&copy; 2025 Clinical Engineering Model Answers | Dr. Mohammed Yagoub Esmail</p>
        <p>Nahda College | All Rights Reserved</p>
    </footer>

    <script>
        // Model Answers Data Structure
        const modelAnswers = {
            1: {
                title: "Lecture 1: Introduction to Clinical Engineering",
                activities: [
                    {
                        question: "Define Clinical Engineering and explain its role in healthcare delivery.",
                        modelAnswer: "Clinical Engineering is a specialized field that applies engineering principles and practices to healthcare technology management. It encompasses the application of engineering and management skills to ensure the safe and effective use of medical equipment and systems in healthcare facilities.",
                        keyPoints: [
                            "Integration of engineering principles with healthcare needs",
                            "Focus on medical equipment lifecycle management",
                            "Patient safety and regulatory compliance",
                            "Cost-effective technology solutions",
                            "Interdisciplinary collaboration with clinical staff"
                        ],
                        assessmentCriteria: [
                            "Accurate definition (20 points)",
                            "Clear explanation of healthcare role (25 points)",
                            "Examples of applications (20 points)",
                            "Understanding of scope (20 points)",
                            "Professional communication (15 points)"
                        ]
                    },
                    {
                        question: "Analyze the career pathways available in Clinical Engineering.",
                        modelAnswer: "Clinical Engineering offers diverse career pathways including: 1) Hospital-based clinical engineers managing equipment and technology, 2) Regulatory affairs specialists ensuring compliance, 3) Medical device industry roles in R&D and quality assurance, 4) Consulting positions providing specialized expertise, 5) Academic and research positions advancing the field.",
                        keyPoints: [
                            "Hospital/healthcare facility positions",
                            "Medical device industry opportunities",
                            "Regulatory and compliance roles",
                            "Consulting and independent practice",
                            "Academic and research careers"
                        ],
                        assessmentCriteria: [
                            "Comprehensive pathway identification (30 points)",
                            "Understanding of role requirements (25 points)",
                            "Career progression awareness (20 points)",
                            "Industry knowledge (15 points)",
                            "Personal reflection (10 points)"
                        ]
                    }
                ]
            },
            2: {
                title: "Lecture 2: Healthcare Technology Management",
                activities: [
                    {
                        question: "Develop a comprehensive Healthcare Technology Management (HTM) framework for a 300-bed hospital.",
                        modelAnswer: "A comprehensive HTM framework should include: 1) Strategic planning aligned with hospital goals, 2) Technology assessment and acquisition processes, 3) Lifecycle management from procurement to disposal, 4) Maintenance and support programs, 5) Performance monitoring and optimization, 6) Staff training and competency management, 7) Regulatory compliance and quality assurance, 8) Financial management and budgeting.",
                        keyPoints: [
                            "Strategic alignment with organizational goals",
                            "Comprehensive lifecycle approach",
                            "Risk-based decision making",
                            "Performance measurement systems",
                            "Stakeholder engagement processes"
                        ],
                        assessmentCriteria: [
                            "Framework completeness (25 points)",
                            "Strategic alignment (20 points)",
                            "Practical implementation (25 points)",
                            "Risk consideration (15 points)",
                            "Innovation and creativity (15 points)"
                        ]
                    }
                ]
            },
            3: {
                title: "Lecture 3: Regulatory Standards & Compliance",
                activities: [
                    {
                        question: "Compare and contrast FDA and ISO regulatory approaches for medical devices.",
                        modelAnswer: "FDA focuses on market access and post-market surveillance in the US, emphasizing premarket approval processes (510(k), PMA) and quality system regulations (QSR). ISO provides international standards for quality management (ISO 13485), risk management (ISO 14971), and usability engineering (IEC 62366). Key differences include: FDA is regulatory authority vs ISO as standards organization, FDA has legal enforcement power vs ISO provides voluntary standards, FDA focuses on US market vs ISO provides global framework.",
                        keyPoints: [
                            "FDA: Regulatory authority with enforcement power",
                            "ISO: International standards organization",
                            "Different approval pathways and requirements",
                            "Complementary rather than competing approaches",
                            "Global harmonization efforts"
                        ],
                        assessmentCriteria: [
                            "Accurate comparison (25 points)",
                            "Understanding of differences (25 points)",
                            "Practical implications (20 points)",
                            "Global perspective (15 points)",
                            "Critical analysis (15 points)"
                        ]
                    }
                ]
            }
        };

        // Show model answers for selected lecture
        function showLectureAnswers(lectureNumber) {
            const answersContent = document.getElementById('answersContent');
            const answersTitle = document.getElementById('answersTitle');
            const answersBody = document.getElementById('answersBody');

            // Update active lecture card
            document.querySelectorAll('.lecture-card').forEach(card => {
                card.classList.remove('active');
            });
            event.target.closest('.lecture-card').classList.add('active');

            // Get lecture data
            const lectureData = modelAnswers[lectureNumber];
            if (!lectureData) {
                answersBody.innerHTML = `
                    <div class="model-answer">
                        <h3>⚠️ Content Under Development</h3>
                        <p>Model answers for Lecture ${lectureNumber} are currently being prepared. Please check back soon for comprehensive solutions and assessment criteria.</p>
                    </div>
                `;
            } else {
                // Generate content
                let content = `<h3>${lectureData.title}</h3>`;

                lectureData.activities.forEach((activity, index) => {
                    content += `
                        <div class="answer-section">
                            <h4>Activity ${index + 1}</h4>
                            <p><strong>Question:</strong> ${activity.question}</p>

                            <button class="answer-toggle" onclick="toggleAnswer('answer-${lectureNumber}-${index}')">
                                Show Model Answer
                            </button>

                            <div id="answer-${lectureNumber}-${index}" class="hidden">
                                <div class="model-answer">
                                    <h5>📝 Model Answer:</h5>
                                    <p>${activity.modelAnswer}</p>
                                </div>

                                <div class="key-points">
                                    <h5>🎯 Key Points to Include:</h5>
                                    <ul>
                                        ${activity.keyPoints.map(point => `<li>${point}</li>`).join('')}
                                    </ul>
                                </div>

                                <div class="assessment-criteria">
                                    <h5>📊 Assessment Criteria:</h5>
                                    <ul>
                                        ${activity.assessmentCriteria.map(criteria => `<li>${criteria}</li>`).join('')}
                                    </ul>
                                </div>
                            </div>
                        </div>
                    `;
                });
            }

            answersTitle.textContent = `Model Answers - Lecture ${lectureNumber}`;
            answersBody.innerHTML = content;
            answersContent.style.display = 'block';
            answersContent.scrollIntoView({ behavior: 'smooth' });
        }

        // Toggle individual answer visibility
        function toggleAnswer(answerId) {
            const answerDiv = document.getElementById(answerId);
            const button = answerDiv.previousElementSibling;

            if (answerDiv.classList.contains('hidden')) {
                answerDiv.classList.remove('hidden');
                button.textContent = 'Hide Model Answer';
            } else {
                answerDiv.classList.add('hidden');
                button.textContent = 'Show Model Answer';
            }
        }

        // Hide answers section
        function hideAnswers() {
            document.getElementById('answersContent').style.display = 'none';
            document.querySelectorAll('.lecture-card').forEach(card => {
                card.classList.remove('active');
            });
        }

        // Filter lectures by category
        function filterLectures(category) {
            const cards = document.querySelectorAll('.lecture-card');
            const buttons = document.querySelectorAll('.filter-btn');

            // Update active filter button
            buttons.forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');

            // Show/hide cards based on category
            cards.forEach(card => {
                if (category === 'all' || card.classList.contains(category)) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });
        }

        // Search functionality
        function setupSearch() {
            const searchInput = document.getElementById('searchInput');
            searchInput.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();
                const cards = document.querySelectorAll('.lecture-card');

                cards.forEach(card => {
                    const text = card.textContent.toLowerCase();
                    if (text.includes(searchTerm)) {
                        card.style.display = 'block';
                    } else {
                        card.style.display = 'none';
                    }
                });
            });
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            setupSearch();

            // Add welcome message
            setTimeout(() => {
                if (!localStorage.getItem('modelAnswersVisited')) {
                    alert('Welcome to the Model Answers section! Click on any lecture card to view comprehensive solutions and assessment criteria for all activities.');
                    localStorage.setItem('modelAnswersVisited', 'true');
                }
            }, 1000);
        });
    </script>
</body>
</html>
