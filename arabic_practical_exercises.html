<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التمارين العملية - الهندسة السريرية | Practical Exercises - Clinical Engineering</title>
    <style>
        body {
            font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
            line-height: 1.8;
            color: #333;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            direction: rtl;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        header {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            color: white;
            padding: 40px 0;
            text-align: center;
            border-radius: 15px;
            margin-bottom: 30px;
            position: relative;
            overflow: hidden;
        }
        
        header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 30% 70%, rgba(255,255,255,0.1) 0%, transparent 50%);
        }
        
        .header-content {
            position: relative;
            z-index: 1;
        }
        
        .exercise-category {
            background: white;
            border-radius: 15px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
            transition: all 0.3s ease;
        }
        
        .exercise-category:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
        }
        
        .category-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px 30px;
            position: relative;
        }
        
        .category-title {
            font-size: 1.8rem;
            font-weight: 600;
            margin: 0 0 10px 0;
        }
        
        .category-subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            margin: 0;
            font-weight: 300;
        }
        
        .category-stats {
            position: absolute;
            top: 50%;
            left: 30px;
            transform: translateY(-50%);
            text-align: center;
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            line-height: 1;
        }
        
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.8;
        }
        
        .exercises-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            padding: 30px;
        }
        
        .exercise-card {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 25px;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .exercise-card:hover {
            border-color: #667eea;
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
        }
        
        .exercise-number {
            position: absolute;
            top: -15px;
            right: 20px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 1.1rem;
        }
        
        .exercise-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #2c3e50;
            margin: 15px 0 15px 0;
        }
        
        .exercise-description {
            color: #495057;
            margin-bottom: 20px;
            line-height: 1.6;
        }
        
        .exercise-objectives {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            border-right: 4px solid #2196f3;
        }
        
        .exercise-objectives h5 {
            color: #1976d2;
            margin: 0 0 10px 0;
            font-size: 1rem;
        }
        
        .exercise-objectives ul {
            margin: 0;
            padding-right: 20px;
        }
        
        .exercise-objectives li {
            margin-bottom: 5px;
            color: #495057;
            font-size: 0.9rem;
        }
        
        .exercise-tools {
            background: linear-gradient(135deg, #e8f5e9 0%, #c8e6c9 100%);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            border-right: 4px solid #4caf50;
        }
        
        .exercise-tools h5 {
            color: #2e7d32;
            margin: 0 0 10px 0;
            font-size: 1rem;
        }
        
        .tools-list {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }
        
        .tool-tag {
            background: #4caf50;
            color: white;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .exercise-steps {
            background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            border-right: 4px solid #ff9800;
        }
        
        .exercise-steps h5 {
            color: #f57c00;
            margin: 0 0 10px 0;
            font-size: 1rem;
        }
        
        .step-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: 10px;
            padding: 8px;
            background: rgba(255,255,255,0.7);
            border-radius: 6px;
        }
        
        .step-number {
            background: #ff9800;
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
            font-weight: bold;
            margin-left: 10px;
            flex-shrink: 0;
        }
        
        .step-text {
            color: #495057;
            font-size: 0.9rem;
        }
        
        .exercise-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            justify-content: center;
        }
        
        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            font-size: 0.9rem;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        }
        
        .btn-success {
            background: linear-gradient(45deg, #28a745, #20c997);
        }
        
        .btn-warning {
            background: linear-gradient(45deg, #ffc107, #ffb300);
        }
        
        .btn-info {
            background: linear-gradient(45deg, #17a2b8, #138496);
        }
        
        .progress-indicator {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .progress-header {
            text-align: center;
            margin-bottom: 25px;
        }
        
        .progress-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }
        
        .progress-item {
            text-align: center;
            padding: 20px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 10px;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }
        
        .progress-item:hover {
            border-color: #667eea;
            transform: translateY(-2px);
        }
        
        .progress-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
        }
        
        .progress-label {
            color: #495057;
            font-weight: 500;
        }
        
        .home-btn {
            position: fixed;
            bottom: 20px;
            left: 20px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 16px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: 600;
            font-size: 0.9em;
            line-height: 1.2;
            text-align: center;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
            transition: all 0.3s ease;
            z-index: 1000;
            min-width: 80px;
        }
        
        .home-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }
            
            .exercises-grid {
                grid-template-columns: 1fr;
                padding: 20px;
            }
            
            .exercise-card {
                padding: 20px;
            }
            
            .category-stats {
                position: static;
                transform: none;
                margin-top: 15px;
            }
        }
        
        .fade-in {
            animation: fadeIn 0.6s ease-in;
        }
        
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <div class="header-content">
                <!-- Enhanced Navigation Bar -->
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px; padding: 15px 0; border-bottom: 1px solid rgba(255,255,255,0.2);">
                    <div>
                        <a href="index.html" style="background: rgba(255,255,255,0.2); color: white; padding: 10px 20px; border-radius: 25px; text-decoration: none; font-size: 1em; transition: all 0.3s ease; display: inline-flex; align-items: center; gap: 10px; font-weight: 500;"
                           onmouseover="this.style.background='rgba(255,255,255,0.3)'; this.style.transform='translateY(-2px)'"
                           onmouseout="this.style.background='rgba(255,255,255,0.2)'; this.style.transform='translateY(0)'">
                            🏠 العودة للرئيسية | Back to Home
                        </a>
                    </div>
                    <div style="display: flex; gap: 12px;">
                        <a href="arabic_course_lectures.html" style="background: rgba(255,255,255,0.15); color: white; padding: 8px 16px; border-radius: 20px; text-decoration: none; font-size: 0.9em; transition: all 0.3s ease;"
                           onmouseover="this.style.background='rgba(255,255,255,0.25)'"
                           onmouseout="this.style.background='rgba(255,255,255,0.15)'">📚 المحاضرات</a>
                        <a href="clinical_engineering_simulation.html" style="background: rgba(255,255,255,0.15); color: white; padding: 8px 16px; border-radius: 20px; text-decoration: none; font-size: 0.9em; transition: all 0.3s ease;"
                           onmouseover="this.style.background='rgba(255,255,255,0.25)'"
                           onmouseout="this.style.background='rgba(255,255,255,0.15)'">🚀 المحاكاة</a>
                        <a href="interactive_assessment.html" style="background: rgba(255,255,255,0.15); color: white; padding: 8px 16px; border-radius: 20px; text-decoration: none; font-size: 0.9em; transition: all 0.3s ease;"
                           onmouseover="this.style.background='rgba(255,255,255,0.25)'"
                           onmouseout="this.style.background='rgba(255,255,255,0.15)'">🎯 التقييم</a>
                        <a href="analytics_dashboard.html" style="background: rgba(255,255,255,0.15); color: white; padding: 8px 16px; border-radius: 20px; text-decoration: none; font-size: 0.9em; transition: all 0.3s ease;"
                           onmouseover="this.style.background='rgba(255,255,255,0.25)'"
                           onmouseout="this.style.background='rgba(255,255,255,0.15)'">📊 التحليلات</a>
                    </div>
                </div>
                
                <h1>التمارين العملية للهندسة السريرية</h1>
                <p style="font-size: 1.3rem; color: rgba(255,255,255,0.9); font-weight: 300; margin-bottom: 15px;">Practical Exercises for Clinical Engineering</p>
                <p style="font-size: 1.1rem; color: rgba(255,255,255,0.8); max-width: 800px; margin: 0 auto;">
                    مجموعة شاملة من التمارين العملية المصممة لتطوير المهارات التطبيقية في الهندسة السريرية
                </p>
                <p style="font-size: 1rem; color: rgba(255,255,255,0.7); max-width: 800px; margin: 10px auto 0;">
                    Comprehensive collection of practical exercises designed to develop applied skills in clinical engineering
                </p>
            </div>
        </header>

        <!-- Progress Indicator -->
        <div class="progress-indicator">
            <div class="progress-header">
                <h2 style="color: #667eea; margin-bottom: 10px;">📈 تقدم التمارين العملية | Practical Exercises Progress</h2>
                <p style="color: #6c757d; margin: 0;">تتبع إنجازك في التمارين العملية المختلفة</p>
            </div>
            
            <div class="progress-grid">
                <div class="progress-item">
                    <div class="progress-number" id="completedExercises">0</div>
                    <div class="progress-label">تمارين مكتملة | Completed</div>
                </div>
                <div class="progress-item">
                    <div class="progress-number" id="inProgressExercises">0</div>
                    <div class="progress-label">قيد التنفيذ | In Progress</div>
                </div>
                <div class="progress-item">
                    <div class="progress-number" id="totalExercises">24</div>
                    <div class="progress-label">إجمالي التمارين | Total Exercises</div>
                </div>
                <div class="progress-item">
                    <div class="progress-number" id="practicalHours">0</div>
                    <div class="progress-label">ساعات عملية | Practical Hours</div>
                </div>
            </div>
        </div>

        <!-- Device Operation Exercises -->
        <div class="exercise-category">
            <div class="category-header">
                <div class="category-stats">
                    <div class="stat-number">6</div>
                    <div class="stat-label">تمارين</div>
                </div>
                <h2 class="category-title">🔧 تشغيل الأجهزة الطبية</h2>
                <p class="category-subtitle">Medical Device Operation Exercises</p>
            </div>

            <div class="exercises-grid">
                <!-- Exercise 1: ECG Machine Operation -->
                <div class="exercise-card" data-exercise="1">
                    <div class="exercise-number">1</div>
                    <h3 class="exercise-title">تشغيل جهاز تخطيط القلب الكهربائي</h3>
                    <p class="exercise-description">
                        تعلم كيفية تشغيل وإعداد جهاز تخطيط القلب الكهربائي (ECG) بشكل صحيح وآمن، بما في ذلك وضع الأقطاب وتفسير النتائج الأساسية.
                    </p>

                    <div class="exercise-objectives">
                        <h5>🎯 الأهداف التعليمية:</h5>
                        <ul>
                            <li>فهم مبادئ عمل جهاز تخطيط القلب</li>
                            <li>إتقان وضع الأقطاب الصحيح</li>
                            <li>تشغيل الجهاز وإجراء القياسات</li>
                            <li>تفسير النتائج الأساسية</li>
                            <li>تطبيق إجراءات السلامة</li>
                        </ul>
                    </div>

                    <div class="exercise-tools">
                        <h5>🛠️ الأدوات المطلوبة:</h5>
                        <div class="tools-list">
                            <span class="tool-tag">جهاز ECG</span>
                            <span class="tool-tag">أقطاب كهربائية</span>
                            <span class="tool-tag">جل موصل</span>
                            <span class="tool-tag">ورق تسجيل</span>
                            <span class="tool-tag">مناديل تنظيف</span>
                        </div>
                    </div>

                    <div class="exercise-steps">
                        <h5>📋 خطوات التنفيذ:</h5>
                        <div class="step-item">
                            <div class="step-number">1</div>
                            <div class="step-text">فحص الجهاز والتأكد من سلامة الكابلات</div>
                        </div>
                        <div class="step-item">
                            <div class="step-number">2</div>
                            <div class="step-text">تحضير المريض وتنظيف مواقع الأقطاب</div>
                        </div>
                        <div class="step-item">
                            <div class="step-number">3</div>
                            <div class="step-text">وضع الأقطاب في المواقع الصحيحة</div>
                        </div>
                        <div class="step-item">
                            <div class="step-number">4</div>
                            <div class="step-text">تشغيل الجهاز وإجراء المعايرة</div>
                        </div>
                        <div class="step-item">
                            <div class="step-number">5</div>
                            <div class="step-text">تسجيل تخطيط القلب وحفظ النتائج</div>
                        </div>
                    </div>

                    <div class="exercise-actions">
                        <button class="btn btn-success" onclick="startExercise(1)">بدء التمرين | Start Exercise</button>
                        <button class="btn btn-info" onclick="viewGuide(1)">دليل التشغيل | Operation Guide</button>
                        <button class="btn btn-warning" onclick="markCompleted(1)">تم الإنجاز | Mark Complete</button>
                    </div>
                </div>

                <!-- Exercise 2: Blood Pressure Monitor -->
                <div class="exercise-card" data-exercise="2">
                    <div class="exercise-number">2</div>
                    <h3 class="exercise-title">قياس ضغط الدم الإلكتروني</h3>
                    <p class="exercise-description">
                        تطبيق عملي لاستخدام أجهزة قياس ضغط الدم الإلكترونية، بما في ذلك المعايرة والصيانة الأساسية وضمان دقة القياسات.
                    </p>

                    <div class="exercise-objectives">
                        <h5>🎯 الأهداف التعليمية:</h5>
                        <ul>
                            <li>فهم مبادئ قياس ضغط الدم</li>
                            <li>تشغيل الجهاز بطريقة صحيحة</li>
                            <li>إجراء معايرة أساسية</li>
                            <li>تفسير القراءات والنتائج</li>
                            <li>تطبيق إجراءات الصيانة الوقائية</li>
                        </ul>
                    </div>

                    <div class="exercise-tools">
                        <h5>🛠️ الأدوات المطلوبة:</h5>
                        <div class="tools-list">
                            <span class="tool-tag">جهاز ضغط إلكتروني</span>
                            <span class="tool-tag">كفة قياس</span>
                            <span class="tool-tag">جهاز معايرة</span>
                            <span class="tool-tag">مقياس زئبقي مرجعي</span>
                            <span class="tool-tag">سجل المعايرة</span>
                        </div>
                    </div>

                    <div class="exercise-steps">
                        <h5>📋 خطوات التنفيذ:</h5>
                        <div class="step-item">
                            <div class="step-number">1</div>
                            <div class="step-text">فحص الجهاز والكفة للتأكد من سلامتها</div>
                        </div>
                        <div class="step-item">
                            <div class="step-number">2</div>
                            <div class="step-text">إجراء معايرة الجهاز باستخدام المرجع</div>
                        </div>
                        <div class="step-item">
                            <div class="step-number">3</div>
                            <div class="step-text">تطبيق القياس على متطوع</div>
                        </div>
                        <div class="step-item">
                            <div class="step-number">4</div>
                            <div class="step-text">مقارنة النتائج مع القياس اليدوي</div>
                        </div>
                        <div class="step-item">
                            <div class="step-number">5</div>
                            <div class="step-text">توثيق النتائج وتقييم الدقة</div>
                        </div>
                    </div>

                    <div class="exercise-actions">
                        <button class="btn btn-success" onclick="startExercise(2)">بدء التمرين | Start Exercise</button>
                        <button class="btn btn-info" onclick="viewGuide(2)">دليل المعايرة | Calibration Guide</button>
                        <button class="btn btn-warning" onclick="markCompleted(2)">تم الإنجاز | Mark Complete</button>
                    </div>
                </div>

                <!-- Exercise 3: Pulse Oximeter -->
                <div class="exercise-card" data-exercise="3">
                    <div class="exercise-number">3</div>
                    <h3 class="exercise-title">جهاز قياس الأكسجين في الدم</h3>
                    <p class="exercise-description">
                        تعلم استخدام جهاز قياس الأكسجين في الدم (Pulse Oximeter) وفهم مبادئ عمله وطرق المعايرة والصيانة الدورية.
                    </p>

                    <div class="exercise-objectives">
                        <h5>🎯 الأهداف التعليمية:</h5>
                        <ul>
                            <li>فهم مبدأ عمل جهاز قياس الأكسجين</li>
                            <li>تطبيق القياس بطريقة صحيحة</li>
                            <li>تفسير قراءات SpO2 ومعدل النبض</li>
                            <li>إجراء اختبارات الأداء</li>
                            <li>تطبيق إجراءات التنظيف والتعقيم</li>
                        </ul>
                    </div>

                    <div class="exercise-tools">
                        <h5>🛠️ الأدوات المطلوبة:</h5>
                        <div class="tools-list">
                            <span class="tool-tag">جهاز Pulse Oximeter</span>
                            <span class="tool-tag">محاكي SpO2</span>
                            <span class="tool-tag">مواد تنظيف</span>
                            <span class="tool-tag">بطاريات احتياطية</span>
                            <span class="tool-tag">دليل المستخدم</span>
                        </div>
                    </div>

                    <div class="exercise-steps">
                        <h5>📋 خطوات التنفيذ:</h5>
                        <div class="step-item">
                            <div class="step-number">1</div>
                            <div class="step-text">فحص الجهاز وحالة البطارية</div>
                        </div>
                        <div class="step-item">
                            <div class="step-number">2</div>
                            <div class="step-text">اختبار الجهاز باستخدام المحاكي</div>
                        </div>
                        <div class="step-item">
                            <div class="step-number">3</div>
                            <div class="step-text">تطبيق القياس على متطوع</div>
                        </div>
                        <div class="step-item">
                            <div class="step-number">4</div>
                            <div class="step-text">مراقبة استقرار القراءات</div>
                        </div>
                        <div class="step-item">
                            <div class="step-number">5</div>
                            <div class="step-text">تنظيف وتعقيم الجهاز</div>
                        </div>
                    </div>

                    <div class="exercise-actions">
                        <button class="btn btn-success" onclick="startExercise(3)">بدء التمرين | Start Exercise</button>
                        <button class="btn btn-info" onclick="viewGuide(3)">دليل الاستخدام | User Guide</button>
                        <button class="btn btn-warning" onclick="markCompleted(3)">تم الإنجاز | Mark Complete</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Calibration and Testing Exercises -->
        <div class="exercise-category">
            <div class="category-header" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
                <div class="category-stats">
                    <div class="stat-number">6</div>
                    <div class="stat-label">تمارين</div>
                </div>
                <h2 class="category-title">⚖️ المعايرة والاختبار</h2>
                <p class="category-subtitle">Calibration and Testing Exercises</p>
            </div>

            <div class="exercises-grid">
                <!-- Exercise 4: Thermometer Calibration -->
                <div class="exercise-card" data-exercise="4">
                    <div class="exercise-number">4</div>
                    <h3 class="exercise-title">معايرة مقاييس الحرارة الطبية</h3>
                    <p class="exercise-description">
                        تطبيق عملي لمعايرة مقاييس الحرارة الطبية باستخدام حمام مائي مرجعي وتطبيق منهجية GUM لحساب عدم اليقين في القياس.
                    </p>

                    <div class="exercise-objectives">
                        <h5>🎯 الأهداف التعليمية:</h5>
                        <ul>
                            <li>فهم مبادئ معايرة مقاييس الحرارة</li>
                            <li>استخدام الحمام المائي المرجعي</li>
                            <li>تطبيق منهجية GUM</li>
                            <li>حساب عدم اليقين في القياس</li>
                            <li>إعداد شهادة المعايرة</li>
                        </ul>
                    </div>

                    <div class="exercise-tools">
                        <h5>🛠️ الأدوات المطلوبة:</h5>
                        <div class="tools-list">
                            <span class="tool-tag">حمام مائي مرجعي</span>
                            <span class="tool-tag">مقياس حرارة مرجعي</span>
                            <span class="tool-tag">مقاييس حرارة للاختبار</span>
                            <span class="tool-tag">برنامج حساب عدم اليقين</span>
                            <span class="tool-tag">نماذج شهادات المعايرة</span>
                        </div>
                    </div>

                    <div class="exercise-steps">
                        <h5>📋 خطوات التنفيذ:</h5>
                        <div class="step-item">
                            <div class="step-number">1</div>
                            <div class="step-text">إعداد الحمام المائي وضبط درجات الحرارة</div>
                        </div>
                        <div class="step-item">
                            <div class="step-number">2</div>
                            <div class="step-text">وضع المقاييس في الحمام والانتظار للاستقرار</div>
                        </div>
                        <div class="step-item">
                            <div class="step-number">3</div>
                            <div class="step-text">تسجيل القراءات عند نقاط مختلفة</div>
                        </div>
                        <div class="step-item">
                            <div class="step-number">4</div>
                            <div class="step-text">حساب الانحراف وعدم اليقين</div>
                        </div>
                        <div class="step-item">
                            <div class="step-number">5</div>
                            <div class="step-text">إعداد شهادة المعايرة النهائية</div>
                        </div>
                    </div>

                    <div class="exercise-actions">
                        <button class="btn btn-success" onclick="startExercise(4)">بدء التمرين | Start Exercise</button>
                        <button class="btn btn-info" onclick="viewGuide(4)">دليل المعايرة | Calibration Guide</button>
                        <button class="btn btn-warning" onclick="markCompleted(4)">تم الإنجاز | Mark Complete</button>
                    </div>
                </div>

                <!-- Exercise 5: Electrical Safety Testing -->
                <div class="exercise-card" data-exercise="5">
                    <div class="exercise-number">5</div>
                    <h3 class="exercise-title">اختبار السلامة الكهربائية</h3>
                    <p class="exercise-description">
                        تطبيق اختبارات السلامة الكهربائية للأجهزة الطبية وفقاً لمعيار IEC 62353، بما في ذلك اختبار التسرب والعزل والتأريض.
                    </p>

                    <div class="exercise-objectives">
                        <h5>🎯 الأهداف التعليمية:</h5>
                        <ul>
                            <li>فهم معايير السلامة الكهربائية</li>
                            <li>تطبيق اختبارات التسرب الكهربائي</li>
                            <li>قياس مقاومة العزل</li>
                            <li>اختبار فعالية التأريض</li>
                            <li>تفسير النتائج وإصدار التقارير</li>
                        </ul>
                    </div>

                    <div class="exercise-tools">
                        <h5>🛠️ الأدوات المطلوبة:</h5>
                        <div class="tools-list">
                            <span class="tool-tag">جهاز اختبار السلامة</span>
                            <span class="tool-tag">مقياس مقاومة العزل</span>
                            <span class="tool-tag">جهاز قياس التأريض</span>
                            <span class="tool-tag">أجهزة طبية للاختبار</span>
                            <span class="tool-tag">نماذج تقارير السلامة</span>
                        </div>
                    </div>

                    <div class="exercise-steps">
                        <h5>📋 خطوات التنفيذ:</h5>
                        <div class="step-item">
                            <div class="step-number">1</div>
                            <div class="step-text">فحص الجهاز بصرياً للتأكد من سلامته</div>
                        </div>
                        <div class="step-item">
                            <div class="step-number">2</div>
                            <div class="step-text">اختبار مقاومة العزل</div>
                        </div>
                        <div class="step-item">
                            <div class="step-number">3</div>
                            <div class="step-text">قياس التسرب الكهربائي</div>
                        </div>
                        <div class="step-item">
                            <div class="step-number">4</div>
                            <div class="step-text">اختبار فعالية التأريض</div>
                        </div>
                        <div class="step-item">
                            <div class="step-number">5</div>
                            <div class="step-text">توثيق النتائج وإصدار التقرير</div>
                        </div>
                    </div>

                    <div class="exercise-actions">
                        <button class="btn btn-success" onclick="startExercise(5)">بدء التمرين | Start Exercise</button>
                        <button class="btn btn-info" onclick="viewGuide(5)">دليل السلامة | Safety Guide</button>
                        <button class="btn btn-warning" onclick="markCompleted(5)">تم الإنجاز | Mark Complete</button>
                    </div>
                </div>

                <!-- Exercise 6: Performance Testing -->
                <div class="exercise-card" data-exercise="6">
                    <div class="exercise-number">6</div>
                    <h3 class="exercise-title">اختبار الأداء الوظيفي</h3>
                    <p class="exercise-description">
                        تطبيق اختبارات الأداء الوظيفي للأجهزة الطبية للتأكد من مطابقتها للمواصفات المطلوبة وضمان دقة القياسات.
                    </p>

                    <div class="exercise-objectives">
                        <h5>🎯 الأهداف التعليمية:</h5>
                        <ul>
                            <li>فهم متطلبات اختبار الأداء</li>
                            <li>تطبيق اختبارات الدقة</li>
                            <li>قياس الاستقرار والتكرارية</li>
                            <li>تقييم الاستجابة الزمنية</li>
                            <li>مقارنة النتائج مع المواصفات</li>
                        </ul>
                    </div>

                    <div class="exercise-tools">
                        <h5>🛠️ الأدوات المطلوبة:</h5>
                        <div class="tools-list">
                            <span class="tool-tag">محاكيات الإشارات</span>
                            <span class="tool-tag">أجهزة قياس مرجعية</span>
                            <span class="tool-tag">برامج تحليل البيانات</span>
                            <span class="tool-tag">أجهزة للاختبار</span>
                            <span class="tool-tag">نماذج تقارير الأداء</span>
                        </div>
                    </div>

                    <div class="exercise-steps">
                        <h5>📋 خطوات التنفيذ:</h5>
                        <div class="step-item">
                            <div class="step-number">1</div>
                            <div class="step-text">إعداد المحاكيات والأجهزة المرجعية</div>
                        </div>
                        <div class="step-item">
                            <div class="step-number">2</div>
                            <div class="step-text">تطبيق إشارات اختبار متنوعة</div>
                        </div>
                        <div class="step-item">
                            <div class="step-number">3</div>
                            <div class="step-text">تسجيل استجابة الجهاز</div>
                        </div>
                        <div class="step-item">
                            <div class="step-number">4</div>
                            <div class="step-text">تحليل البيانات وحساب الانحرافات</div>
                        </div>
                        <div class="step-item">
                            <div class="step-number">5</div>
                            <div class="step-text">مقارنة النتائج مع المواصفات</div>
                        </div>
                    </div>

                    <div class="exercise-actions">
                        <button class="btn btn-success" onclick="startExercise(6)">بدء التمرين | Start Exercise</button>
                        <button class="btn btn-info" onclick="viewGuide(6)">دليل الاختبار | Testing Guide</button>
                        <button class="btn btn-warning" onclick="markCompleted(6)">تم الإنجاز | Mark Complete</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Maintenance and Troubleshooting Exercises -->
        <div class="exercise-category">
            <div class="category-header" style="background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);">
                <div class="category-stats">
                    <div class="stat-number">6</div>
                    <div class="stat-label">تمارين</div>
                </div>
                <h2 class="category-title">🔧 الصيانة واستكشاف الأخطاء</h2>
                <p class="category-subtitle">Maintenance and Troubleshooting Exercises</p>
            </div>

            <div class="exercises-grid">
                <!-- Exercise 7: Preventive Maintenance -->
                <div class="exercise-card" data-exercise="7">
                    <div class="exercise-number">7</div>
                    <h3 class="exercise-title">الصيانة الوقائية للأجهزة</h3>
                    <p class="exercise-description">
                        تطبيق برنامج الصيانة الوقائية للأجهزة الطبية، بما في ذلك التنظيف والفحص والاختبار الدوري وتوثيق الأنشطة.
                    </p>

                    <div class="exercise-objectives">
                        <h5>🎯 الأهداف التعليمية:</h5>
                        <ul>
                            <li>فهم مبادئ الصيانة الوقائية</li>
                            <li>تطبيق جداول الصيانة</li>
                            <li>إجراء الفحوصات الدورية</li>
                            <li>توثيق أنشطة الصيانة</li>
                            <li>تحديث سجلات المعدات</li>
                        </ul>
                    </div>

                    <div class="exercise-tools">
                        <h5>🛠️ الأدوات المطلوبة:</h5>
                        <div class="tools-list">
                            <span class="tool-tag">أدوات التنظيف</span>
                            <span class="tool-tag">مواد التشحيم</span>
                            <span class="tool-tag">أدوات القياس</span>
                            <span class="tool-tag">قطع غيار أساسية</span>
                            <span class="tool-tag">نماذج سجلات الصيانة</span>
                        </div>
                    </div>

                    <div class="exercise-steps">
                        <h5>📋 خطوات التنفيذ:</h5>
                        <div class="step-item">
                            <div class="step-number">1</div>
                            <div class="step-text">مراجعة جدول الصيانة الوقائية</div>
                        </div>
                        <div class="step-item">
                            <div class="step-number">2</div>
                            <div class="step-text">إجراء الفحص البصري الشامل</div>
                        </div>
                        <div class="step-item">
                            <div class="step-number">3</div>
                            <div class="step-text">تنظيف وتشحيم الأجزاء المطلوبة</div>
                        </div>
                        <div class="step-item">
                            <div class="step-number">4</div>
                            <div class="step-text">إجراء الاختبارات الوظيفية</div>
                        </div>
                        <div class="step-item">
                            <div class="step-number">5</div>
                            <div class="step-text">توثيق النتائج وتحديث السجلات</div>
                        </div>
                    </div>

                    <div class="exercise-actions">
                        <button class="btn btn-success" onclick="startExercise(7)">بدء التمرين | Start Exercise</button>
                        <button class="btn btn-info" onclick="viewGuide(7)">دليل الصيانة | Maintenance Guide</button>
                        <button class="btn btn-warning" onclick="markCompleted(7)">تم الإنجاز | Mark Complete</button>
                    </div>
                </div>

                <!-- Exercise 8: Fault Diagnosis -->
                <div class="exercise-card" data-exercise="8">
                    <div class="exercise-number">8</div>
                    <h3 class="exercise-title">تشخيص الأعطال</h3>
                    <p class="exercise-description">
                        تطبيق منهجية منظمة لتشخيص أعطال الأجهزة الطبية باستخدام أدوات القياس والتحليل المنطقي للمشاكل.
                    </p>

                    <div class="exercise-objectives">
                        <h5>🎯 الأهداف التعليمية:</h5>
                        <ul>
                            <li>تطبيق منهجية تشخيص الأعطال</li>
                            <li>استخدام أدوات القياس والاختبار</li>
                            <li>تحليل الأعراض والمؤشرات</li>
                            <li>تحديد السبب الجذري للعطل</li>
                            <li>وضع خطة الإصلاح المناسبة</li>
                        </ul>
                    </div>

                    <div class="exercise-tools">
                        <h5>🛠️ الأدوات المطلوبة:</h5>
                        <div class="tools-list">
                            <span class="tool-tag">مقياس متعدد</span>
                            <span class="tool-tag">راسم الذبذبات</span>
                            <span class="tool-tag">مخططات الدوائر</span>
                            <span class="tool-tag">أدوات التشخيص</span>
                            <span class="tool-tag">نماذج تقارير الأعطال</span>
                        </div>
                    </div>

                    <div class="exercise-steps">
                        <h5>📋 خطوات التنفيذ:</h5>
                        <div class="step-item">
                            <div class="step-number">1</div>
                            <div class="step-text">جمع المعلومات حول العطل</div>
                        </div>
                        <div class="step-item">
                            <div class="step-number">2</div>
                            <div class="step-text">إجراء الفحص البصري والسمعي</div>
                        </div>
                        <div class="step-item">
                            <div class="step-number">3</div>
                            <div class="step-text">تطبيق اختبارات القياس المناسبة</div>
                        </div>
                        <div class="step-item">
                            <div class="step-number">4</div>
                            <div class="step-text">تحليل النتائج وتحديد السبب</div>
                        </div>
                        <div class="step-item">
                            <div class="step-number">5</div>
                            <div class="step-text">إعداد تقرير التشخيص</div>
                        </div>
                    </div>

                    <div class="exercise-actions">
                        <button class="btn btn-success" onclick="startExercise(8)">بدء التمرين | Start Exercise</button>
                        <button class="btn btn-info" onclick="viewGuide(8)">دليل التشخيص | Diagnosis Guide</button>
                        <button class="btn btn-warning" onclick="markCompleted(8)">تم الإنجاز | Mark Complete</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div style="text-align: center; margin: 40px 0; padding: 30px; background: white; border-radius: 15px; box-shadow: 0 5px 15px rgba(0,0,0,0.1);">
            <h3 style="color: #667eea; margin-bottom: 25px;">🎯 إجراءات سريعة | Quick Actions</h3>
            <div style="display: flex; gap: 15px; justify-content: center; flex-wrap: wrap;">
                <button class="btn btn-success" onclick="exportProgress()">تصدير التقدم | Export Progress</button>
                <button class="btn btn-info" onclick="viewAllGuides()">عرض جميع الأدلة | View All Guides</button>
                <button class="btn btn-warning" onclick="resetProgress()">إعادة تعيين | Reset Progress</button>
                <a href="analytics_dashboard.html" class="btn" style="background: linear-gradient(45deg, #ff5722, #ff9800);">لوحة التحليلات | Analytics</a>
            </div>
        </div>
    </div>

    <!-- Home Button -->
    <button class="home-btn" onclick="window.location.href='index.html'">
        🏠 الرئيسية<br>Home
    </button>

    <script>
        // Exercise progress tracking
        let exerciseProgress = JSON.parse(localStorage.getItem('exerciseProgress')) || {};
        let practicalHours = parseInt(localStorage.getItem('practicalHours')) || 0;

        // Initialize page
        function initializePage() {
            updateProgressDisplay();
            updateExerciseCards();

            // Add fade-in animation to cards
            const cards = document.querySelectorAll('.exercise-card');
            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.classList.add('fade-in');
                }, index * 100);
            });
        }

        // Start exercise
        function startExercise(exerciseNum) {
            exerciseProgress[exerciseNum] = 'in-progress';
            saveProgress();
            updateProgressDisplay();
            updateExerciseCards();

            showNotification(`تم بدء التمرين ${exerciseNum} | Exercise ${exerciseNum} started`, 'info');

            // Simulate exercise timer
            const startTime = Date.now();
            localStorage.setItem(`exercise_${exerciseNum}_start`, startTime);
        }

        // Mark exercise as completed
        function markCompleted(exerciseNum) {
            if (exerciseProgress[exerciseNum] === 'in-progress') {
                const startTime = localStorage.getItem(`exercise_${exerciseNum}_start`);
                if (startTime) {
                    const duration = Math.round((Date.now() - parseInt(startTime)) / 60000); // minutes
                    practicalHours += duration;
                    localStorage.setItem('practicalHours', practicalHours);
                    localStorage.removeItem(`exercise_${exerciseNum}_start`);
                }
            }

            exerciseProgress[exerciseNum] = 'completed';
            saveProgress();
            updateProgressDisplay();
            updateExerciseCards();

            showNotification(`تم إنجاز التمرين ${exerciseNum} بنجاح! | Exercise ${exerciseNum} completed successfully!`, 'success');
        }

        // View exercise guide
        function viewGuide(exerciseNum) {
            const guides = {
                1: 'دليل تشغيل جهاز تخطيط القلب الكهربائي\n\n1. التحضير:\n- فحص الجهاز والكابلات\n- تحضير الأقطاب والجل\n\n2. التشغيل:\n- وضع الأقطاب في المواقع الصحيحة\n- تشغيل الجهاز وإجراء المعايرة\n\n3. التسجيل:\n- تسجيل تخطيط القلب\n- حفظ وطباعة النتائج',
                2: 'دليل معايرة جهاز قياس ضغط الدم\n\n1. الإعداد:\n- فحص الجهاز والكفة\n- تحضير المرجع\n\n2. المعايرة:\n- ربط الجهاز بالمرجع\n- تطبيق ضغوط مختلفة\n\n3. التوثيق:\n- تسجيل القراءات\n- حساب الانحرافات',
                3: 'دليل استخدام جهاز قياس الأكسجين\n\n1. الفحص:\n- فحص الجهاز والبطارية\n- اختبار المحاكي\n\n2. القياس:\n- وضع الجهاز على الإصبع\n- انتظار استقرار القراءة\n\n3. التنظيف:\n- تنظيف وتعقيم الجهاز',
                4: 'دليل معايرة مقاييس الحرارة\n\n1. الإعداد:\n- تحضير الحمام المائي\n- ضبط درجات الحرارة\n\n2. المعايرة:\n- وضع المقاييس في الحمام\n- تسجيل القراءات\n\n3. التحليل:\n- حساب عدم اليقين\n- إعداد الشهادة',
                5: 'دليل اختبار السلامة الكهربائية\n\n1. الفحص البصري:\n- فحص الكابلات والمقابس\n- التأكد من سلامة الهيكل\n\n2. الاختبارات:\n- قياس مقاومة العزل\n- اختبار التسرب\n\n3. التوثيق:\n- تسجيل النتائج\n- إصدار التقرير',
                6: 'دليل اختبار الأداء الوظيفي\n\n1. الإعداد:\n- تحضير المحاكيات\n- ضبط الأجهزة المرجعية\n\n2. الاختبار:\n- تطبيق إشارات مختلفة\n- تسجيل الاستجابة\n\n3. التحليل:\n- مقارنة مع المواصفات\n- تقييم الأداء',
                7: 'دليل الصيانة الوقائية\n\n1. التخطيط:\n- مراجعة جدول الصيانة\n- تحضير الأدوات\n\n2. التنفيذ:\n- الفحص والتنظيف\n- التشحيم والاختبار\n\n3. التوثيق:\n- تسجيل الأنشطة\n- تحديث السجلات',
                8: 'دليل تشخيص الأعطال\n\n1. جمع المعلومات:\n- وصف العطل\n- تاريخ المشكلة\n\n2. التشخيص:\n- الفحص البصري\n- اختبارات القياس\n\n3. التحليل:\n- تحديد السبب\n- وضع خطة الإصلاح'
            };

            const guide = guides[exerciseNum] || 'الدليل غير متوفر حالياً';
            alert(guide);
        }

        // Update progress display
        function updateProgressDisplay() {
            const completed = Object.values(exerciseProgress).filter(status => status === 'completed').length;
            const inProgress = Object.values(exerciseProgress).filter(status => status === 'in-progress').length;

            document.getElementById('completedExercises').textContent = completed;
            document.getElementById('inProgressExercises').textContent = inProgress;
            document.getElementById('practicalHours').textContent = Math.round(practicalHours / 60);
        }

        // Update exercise cards appearance
        function updateExerciseCards() {
            Object.entries(exerciseProgress).forEach(([exerciseNum, status]) => {
                const card = document.querySelector(`[data-exercise="${exerciseNum}"]`);
                if (card) {
                    // Remove existing status classes
                    card.classList.remove('completed', 'in-progress', 'not-started');
                    // Add current status class
                    card.classList.add(status);

                    // Update card appearance based on status
                    if (status === 'completed') {
                        card.style.borderColor = '#28a745';
                        card.style.background = 'linear-gradient(135deg, #e8f5e9 0%, #f8f9fa 100%)';
                    } else if (status === 'in-progress') {
                        card.style.borderColor = '#ffc107';
                        card.style.background = 'linear-gradient(135deg, #fff3cd 0%, #f8f9fa 100%)';
                    }
                }
            });
        }

        // Save progress
        function saveProgress() {
            localStorage.setItem('exerciseProgress', JSON.stringify(exerciseProgress));
        }

        // Export progress
        function exportProgress() {
            const completed = Object.values(exerciseProgress).filter(status => status === 'completed').length;
            const inProgress = Object.values(exerciseProgress).filter(status => status === 'in-progress').length;
            const hours = Math.round(practicalHours / 60);

            const report = `
تقرير التمارين العملية - الهندسة السريرية
Practical Exercises Report - Clinical Engineering

التاريخ | Date: ${new Date().toLocaleDateString()}
التمارين المكتملة | Completed Exercises: ${completed}/24
التمارين قيد التنفيذ | In Progress: ${inProgress}
الساعات العملية | Practical Hours: ${hours}

تفاصيل التمارين | Exercise Details:
${Object.entries(exerciseProgress).map(([num, status]) =>
    `التمرين ${num} | Exercise ${num}: ${status === 'completed' ? 'مكتمل | Completed' : status === 'in-progress' ? 'قيد التنفيذ | In Progress' : 'لم يبدأ | Not Started'}`
).join('\n')}
            `;

            const blob = new Blob([report], { type: 'text/plain;charset=utf-8' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `practical-exercises-report-${new Date().toISOString().split('T')[0]}.txt`;
            a.click();
            URL.revokeObjectURL(url);

            showNotification('تم تصدير التقرير بنجاح | Report exported successfully', 'success');
        }

        // View all guides
        function viewAllGuides() {
            window.open('arabic_course_lectures.html', '_blank');
        }

        // Reset progress
        function resetProgress() {
            if (confirm('هل أنت متأكد من إعادة تعيين جميع البيانات؟ | Are you sure you want to reset all progress?')) {
                exerciseProgress = {};
                practicalHours = 0;
                localStorage.removeItem('exerciseProgress');
                localStorage.removeItem('practicalHours');

                // Remove all exercise start times
                for (let i = 1; i <= 24; i++) {
                    localStorage.removeItem(`exercise_${i}_start`);
                }

                updateProgressDisplay();
                updateExerciseCards();
                showNotification('تم إعادة تعيين التقدم | Progress reset successfully', 'warning');
            }
        }

        // Show notification
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            const colors = {
                success: '#28a745',
                warning: '#ffc107',
                info: '#17a2b8',
                error: '#dc3545'
            };

            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${colors[type] || colors.info};
                color: white;
                padding: 15px 20px;
                border-radius: 10px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                z-index: 10000;
                font-weight: bold;
                max-width: 400px;
                animation: slideIn 0.5s ease;
            `;
            notification.textContent = message;

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.animation = 'slideOut 0.5s ease';
                setTimeout(() => {
                    if (document.body.contains(notification)) {
                        document.body.removeChild(notification);
                    }
                }, 500);
            }, 3000);
        }

        // Add CSS animations
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);

        // Initialize page on load
        document.addEventListener('DOMContentLoaded', initializePage);
    </script>
</body>
</html>
