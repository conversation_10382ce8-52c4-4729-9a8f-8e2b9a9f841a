<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clinical Engineering & Hospital Administration</title>
    <!-- Tailwind CSS CDN for styling -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Google Fonts for 'Inter' typeface -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        /* Base styles for the body and overall container */
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f0f2f5; /* Light grey background for the presentation area */
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh; /* Full viewport height */
            margin: 0;
            overflow: hidden; /* Hide scrollbars, especially important for slide transitions */
        }

        /* Styles for the main slide container */
        .slide-container {
            width: 90vw; /* Responsive width */
            max-width: 1200px; /* Maximum width for larger screens */
            height: 75vh; /* Responsive height */
            max-height: 800px; /* Maximum height */
            background-color: #ffffff; /* White background for slides */
            border-radius: 20px; /* Rounded corners for the container */
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1); /* Soft shadow for depth */
            position: relative; /* Needed for absolute positioning of slides and buttons */
            overflow: hidden; /* Hide content overflowing during transitions */
        }

        /* Styles for individual slides */
        .slide {
            position: absolute; /* Allows slides to overlap and transition */
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column; /* Content stacks vertically */
            justify-content: center;
            align-items: center;
            padding: 40px; /* Padding inside each slide */
            opacity: 0; /* Hidden by default */
            transition: opacity 0.8s ease-in-out, transform 0.8s ease-in-out; /* Smooth transitions */
            transform: translateX(100%); /* Start off-screen to the right */
            color: #333; /* Dark grey text */
            text-align: center; /* Center text by default */
        }
        /* Active slide: visible and in position */
        .slide.active {
            opacity: 1;
            transform: translateX(0);
        }
        /* Previous slide: moves off-screen to the left */
        .slide.previous {
            transform: translateX(-100%);
        }

        /* Slide title styling */
        .slide-title {
            font-size: 2.5rem; /* Large font size */
            font-weight: 700; /* Bold */
            margin-bottom: 2rem; /* Space below title */
            color: #1a202c; /* Very dark grey */
        }

        /* General slide content area styling */
        .slide-content {
            font-size: 1.2rem;
            line-height: 1.6;
            text-align: left; /* Align content text to the left */
            width: 100%;
            max-width: 900px; /* Max width for content area */
            display: flex; /* Use flexbox for sub-sections */
            flex-wrap: wrap; /* Allow content sections to wrap on smaller screens */
            gap: 30px; /* Space between content sections */
            justify-content: center; /* Center content sections */
            align-items: flex-start; /* Align content sections to the top */
        }
        /* Paragraph and list spacing within content */
        .slide-content p, .slide-content ul {
            margin-bottom: 1rem;
        }
        .slide-content li {
            margin-bottom: 0.5rem;
        }

        /* Footer text styling for slides */
        .slide-footer {
            margin-top: auto; /* Pushes footer to the bottom */
            font-size: 0.9rem;
            color: #6b7280; /* Muted grey */
        }

        /* Navigation button styling */
        .nav-button {
            position: absolute;
            top: 50%;
            transform: translateY(-50%); /* Vertically center the buttons */
            background-color: #4c51bf; /* Indigo primary color */
            color: white;
            border: none;
            padding: 15px 25px; /* Large padding for click target */
            font-size: 1.5rem;
            border-radius: 50%; /* Circular buttons */
            cursor: pointer;
            z-index: 10; /* Ensure buttons are above slides */
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2); /* Soft shadow for buttons */
            transition: background-color 0.3s ease, transform 0.3s ease; /* Hover effects */
        }
        .nav-button:hover {
            background-color: #3f46ad; /* Darker indigo on hover */
            transform: translateY(-50%) scale(1.05); /* Slightly enlarge on hover */
        }
        .nav-button.left {
            left: 20px; /* Position left button */
        }
        .nav-button.right {
            right: 20px; /* Position right button */
        }

        /* Styling for content sub-sections (e.g., columns) */
        .content-section {
            flex: 1; /* Allows sections to grow and shrink */
            min-width: 300px; /* Minimum width before wrapping */
            padding: 10px;
        }
        .content-section h3 {
            font-size: 1.8rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #2d3748; /* Darker grey for section titles */
        }
        .content-section ul {
            list-style-type: none; /* Remove default list bullets */
            padding-left: 0;
        }
        .content-section li {
            display: flex; /* Use flex for icon and text alignment */
            align-items: flex-start; /* Align items to the top */
            margin-bottom: 10px;
        }
        /* Icon styling within list items */
        .content-section .icon {
            margin-right: 15px;
            font-size: 2.2rem;
            color: #667eea; /* Light indigo accent for icons */
            line-height: 1; /* Ensure vertical alignment with text */
        }
        /* General placeholder image styling */
        .image-placeholder {
            width: 100%;
            height: 200px; /* Fixed height for placeholders */
            background-color: #e2e8f0; /* Light blue-grey background */
            border-radius: 10px;
            display: flex;
            justify-content: center;
            align-items: center;
            color: #718096; /* Muted text color */
            font-size: 1.1rem;
            text-align: center;
            margin-top: 1rem;
            overflow: hidden; /* Hide overflow from text or images */
        }
        .image-placeholder img {
            width: 100%;
            height: 100%;
            object-fit: cover; /* Cover the area, cropping if necessary */
        }

        /* Call-to-action button styling */
        .cta-button {
            background-color: #667eea; /* Light indigo */
            color: white;
            padding: 12px 25px;
            border-radius: 10px;
            font-size: 1.1rem;
            margin-top: 2rem;
            transition: background-color 0.3s ease;
            cursor: pointer;
        }
        .cta-button:hover {
            background-color: #5a67d8; /* Darker indigo on hover */
        }

        /* Organogram (Organizational Chart) styling */
        .organogram {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 15px; /* Space between levels */
            margin-top: 20px;
            width: 100%;
        }
        .org-level {
            display: flex;
            gap: 20px; /* Space between boxes in the same level */
            justify-content: center;
            width: 100%;
            flex-wrap: wrap; /* Allow levels to wrap */
        }
        .org-box {
            background-color: #edf2f7; /* Lighter grey for boxes */
            padding: 15px 25px;
            border-radius: 10px;
            font-weight: 600;
            color: #2d3748;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05); /* Soft shadow */
            min-width: 150px;
            text-align: center;
        }
        .arrow-down {
            width: 0;
            height: 0;
            border-left: 10px solid transparent;
            border-right: 10px solid transparent;
            border-top: 15px solid #a0aec0; /* Grey arrow */
        }
    </style>
</head>
<body>
    <div class="slide-container">
        <!-- Title Slide -->
        <div class="slide active" id="slide-0">
            <h1 class="slide-title">Clinical Engineering & Hospital Administration</h1>
            <div class="text-xl text-gray-700">
                <p><strong>Dr. Mohammed Yagoub Esmail</strong></p>
                <p>SUST - BME, @ 2025, Copy right</p>
                <p>Email: <EMAIL></p>
                <p>Phone: +249912867327, +966538076790</p>
            </div>
            <div class="image-placeholder mt-8">
                <img src="https://placehold.co/600x300/667eea/ffffff?text=Modern+Healthcare+Technology" alt="Modern Healthcare Technology">
            </div>
            <p class="slide-footer">Introduction to the Synergy of Engineering & Administration</p>
        </div>

        <!-- Slide 1: Hospital Administration & Relevance to Clinical Engineers -->
        <div class="slide" id="slide-1">
            <h2 class="slide-title">I. Hospital Administration & Relevance to Clinical Engineers</h2>
            <div class="slide-content">
                <div class="content-section">
                    <h3>Understanding Administration</h3>
                    <ul>
                        <li><span class="icon">🏥</span> While not the primary focus, understanding administrative departments is crucial for effective operation within a hospital.</li>
                    </ul>
                </div>
                <div class="content-section">
                    <h3>Key Administrative Departments</h3>
                    <ul>
                        <li>
                            <span class="icon">📦</span> <strong>Purchasing & Warehousing:</strong>
                            <p class="text-base text-gray-700 mt-2">Clinical engineers rely on these for acquiring spare parts and consumables needed for medical equipment maintenance. Ensures availability for operational equipment.</p>
                        </li>
                        <li>
                            <span class="icon">✅</span> <strong>Quality Department:</strong>
                            <p class="text-base text-gray-700 mt-2">Responsible for ensuring quality standards (e.g., JCI, ISO) are met. Ensures proper documentation of repairs (devices, time, technicians, costs) and standardized performance of maintenance processes.</p>
                        </li>
                    </ul>
                </div>
                <div class="image-placeholder mt-4">
                    <img src="https://placehold.co/400x200/4c51bf/ffffff?text=Hospital+Management" alt="Hospital Management System">
                </div>
            </div>
            <p class="slide-footer">Effective Collaboration for Operational Excellence</p>
        </div>

        <!-- Slide 2: Medical vs. Support Services -->
        <div class="slide" id="slide-2">
            <h2 class="slide-title">II. Medical vs. Support Services</h2>
            <div class="slide-content">
                <div class="content-section">
                    <h3><span class="icon">🩺</span> Medical-Surgical Services</h3>
                    <ul>
                        <li>Core services directly related to patient diagnosis and treatment.</li>
                        <li><strong>Examples:</strong> Outpatient Clinics, Inpatient Wards, Operating Rooms, Diagnostic Imaging (X-ray, CT, MRI), Laboratories.</li>
                    </ul>
                </div>
                <div class="content-section">
                    <h3><span class="icon">🤝</span> Support Services</h3>
                    <ul>
                        <li>Services that facilitate the delivery of medical care but are not directly diagnostic or therapeutic.</li>
                        <li><strong>Examples:</strong> Pharmacy, Blood Bank, Housekeeping, IT Systems.</li>
                        <li>
                            <strong>Central Sterilization Department (CSSD):</strong>
                            <p class="text-base text-gray-700 mt-1">Function: Sterilizes surgical instruments and any equipment that comes into contact with patients. Classified as a support service.</p>
                        </li>
                    </ul>
                </div>
                <div class="image-placeholder mt-4">
                    <img src="https://placehold.co/400x200/667eea/ffffff?text=Hospital+Departments+Overview" alt="Hospital Departments Overview">
                </div>
            </div>
            <p class="slide-footer">Distinguishing Core Healthcare Functions</p>
        </div>

        <!-- Slide 3: Hospital Organizational Structure (Organogram) -->
        <div class="slide" id="slide-3">
            <h2 class="slide-title">III. Hospital Organizational Structure (Organogram)</h2>
            <div class="slide-content flex-col items-center">
                <div class="text-center w-full mb-8">
                    <p class="text-lg mb-4">A diagram showing the hierarchy and reporting structure within an organization.</p>
                    <p class="text-xl font-semibold text-indigo-700">Clinical engineers typically fall under the Engineering Department.</p>
                </div>
                <div class="organogram">
                    <div class="org-box">General Director/CEO</div>
                    <div class="arrow-down"></div>
                    <div class="org-level">
                        <div class="org-box">Head of Nursing</div>
                        <div class="org-box">Head of Operations</div>
                        <div class="org-box">Head of Pharmacy</div>
                        <div class="org-box">Head of Engineering</div>
                    </div>
                    <div class="arrow-down"></div>
                    <div class="org-level">
                        <div class="org-box">Electro-mechanical<br>(HVAC, Fire Safety, Electrical, Plumbing, IT)</div>
                        <div class="org-box">Clinical Engineering<br>(Medical Equipment)</div>
                    </div>
                </div>
                <p class="text-base text-gray-700 mt-8">Note: Investment in medical equipment (e.g., CT scanners ~$2M-100k+, Linear Accelerators ~$8M+) is increasingly surpassing electro-mechanical systems, suggesting potential shifts in leadership.</p>
            </div>
            <p class="slide-footer">Navigating the Hospital Hierarchy for Career Advancement</p>
        </div>

        <!-- Slide 4: FDA Classification of Medical Devices -->
        <div class="slide" id="slide-4">
            <h2 class="slide-title">IV. FDA Classification of Medical Devices</h2>
            <div class="slide-content flex-col items-center">
                <p class="text-xl mb-6 text-gray-700">The U.S. FDA classifies medical devices into three classes based on their risk level:</p>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 w-full">
                    <div class="content-section bg-blue-50 p-6 rounded-lg shadow-md">
                        <h3 class="flex items-center text-blue-800"><span class="icon text-blue-600">🥇</span> Class I: Lowest Risk</h3>
                        <p class="text-base text-gray-800">Require minimal regulation.</p>
                        <p class="text-sm text-gray-600 mt-2">Examples: Elastic bandages, examination gloves, simple wheelchairs, basic surgical scalpels (disposable).</p>
                    </div>
                    <div class="content-section bg-green-50 p-6 rounded-lg shadow-md">
                        <h3 class="flex items-center text-green-800"><span class="icon text-green-600">🥈</span> Class II: Moderate Risk</h3>
                        <p class="text-base text-gray-800">Require more stringent controls and performance standards.</p>
                        <p class="text-sm text-gray-600 mt-2">Examples: Syringes, infusion pumps, X-ray machines, ultrasound devices, blood pressure monitors.</p>
                    </div>
                    <div class="content-section bg-red-50 p-6 rounded-lg shadow-md">
                        <h3 class="flex items-center text-red-800"><span class="icon text-red-600">🥉</span> Class III: Highest Risk</h3>
                        <p class="text-base text-gray-800">Often life-sustaining or implantable. Require the most rigorous pre-market approval.</p>
                        <p class="text-sm text-gray-600 mt-2">Examples: Pacemakers, artificial heart valves, ventilators, complex imaging systems.</p>
                    </div>
                </div>
                <p class="text-sm text-gray-600 mt-6">Understanding these classifications is vital for selecting, procuring, and managing medical equipment safely and compliantly.</p>
            </div>
            <p class="slide-footer">FDA's Framework for Device Safety and Effectiveness</p>
        </div>

        <!-- Slide 5: Defining Medical Devices (FDA Perspective) -->
        <div class="slide" id="slide-5">
            <h2 class="slide-title">V. Defining Medical Devices (FDA Perspective)</h2>
            <div class="slide-content">
                <div class="content-section">
                    <h3>Broad FDA Definition for Comprehensive Regulation:</h3>
                    <ul>
                        <li><span class="icon">🛠️</span> <strong>Instrument:</strong> An apparatus used in the diagnosis, cure, mitigation, treatment, or prevention of disease.</li>
                        <li><span class="icon">⚙️</span> <strong>Apparatus:</strong> A device or machine.</li>
                        <li><span class="icon">🩹</span> <strong>Implant:</strong> A device surgically placed inside the body.</li>
                        <li><span class="icon">🧪</span> <strong>In Vitro Device (IVD):</strong> Devices used for testing samples taken from the body (e.g., blood glucose meters, pregnancy tests, laboratory reagents).</li>
                        <li><span class="icon">🧩</span> <strong>Component Parts:</strong> Even parts of a larger device can be considered medical devices if intended for medical use.</li>
                        <li><span class="icon">💡</span> <strong>"Novel" Devices (Gadget):</strong> Any new invention or innovation that provides a medical service (e.g., a pen modified to measure blood pressure).</li>
                    </ul>
                </div>
                <div class="content-section flex items-center justify-center">
                    <div class="image-placeholder w-full h-auto min-h-[250px]">
                        <img src="https://placehold.co/400x300/4c51bf/ffffff?text=Diverse+Medical+Devices" alt="Diverse Medical Devices">
                    </div>
                </div>
            </div>
            <p class="slide-footer">The Comprehensive Scope of Medical Devices in Healthcare</p>
        </div>

        <!-- Slide 6: Supply Chain and Material Considerations -->
        <div class="slide" id="slide-6">
            <h2 class="slide-title">VI. Supply Chain and Material Considerations</h2>
            <div class="slide-content">
                <div class="content-section">
                    <h3><span class="icon">🔗</span> Supply Chain</h3>
                    <p class="text-lg text-gray-700">The entire process of producing and delivering a product, from raw materials to the end-user.</p>
                    <h3 class="mt-6"><span class="icon">💉</span> Material Examples in a Syringe:</h3>
                    <ul>
                        <li><strong>Plastic:</strong> For the barrel and plunger.</li>
                        <li><strong>Rubber/Silicone:</strong> For the plunger seal.</li>
                        <li><strong>Metal (Stainless Steel):</strong> For the needle.</li>
                        <li><strong>Lubricants:</strong> Sometimes used on the plunger.</li>
                    </ul>
                </div>
                <div class="content-section">
                    <h3>Key Considerations:</h3>
                    <ul>
                        <li>
                            <span class="icon">💰</span> <strong>Cost Implications:</strong> The cost of materials significantly impacts the final product price (e.g., platinum vs. plastic).
                        </li>
                        <li>
                            <span class="icon">🌱</span> <strong>Biocompatibility:</strong> Materials contacting the body must be biocompatible – causing no adverse reactions.
                        </li>
                        <li>
                            <span class="icon">✨</span> <strong>Sterilization:</strong> Many medical devices require sterilization before use.
                        </li>
                        <li>
                            <span class="icon">💧</span> <strong>Humidification:</strong> Increases humidity of inhaled air, crucial for patients on ventilators or with respiratory conditions. Humidifier with a ventilator is a medical device; standalone for home use may not be.
                        </li>
                    </ul>
                </div>
                <div class="image-placeholder mt-4">
                    <img src="https://placehold.co/500x250/667eea/ffffff?text=Supply+Chain+for+Medical+Products" alt="Supply Chain and Materials in Medical Devices">
                </div>
            </div>
            <p class="slide-footer">From Raw Materials to Patient Care: A Detailed Look</p>
        </div>

        <!-- Slide 7: The Role of the FDA in Device Approval -->
        <div class="slide" id="slide-7">
            <h2 class="slide-title">VII. The Role of the FDA in Device Approval</h2>
            <div class="slide-content flex-col items-center">
                <p class="text-2xl font-semibold text-indigo-700 mb-8">The FDA is responsible for ensuring that medical devices are safe and effective.</p>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-8 w-full max-w-4xl">
                    <div class="content-section bg-purple-50 p-6 rounded-lg shadow-md">
                        <h3 class="flex items-center text-purple-800"><span class="icon text-purple-600">📝</span> Approval Process</h3>
                        <p class="text-base text-gray-800">Manufacturers must submit detailed information about their devices for FDA review, including:</p>
                        <ul class="list-disc pl-5 text-sm text-gray-700">
                            <li>Design specifications</li>
                            <li>Material composition</li>
                            <li>Manufacturing processes</li>
                            <li>Comprehensive performance data</li>
                        </ul>
                    </div>
                    <div class="content-section bg-yellow-50 p-6 rounded-lg shadow-md">
                        <h3 class="flex items-center text-yellow-800"><span class="icon text-yellow-600">🚫</span> Consequences of Non-Compliance</h3>
                        <p class="text-base text-gray-800">Devices that do not receive FDA approval cannot be legally marketed or sold in the United States.</p>
                        <p class="text-sm text-gray-700 mt-2">This is why many student projects in biomedical engineering often do not reach the market – they lack the necessary regulatory understanding and compliance.</p>
                    </div>
                </div>
                <div class="image-placeholder mt-8">
                    <img src="https://placehold.co/500x200/4c51bf/ffffff?text=FDA+Regulatory+Process" alt="FDA Approval Process Flowchart">
                </div>
            </div>
            <p class="slide-footer">Ensuring Safety and Efficacy for Public Health</p>
        </div>

        <!-- Slide 8: Industry Statistics and Market Size -->
        <div class="slide" id="slide-8">
            <h2 class="slide-title">VIII. Industry Statistics and Market Size</h2>
            <div class="slide-content flex-col items-center">
                <p class="text-2xl font-semibold text-green-700 mb-8">The Medical Device Industry: A Globally Significant and Growing Sector</p>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-8 w-full max-w-4xl">
                    <div class="content-section bg-teal-50 p-6 rounded-lg shadow-md">
                        <h3 class="flex items-center text-teal-800"><span class="icon text-teal-600">📈</span> Market Growth</h3>
                        <p class="text-base text-gray-800">Projected to reach <strong class="text-xl text-teal-700">$500 Billion</strong> globally by 2026.</p>
                        <p class="text-sm text-gray-700 mt-2">It is one of the largest industries worldwide, reflecting its critical role in healthcare.</p>
                    </div>
                    <div class="content-section bg-orange-50 p-6 rounded-lg shadow-md">
                        <h3 class="flex items-center text-orange-800"><span class="icon text-orange-600">💲</span> Investment & Comparison</h3>
                        <p class="text-base text-gray-800">The medical device market is comparable in size to the <strong class="text-xl text-orange-700">Smartphone Market</strong>.</p>
                        <p class="text-sm text-gray-700 mt-2">This significant market size attracts substantial investment, driving innovation and development.</p>
                    </div>
                </div>
                <div class="image-placeholder mt-8">
                    <img src="https://placehold.co/500x200/667eea/ffffff?text=Global+Medical+Device+Market" alt="Global Medical Device Market Size Chart">
                </div>
            </div>
            <p class="slide-footer">Understanding the Economic Impact of Medical Devices</p>
        </div>

        <!-- Slide 9: FDA Organizational Structure -->
        <div class="slide" id="slide-9">
            <h2 class="slide-title">IX. FDA Organizational Structure</h2>
            <div class="slide-content flex-col items-center">
                <p class="text-xl mb-6 text-gray-700">The FDA is an agency within the U.S. Department of Health and Human Services (HHS).</p>
                <div class="organogram">
                    <div class="org-box">U.S. Department of Health and Human Services (HHS)</div>
                    <div class="arrow-down"></div>
                    <div class="org-box">Food and Drug Administration (FDA)</div>
                    <div class="arrow-down"></div>
                    <div class="org-box">Center for Devices and Radiological Health (CDRH)</div>
                </div>
                <div class="text-center w-full mt-8">
                    <p class="text-lg font-semibold text-indigo-700">CDRH's Key Roles:</p>
                    <ul class="list-disc inline-block text-left text-gray-700">
                        <li>Classifying medical devices based on risk.</li>
                        <li>Reviewing applications for market approval (e.g., 510(k), PMA).</li>
                        <li>Overseeing post-market surveillance to monitor device safety once in use.</li>
                    </ul>
                </div>
                <div class="image-placeholder mt-8">
                    <img src="https://placehold.co/500x200/4c51bf/ffffff?text=FDA+Hierarchy" alt="FDA Organizational Hierarchy">
                </div>
            </div>
            <p class="slide-footer">The Regulatory Body Governing Medical Devices in the U.S.</p>
        </div>

        <!-- Slide 10: Key Takeaways & Future Learning -->
        <div class="slide" id="slide-10">
            <h2 class="slide-title">X. Key Takeaways & Future Learning</h2>
            <div class="slide-content flex-col items-center">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-8 w-full max-w-4xl">
                    <div class="content-section bg-indigo-50 p-6 rounded-lg shadow-md">
                        <h3 class="flex items-center text-indigo-800"><span class="icon text-indigo-600">🌟</span> Importance of Understanding</h3>
                        <ul class="list-disc pl-5 text-base text-gray-800">
                            <li>The various departments within a hospital.</li>
                            <li>Regulatory processes for medical devices.</li>
                            <li>The complete lifecycle of medical devices.</li>
                        </ul>
                    </div>
                    <div class="content-section bg-purple-50 p-6 rounded-lg shadow-md">
                        <h3 class="flex items-center text-purple-800"><span class="icon text-purple-600">🧠</span> Active Learning</h3>
                        <ul class="list-disc pl-5 text-base text-gray-800">
                            <li>Be an active participant in your learning journey.</li>
                            <li>Always ask questions to deepen your understanding.</li>
                            <li>Engage with the material beyond just memorization.</li>
                        </ul>
                    </div>
                </div>
                <p class="text-xl font-semibold text-gray-700 mt-10">This lecture will continue to explore specific aspects of medical devices and their regulation in upcoming sessions.</p>
                <button class="cta-button">Continue Your Learning Journey</button>
            </div>
            <p class="slide-footer">Continuous Growth in Clinical Engineering and Healthcare</p>
        </div>

        <!-- Navigation Buttons -->
        <button class="nav-button left" id="prevBtn">‹</button>
        <button class="nav-button right" id="nextBtn">›</button>
    </div>

    <script>
        // Get all slide elements
        const slides = document.querySelectorAll('.slide');
        let currentSlide = 0; // Keep track of the current active slide index

        /**
         * Displays the slide at the given index and applies transition classes.
         * @param {number} index The index of the slide to display.
         */
        function showSlide(index) {
            slides.forEach((slide, i) => {
                // Remove all transition/active classes from all slides
                slide.classList.remove('active', 'previous');
                if (i === index) {
                    // Add 'active' class to the current slide to make it visible
                    slide.classList.add('active');
                } else if (i < index) {
                    // For slides that have already been viewed, move them off-screen to the left
                    slide.classList.add('previous');
                }
                // Slides with i > index (future slides) will naturally stay off-screen to the right
            });
        }

        /**
         * Navigates to the next slide.
         */
        function nextSlide() {
            // Increment currentSlide, looping back to 0 if at the end
            currentSlide = (currentSlide + 1) % slides.length;
            showSlide(currentSlide);
        }

        /**
         * Navigates to the previous slide.
         */
        function prevSlide() {
            // Decrement currentSlide, looping to the last slide if at the beginning
            currentSlide = (currentSlide - 1 + slides.length) % slides.length;
            showSlide(currentSlide);
        }

        // Add event listeners to the navigation buttons
        document.getElementById('nextBtn').addEventListener('click', nextSlide);
        document.getElementById('prevBtn').addEventListener('click', prevSlide);

        // Initialize the presentation by showing the first slide when the DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            showSlide(currentSlide);
        });
    </script>
</body>
</html>
