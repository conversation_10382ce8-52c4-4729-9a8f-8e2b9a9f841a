<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحليلات والإحصائيات | Analytics Dashboard</title>
    <style>
        body {
            font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            direction: rtl;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        header {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            color: white;
            padding: 30px 0;
            text-align: center;
            border-radius: 15px;
            margin-bottom: 30px;
        }
        
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }
        
        .card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
        }
        
        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f0f0f0;
        }
        
        .card-icon {
            font-size: 2.5em;
            margin-left: 15px;
        }
        
        .card-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #2c3e50;
            margin: 0;
        }
        
        .metric {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #f8f9fa;
        }
        
        .metric:last-child {
            border-bottom: none;
        }
        
        .metric-label {
            color: #7f8c8d;
            font-size: 0.95rem;
        }
        
        .metric-value {
            font-weight: bold;
            font-size: 1.1rem;
            color: #2c3e50;
        }
        
        .progress-ring {
            width: 120px;
            height: 120px;
            margin: 0 auto;
            position: relative;
        }
        
        .progress-ring svg {
            width: 100%;
            height: 100%;
            transform: rotate(-90deg);
        }
        
        .progress-ring circle {
            fill: none;
            stroke-width: 8;
        }
        
        .progress-ring .bg {
            stroke: #f0f0f0;
        }
        
        .progress-ring .progress {
            stroke: #3498db;
            stroke-linecap: round;
            transition: stroke-dasharray 0.5s ease;
        }
        
        .progress-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 1.5rem;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .chart-container {
            height: 300px;
            background: #f8f9fa;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #7f8c8d;
            font-size: 1.1rem;
        }
        
        .btn {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            margin: 5px;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(52, 152, 219, 0.3);
        }
        
        .btn-success {
            background: linear-gradient(45deg, #27ae60, #2ecc71);
        }
        
        .btn-warning {
            background: linear-gradient(45deg, #f39c12, #e67e22);
        }
        
        .btn-danger {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
        }
        
        .alert {
            padding: 15px 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            border-right: 4px solid;
        }
        
        .alert-info {
            background: #e3f2fd;
            border-color: #2196f3;
            color: #1976d2;
        }
        
        .alert-success {
            background: #e8f5e9;
            border-color: #4caf50;
            color: #2e7d32;
        }
        
        .alert-warning {
            background: #fff3e0;
            border-color: #ff9800;
            color: #f57c00;
        }
        
        .home-btn {
            position: fixed;
            bottom: 20px;
            left: 20px;
            background: linear-gradient(45deg, #9b59b6, #8e44ad);
            color: white;
            border: none;
            padding: 12px 16px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: 600;
            font-size: 0.9em;
            line-height: 1.2;
            text-align: center;
            box-shadow: 0 4px 12px rgba(155, 89, 182, 0.3);
            transition: all 0.3s ease;
            z-index: 1000;
            min-width: 80px;
        }
        
        .home-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 16px rgba(155, 89, 182, 0.4);
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }
            
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
            
            .card {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>لوحة التحليلات والإحصائيات</h1>
            <p style="font-size: 1.1rem; margin-bottom: 20px;">Analytics Dashboard - Clinical Engineering Platform</p>
            <div style="display: flex; justify-content: center; gap: 15px; flex-wrap: wrap;">
                <a href="index.html" style="background: rgba(255,255,255,0.2); color: white; padding: 8px 16px; border-radius: 20px; text-decoration: none;">🏠 الرئيسية | Home</a>
                <a href="arabic_course_lectures.html" style="background: rgba(255,255,255,0.2); color: white; padding: 8px 16px; border-radius: 20px; text-decoration: none;">📚 المحاضرات</a>
                <a href="interactive_assessment.html" style="background: rgba(255,255,255,0.2); color: white; padding: 8px 16px; border-radius: 20px; text-decoration: none;">🎯 التقييم</a>
            </div>
        </header>

        <!-- System Overview -->
        <div class="alert alert-info">
            <strong>📊 نظرة عامة على النظام | System Overview:</strong>
            مرحباً بكم في لوحة التحليلات الشاملة لمنصة الهندسة السريرية. هنا يمكنكم متابعة جميع الإحصائيات والمقاييس المهمة.
        </div>

        <!-- Main Dashboard Grid -->
        <div class="dashboard-grid">
            <!-- Course Progress Card -->
            <div class="card">
                <div class="card-header">
                    <div class="card-icon">📈</div>
                    <h3 class="card-title">تقدم المنهج | Course Progress</h3>
                </div>
                
                <div class="progress-ring" id="courseProgressRing">
                    <svg>
                        <circle class="bg" cx="60" cy="60" r="52"></circle>
                        <circle class="progress" cx="60" cy="60" r="52" id="courseProgressCircle"></circle>
                    </svg>
                    <div class="progress-text" id="courseProgressText">0%</div>
                </div>
                
                <div style="margin-top: 20px;">
                    <div class="metric">
                        <span class="metric-label">المحاضرات المكتملة | Completed Lectures</span>
                        <span class="metric-value" id="completedLecturesCount">0/12</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">الأنشطة المنجزة | Completed Activities</span>
                        <span class="metric-value" id="completedActivitiesCount">0/21</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">ساعات الدراسة | Study Hours</span>
                        <span class="metric-value" id="totalStudyHours">0</span>
                    </div>
                </div>
            </div>

            <!-- Assessment Results Card -->
            <div class="card">
                <div class="card-header">
                    <div class="card-icon">🎯</div>
                    <h3 class="card-title">نتائج التقييم | Assessment Results</h3>
                </div>
                
                <div class="progress-ring" id="assessmentProgressRing">
                    <svg>
                        <circle class="bg" cx="60" cy="60" r="52"></circle>
                        <circle class="progress" cx="60" cy="60" r="52" id="assessmentProgressCircle" style="stroke: #27ae60;"></circle>
                    </svg>
                    <div class="progress-text" id="assessmentProgressText">0%</div>
                </div>
                
                <div style="margin-top: 20px;">
                    <div class="metric">
                        <span class="metric-label">آخر نتيجة | Last Score</span>
                        <span class="metric-value" id="lastAssessmentScore">--</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">المتوسط العام | Average Score</span>
                        <span class="metric-value" id="averageScore">--</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">عدد المحاولات | Attempts</span>
                        <span class="metric-value" id="assessmentAttempts">0</span>
                    </div>
                </div>
            </div>

            <!-- Platform Usage Card -->
            <div class="card">
                <div class="card-header">
                    <div class="card-icon">📱</div>
                    <h3 class="card-title">استخدام المنصة | Platform Usage</h3>
                </div>
                
                <div style="margin-top: 20px;">
                    <div class="metric">
                        <span class="metric-label">إجمالي الزيارات | Total Visits</span>
                        <span class="metric-value" id="totalVisits">0</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">الصفحة الأكثر زيارة | Most Visited</span>
                        <span class="metric-value" id="mostVisitedPage">--</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">آخر زيارة | Last Visit</span>
                        <span class="metric-value" id="lastVisit">--</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">الوقت المستغرق | Time Spent</span>
                        <span class="metric-value" id="timeSpent">0 دقيقة</span>
                    </div>
                </div>
                
                <div style="margin-top: 20px; text-align: center;">
                    <button class="btn btn-success" onclick="exportUsageReport()">تصدير التقرير | Export Report</button>
                </div>
            </div>

            <!-- System Performance Card -->
            <div class="card">
                <div class="card-header">
                    <div class="card-icon">⚡</div>
                    <h3 class="card-title">أداء النظام | System Performance</h3>
                </div>
                
                <div style="margin-top: 20px;">
                    <div class="metric">
                        <span class="metric-label">سرعة التحميل | Load Speed</span>
                        <span class="metric-value" id="loadSpeed">-- ms</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">حالة النظام | System Status</span>
                        <span class="metric-value" style="color: #27ae60;" id="systemStatus">متصل | Online</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">آخر تحديث | Last Update</span>
                        <span class="metric-value" id="lastUpdate">--</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">الإصدار | Version</span>
                        <span class="metric-value">2025.1.0</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div style="text-align: center; margin-top: 40px;">
            <button class="btn btn-success" onclick="refreshData()">تحديث البيانات | Refresh Data</button>
            <button class="btn btn-warning" onclick="exportFullReport()">تصدير تقرير شامل | Export Full Report</button>
            <button class="btn btn-danger" onclick="resetAllData()">إعادة تعيين البيانات | Reset All Data</button>
        </div>
    </div>

    <!-- Home Button -->
    <button class="home-btn" onclick="window.location.href='index.html'">
        🏠 الرئيسية<br>Home
    </button>

    <script>
        // Analytics data management
        let analyticsData = {
            courseProgress: JSON.parse(localStorage.getItem('lectureProgress')) || {},
            assessmentResults: JSON.parse(localStorage.getItem('assessmentResults')) || [],
            platformUsage: JSON.parse(localStorage.getItem('platformUsage')) || {},
            systemMetrics: {}
        };

        // Initialize dashboard
        function initializeDashboard() {
            updateCourseProgress();
            updateAssessmentResults();
            updatePlatformUsage();
            updateSystemMetrics();
            
            // Set up auto-refresh
            setInterval(refreshData, 30000); // Refresh every 30 seconds
        }

        // Update course progress
        function updateCourseProgress() {
            const completed = Object.values(analyticsData.courseProgress).filter(status => status === 'completed').length;
            const percentage = Math.round((completed / 12) * 100);
            
            document.getElementById('courseProgressText').textContent = percentage + '%';
            document.getElementById('completedLecturesCount').textContent = `${completed}/12`;
            
            // Update progress ring
            const circle = document.getElementById('courseProgressCircle');
            const circumference = 2 * Math.PI * 52;
            const offset = circumference - (percentage / 100) * circumference;
            circle.style.strokeDasharray = circumference;
            circle.style.strokeDashoffset = offset;
            
            // Calculate completed activities (estimate)
            const completedActivities = Math.floor(completed * 1.75); // Average 1.75 activities per lecture
            document.getElementById('completedActivitiesCount').textContent = `${completedActivities}/21`;
            
            // Get study hours
            const studyHours = Math.round((parseInt(localStorage.getItem('totalStudyTime')) || 0) / 60);
            document.getElementById('totalStudyHours').textContent = studyHours;
        }

        // Update assessment results
        function updateAssessmentResults() {
            const results = analyticsData.assessmentResults;
            
            if (results.length > 0) {
                const lastScore = results[results.length - 1].score;
                const averageScore = Math.round(results.reduce((sum, result) => sum + result.score, 0) / results.length);
                
                document.getElementById('lastAssessmentScore').textContent = lastScore + '%';
                document.getElementById('averageScore').textContent = averageScore + '%';
                document.getElementById('assessmentAttempts').textContent = results.length;
                document.getElementById('assessmentProgressText').textContent = averageScore + '%';
                
                // Update assessment progress ring
                const circle = document.getElementById('assessmentProgressCircle');
                const circumference = 2 * Math.PI * 52;
                const offset = circumference - (averageScore / 100) * circumference;
                circle.style.strokeDasharray = circumference;
                circle.style.strokeDashoffset = offset;
            }
        }

        // Update platform usage
        function updatePlatformUsage() {
            const usage = analyticsData.platformUsage;
            
            // Simulate usage data if not available
            if (!usage.totalVisits) {
                usage.totalVisits = Math.floor(Math.random() * 50) + 10;
                usage.mostVisitedPage = 'المحاضرات العربية';
                usage.lastVisit = new Date().toLocaleDateString('ar-SA');
                usage.timeSpent = Math.floor(Math.random() * 120) + 30;
                
                localStorage.setItem('platformUsage', JSON.stringify(usage));
            }
            
            document.getElementById('totalVisits').textContent = usage.totalVisits || 0;
            document.getElementById('mostVisitedPage').textContent = usage.mostVisitedPage || '--';
            document.getElementById('lastVisit').textContent = usage.lastVisit || '--';
            document.getElementById('timeSpent').textContent = (usage.timeSpent || 0) + ' دقيقة';
        }

        // Update system metrics
        function updateSystemMetrics() {
            const loadTime = performance.now();
            document.getElementById('loadSpeed').textContent = Math.round(loadTime) + ' ms';
            document.getElementById('lastUpdate').textContent = new Date().toLocaleString('ar-SA');
        }

        // Refresh all data
        function refreshData() {
            analyticsData = {
                courseProgress: JSON.parse(localStorage.getItem('lectureProgress')) || {},
                assessmentResults: JSON.parse(localStorage.getItem('assessmentResults')) || [],
                platformUsage: JSON.parse(localStorage.getItem('platformUsage')) || {},
                systemMetrics: {}
            };
            
            updateCourseProgress();
            updateAssessmentResults();
            updatePlatformUsage();
            updateSystemMetrics();
            
            showNotification('تم تحديث البيانات بنجاح | Data refreshed successfully', 'success');
        }

        // Export usage report
        function exportUsageReport() {
            const report = `
تقرير استخدام المنصة - الهندسة السريرية
Platform Usage Report - Clinical Engineering

التاريخ | Date: ${new Date().toLocaleDateString()}
إجمالي الزيارات | Total Visits: ${analyticsData.platformUsage.totalVisits || 0}
الصفحة الأكثر زيارة | Most Visited: ${analyticsData.platformUsage.mostVisitedPage || '--'}
آخر زيارة | Last Visit: ${analyticsData.platformUsage.lastVisit || '--'}
الوقت المستغرق | Time Spent: ${analyticsData.platformUsage.timeSpent || 0} دقيقة

تقدم المنهج | Course Progress:
المحاضرات المكتملة | Completed Lectures: ${Object.values(analyticsData.courseProgress).filter(s => s === 'completed').length}/12
ساعات الدراسة | Study Hours: ${Math.round((parseInt(localStorage.getItem('totalStudyTime')) || 0) / 60)}

نتائج التقييم | Assessment Results:
عدد المحاولات | Attempts: ${analyticsData.assessmentResults.length}
${analyticsData.assessmentResults.length > 0 ? `آخر نتيجة | Last Score: ${analyticsData.assessmentResults[analyticsData.assessmentResults.length - 1].score}%` : ''}
            `;
            
            downloadReport(report, 'usage-report');
        }

        // Export full report
        function exportFullReport() {
            const completed = Object.values(analyticsData.courseProgress).filter(status => status === 'completed').length;
            const percentage = Math.round((completed / 12) * 100);
            
            const report = `
التقرير الشامل - منصة الهندسة السريرية
Comprehensive Report - Clinical Engineering Platform

التاريخ والوقت | Date & Time: ${new Date().toLocaleString()}

=== تقدم المنهج | Course Progress ===
المحاضرات المكتملة | Completed Lectures: ${completed}/12 (${percentage}%)
الأنشطة المنجزة | Completed Activities: ${Math.floor(completed * 1.75)}/21
ساعات الدراسة | Study Hours: ${Math.round((parseInt(localStorage.getItem('totalStudyTime')) || 0) / 60)}

=== نتائج التقييم | Assessment Results ===
عدد المحاولات | Total Attempts: ${analyticsData.assessmentResults.length}
${analyticsData.assessmentResults.length > 0 ? `
آخر نتيجة | Last Score: ${analyticsData.assessmentResults[analyticsData.assessmentResults.length - 1].score}%
المتوسط العام | Average Score: ${Math.round(analyticsData.assessmentResults.reduce((sum, result) => sum + result.score, 0) / analyticsData.assessmentResults.length)}%
` : 'لا توجد نتائج تقييم | No assessment results available'}

=== استخدام المنصة | Platform Usage ===
إجمالي الزيارات | Total Visits: ${analyticsData.platformUsage.totalVisits || 0}
الصفحة الأكثر زيارة | Most Visited Page: ${analyticsData.platformUsage.mostVisitedPage || '--'}
آخر زيارة | Last Visit: ${analyticsData.platformUsage.lastVisit || '--'}
الوقت المستغرق | Time Spent: ${analyticsData.platformUsage.timeSpent || 0} دقيقة

=== أداء النظام | System Performance ===
سرعة التحميل | Load Speed: ${Math.round(performance.now())} ms
حالة النظام | System Status: متصل | Online
الإصدار | Version: 2025.1.0
آخر تحديث | Last Update: ${new Date().toLocaleString('ar-SA')}

=== تفاصيل المحاضرات | Lecture Details ===
${Object.entries(analyticsData.courseProgress).map(([num, status]) => 
    `المحاضرة ${num} | Lecture ${num}: ${status === 'completed' ? 'مكتملة | Completed' : status === 'in-progress' ? 'قيد الدراسة | In Progress' : 'لم تبدأ | Not Started'}`
).join('\n')}
            `;
            
            downloadReport(report, 'comprehensive-report');
        }

        // Download report function
        function downloadReport(content, filename) {
            const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `${filename}-${new Date().toISOString().split('T')[0]}.txt`;
            a.click();
            URL.revokeObjectURL(url);
            
            showNotification('تم تصدير التقرير بنجاح | Report exported successfully', 'success');
        }

        // Reset all data
        function resetAllData() {
            if (confirm('هل أنت متأكد من إعادة تعيين جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه.\nAre you sure you want to reset all data? This action cannot be undone.')) {
                localStorage.clear();
                analyticsData = {
                    courseProgress: {},
                    assessmentResults: [],
                    platformUsage: {},
                    systemMetrics: {}
                };
                
                refreshData();
                showNotification('تم إعادة تعيين جميع البيانات | All data has been reset', 'warning');
            }
        }

        // Show notification
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `alert alert-${type}`;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 10000;
                max-width: 400px;
                animation: slideIn 0.5s ease;
            `;
            notification.textContent = message;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.style.animation = 'slideOut 0.5s ease';
                setTimeout(() => {
                    if (document.body.contains(notification)) {
                        document.body.removeChild(notification);
                    }
                }, 500);
            }, 3000);
        }

        // Add CSS animations
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);

        // Initialize dashboard on page load
        document.addEventListener('DOMContentLoaded', initializeDashboard);
    </script>
</body>
</html>
