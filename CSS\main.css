/* Main CSS for Clinical Engineering and Simulation 2025 */

/* Base Styles */
:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --accent-color: #e74c3c;
    --light-color: #ecf0f1;
    --dark-color: #2c3e50;
    --text-color: #333;
    --light-text: #fff;
    --border-radius: 5px;
    --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: #f9f9f9;
}

.container {
    width: 90%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

a {
    text-decoration: none;
    color: var(--secondary-color);
    transition: var(--transition);
}

a:hover {
    color: var(--primary-color);
}

ul {
    list-style: none;
}

img {
    max-width: 100%;
    height: auto;
}

.section {
    padding: 80px 0;
}

.section-title {
    text-align: center;
    margin-bottom: 50px;
    color: var(--primary-color);
    font-size: 2.5rem;
    position: relative;
}

.section-title::after {
    content: '';
    display: block;
    width: 80px;
    height: 4px;
    background-color: var(--secondary-color);
    margin: 15px auto 0;
    border-radius: 2px;
}

.section-description {
    text-align: center;
    max-width: 800px;
    margin: 0 auto 40px;
    color: #666;
    font-size: 1.1rem;
}

.btn {
    display: inline-block;
    padding: 12px 25px;
    border-radius: var(--border-radius);
    font-weight: 600;
    text-align: center;
    cursor: pointer;
    transition: var(--transition);
    border: none;
}

.btn.primary {
    background-color: var(--secondary-color);
    color: var(--light-text);
}

.btn.primary:hover {
    background-color: #2980b9;
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.btn.secondary {
    background-color: transparent;
    color: var(--secondary-color);
    border: 2px solid var(--secondary-color);
}

.btn.secondary:hover {
    background-color: var(--secondary-color);
    color: var(--light-text);
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

/* Header Styles */
header {
    background-color: var(--primary-color);
    padding: 20px 0;
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

header h1 {
    color: var(--light-text);
    font-size: 1.8rem;
}

nav ul {
    display: flex;
}

nav ul li {
    margin-left: 25px;
}

nav ul li a {
    color: var(--light-text);
    font-weight: 500;
    padding: 8px 0;
    position: relative;
}

nav ul li a::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background-color: var(--secondary-color);
    transition: var(--transition);
}

nav ul li a:hover::after,
nav ul li a.active::after {
    width: 100%;
}

/* Hero Section */
.hero {
    background: linear-gradient(rgba(44, 62, 80, 0.8), rgba(44, 62, 80, 0.8)), url('https://via.placeholder.com/1920x1080') no-repeat center center/cover;
    color: var(--light-text);
    padding: 150px 0;
    text-align: center;
}

.hero-content {
    max-width: 800px;
    margin: 0 auto;
}

.hero h2 {
    font-size: 3rem;
    margin-bottom: 20px;
    line-height: 1.2;
}

.hero p {
    font-size: 1.2rem;
    margin-bottom: 30px;
    opacity: 0.9;
}

.hero-buttons {
    display: flex;
    justify-content: center;
    gap: 20px;
}

/* Course Overview Section */
.course-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.course-card {
    background-color: #fff;
    border-radius: var(--border-radius);
    padding: 30px;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    text-align: center;
}

.course-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
}

.card-icon {
    font-size: 2.5rem;
    color: var(--secondary-color);
    margin-bottom: 20px;
}

.course-card h3 {
    margin-bottom: 15px;
    color: var(--primary-color);
}

/* Lectures Section */
.lectures-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.lecture-card {
    background-color: #fff;
    border-radius: var(--border-radius);
    padding: 30px;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.lecture-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
}

.lecture-number {
    position: absolute;
    top: 15px;
    right: 15px;
    background-color: var(--secondary-color);
    color: var(--light-text);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.2rem;
}

.lecture-card h3 {
    margin-bottom: 15px;
    padding-right: 50px;
    color: var(--primary-color);
}

.lecture-card p {
    margin-bottom: 20px;
    color: #666;
}

.lecture-links {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.lecture-links a {
    background-color: #f0f0f0;
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 0.9rem;
    color: var(--primary-color);
    transition: var(--transition);
}

.lecture-links a:hover {
    background-color: var(--secondary-color);
    color: var(--light-text);
}

/* Simulations Section */
.simulations-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.simulation-card {
    background-color: #fff;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
}

.simulation-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
}

.simulation-card img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.simulation-card h3 {
    padding: 20px 20px 10px;
    color: var(--primary-color);
}

.simulation-card p {
    padding: 0 20px 20px;
    color: #666;
}

.simulation-card .btn {
    margin: 0 20px 20px;
}

/* Resources Section */
.resources-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.resource-card {
    background-color: #fff;
    border-radius: var(--border-radius);
    padding: 30px;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    text-align: center;
}

.resource-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
}

.resource-icon {
    font-size: 2.5rem;
    color: var(--secondary-color);
    margin-bottom: 20px;
}

.resource-card h3 {
    margin-bottom: 15px;
    color: var(--primary-color);
}

.resource-card p {
    margin-bottom: 20px;
    color: #666;
}

.resource-link {
    display: inline-block;
    padding: 8px 20px;
    background-color: #f0f0f0;
    border-radius: 20px;
    color: var(--primary-color);
    font-weight: 500;
    transition: var(--transition);
}

.resource-link:hover {
    background-color: var(--secondary-color);
    color: var(--light-text);
}

/* About Section */
.about-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 50px;
}

.about-text p {
    margin-bottom: 20px;
    color: #555;
}

.about-text h3 {
    margin: 30px 0 15px;
    color: var(--primary-color);
}

.about-text ul {
    margin-left: 20px;
}

.about-text ul li {
    margin-bottom: 10px;
    position: relative;
    padding-left: 25px;
}

.about-text ul li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--secondary-color);
    font-weight: bold;
}

.instructor-info {
    background-color: #fff;
    border-radius: var(--border-radius);
    padding: 30px;
    box-shadow: var(--box-shadow);
    text-align: center;
}

.instructor-image {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    object-fit: cover;
    margin-bottom: 20px;
    border: 5px solid #f0f0f0;
}

.instructor-info h3 {
    margin-bottom: 15px;
    color: var(--primary-color);
}

.instructor-info p {
    margin-bottom: 10px;
    color: #666;
}

/* Footer */
footer {
    background-color: var(--primary-color);
    color: var(--light-text);
    padding: 60px 0 30px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
}

.footer-logo h2 {
    font-size: 1.5rem;
    margin-bottom: 15px;
}

.footer-logo p {
    opacity: 0.7;
}

.footer-links h3,
.footer-contact h3 {
    font-size: 1.2rem;
    margin-bottom: 20px;
    position: relative;
    display: inline-block;
}

.footer-links h3::after,
.footer-contact h3::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 40px;
    height: 3px;
    background-color: var(--secondary-color);
}

.footer-links ul li {
    margin-bottom: 10px;
}

.footer-links ul li a {
    color: var(--light-text);
    opacity: 0.7;
    transition: var(--transition);
}

.footer-links ul li a:hover {
    opacity: 1;
    padding-left: 5px;
}

.footer-contact p {
    margin-bottom: 15px;
    opacity: 0.7;
}

.footer-contact i {
    margin-right: 10px;
    color: var(--secondary-color);
}

/* Responsive Styles */
@media (max-width: 992px) {
    .about-content {
        grid-template-columns: 1fr;
    }
    
    .instructor-info {
        max-width: 400px;
        margin: 0 auto;
    }
}

@media (max-width: 768px) {
    header .container {
        flex-direction: column;
    }
    
    header h1 {
        margin-bottom: 15px;
    }
    
    nav ul {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    nav ul li {
        margin: 5px 10px;
    }
    
    .hero {
        padding: 100px 0;
    }
    
    .hero h2 {
        font-size: 2.2rem;
    }
    
    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .section {
        padding: 60px 0;
    }
    
    .section-title {
        font-size: 2rem;
    }
}

@media (max-width: 576px) {
    .hero h2 {
        font-size: 1.8rem;
    }
    
    .course-card, 
    .lecture-card, 
    .simulation-card, 
    .resource-card {
        padding: 20px;
    }
    
    .footer-content {
        text-align: center;
    }
    
    .footer-links h3::after,
    .footer-contact h3::after {
        left: 50%;
        transform: translateX(-50%);
    }
}