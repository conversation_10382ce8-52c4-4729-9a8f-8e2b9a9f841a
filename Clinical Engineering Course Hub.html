<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clinical Engineering Course Hub | Nahda College</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;700;900&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary: #003366;
            --secondary: #00A79D;
            --accent: #E63946;
            --light: #F1FAEE;
            --dark: #1D3557;
        }
        
        body {
            font-family: '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', sans-serif;
            transition: all 0.3s ease;
        }
        
        body.rtl {
            direction: rtl;
            font-family: 'Tajawal', '<PERSON>serrat', sans-serif;
        }
        
        .hero-bg {
            background-image: linear-gradient(rgba(0, 51, 102, 0.8), rgba(0, 167, 157, 0.8)), url('https://images.unsplash.com/photo-1576091160550-2173dba999ef?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80');
            background-size: cover;
            background-position: center;
            height: 70vh;
        }
        
        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        .nav-link::after {
            content: '';
            display: block;
            width: 0;
            height: 2px;
            background: var(--secondary);
            transition: width .3s;
        }
        
        .nav-link:hover::after {
            width: 100%;
        }
        
        .lang-switch {
            position: relative;
            display: inline-block;
            width: 80px;
            height: 34px;
        }
        
        .lang-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: var(--primary);
            transition: .4s;
            border-radius: 34px;
        }
        
        .lang-slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        
        input:checked + .lang-slider {
            background-color: var(--accent);
        }
        
        input:checked + .lang-slider:before {
            transform: translateX(46px);
        }
        
        .week-content {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.4s ease-out;
        }
        
        .week-content.show {
            max-height: 1000px;
        }
        
        @media (max-width: 768px) {
            .mobile-menu {
                position: fixed;
                top: 0;
                right: 0;
                height: 100vh;
                width: 70%;
                background-color: var(--primary);
                z-index: 40;
                transform: translateX(100%);
                transition: transform 0.3s ease-out;
            }
            
            .mobile-menu.show {
                transform: translateX(0);
            }
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Header Section -->
    <header class="fixed w-full bg-white shadow-md z-30">
        <div class="container mx-auto px-4 py-3 flex justify-between items-center">
            <div class="flex items-center space-x-2">
                <div class="w-10 h-10 bg-primary rounded-full flex items-center justify-center">
                    <i class="fas fa-heartbeat text-white text-xl"></i>
                </div>
                <h1 class="text-xl font-bold text-primary" id="site-title">Clinical Engineering Hub</h1>
            </div>
            
            <div class="hidden md:flex items-center space-x-8" id="desktop-nav">
                <a href="#" class="nav-link text-gray-700 hover:text-primary" data-page="home">Home</a>
                <a href="#" class="nav-link text-gray-700 hover:text-primary" data-page="outline">Course Outline</a>
                <a href="#" class="nav-link text-gray-700 hover:text-primary" data-page="resources">Resources</a>
                <a href="#" class="nav-link text-gray-700 hover:text-primary" data-page="instructor">Instructor</a>
                
                <div class="flex items-center">
                    <label for="language-toggle" class="mr-2 text-gray-700" id="lang-label">English</label>
                    <label class="lang-switch">
                        <input type="checkbox" id="language-toggle">
                        <span class="lang-slider"></span>
                    </label>
                </div>
            </div>
            
            <button id="mobile-menu-btn" class="md:hidden text-primary focus:outline-none">
                <i class="fas fa-bars text-2xl"></i>
            </button>
        </div>
    </header>

    <!-- Mobile Menu -->
    <div id="mobile-menu" class="mobile-menu md:hidden p-4 pt-24">
        <div class="flex flex-col space-y-6">
            <a href="#" class="text-white hover:text-accent text-lg" data-page="home">Home</a>
            <a href="#" class="text-white hover:text-accent text-lg" data-page="outline">Course Outline</a>
            <a href="#" class="text-white hover:text-accent text-lg" data-page="resources">Resources</a>
            <a href="#" class="text-white hover:text-accent text-lg" data-page="instructor">Instructor</a>
            
            <div class="flex items-center justify-center mt-4">
                <label for="mobile-language-toggle" class="mr-2 text-white" id="mobile-lang-label">English</label>
                <label class="lang-switch">
                    <input type="checkbox" id="mobile-language-toggle">
                    <span class="lang-slider"></span>
                </label>
            </div>
        </div>
    </div>

    <!-- Main Content Area -->
    <main id="app-content" class="pt-20 min-h-screen">
        <!-- Home Page (Default) -->
        <section id="home-page" class="fade-in">
            <!-- Hero Section -->
            <div class="hero-bg flex flex-col items-center justify-center text-center text-white">
                <div class="container mx-auto px-4 py-20">
                    <h1 class="text-4xl md:text-5xl font-bold mb-4" id="hero-title">Bridging Engineering Innovation with Clinical Practice</h1>
                    <p class="text-xl md:text-2xl mb-8" id="hero-subtitle">BME-305: Clinical Engineering Principles, Applications, and Management</p>
                    <button class="bg-accent hover:bg-red-600 text-white font-bold py-3 px-6 rounded-lg transition duration-300 shadow-lg" id="explore-btn">Explore Course</button>
                </div>
            </div>

            <!-- Features Section -->
            <div class="container mx-auto px-4 py-16">
                <h2 class="text-3xl font-bold text-center mb-12 text-primary" id="features-title">Course Highlights</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                    <div class="bg-white p-6 rounded-lg shadow-lg card-hover transition duration-300">
                        <div class="w-16 h-16 bg-primary rounded-full flex items-center justify-center mb-4 mx-auto">
                            <i class="fas fa-shield-alt text-white text-2xl"></i>
                        </div>
                        <h3 class="text-xl font-bold mb-3 text-center text-primary" id="feature1-title">Patient Safety</h3>
                        <p class="text-gray-600 text-center" id="feature1-text">Safeguarding patients through proper medical device management and risk assessment techniques.</p>
                    </div>
                    
                    <div class="bg-white p-6 rounded-lg shadow-lg card-hover transition duration-300">
                        <div class="w-16 h-16 bg-primary rounded-full flex items-center justify-center mb-4 mx-auto">
                            <i class="fas fa-cogs text-white text-2xl"></i>
                        </div>
                        <h3 class="text-xl font-bold mb-3 text-center text-primary" id="feature2-title">Technology Management</h3>
                        <p class="text-gray-600 text-center" id="feature2-text">Master the complete lifecycle of medical equipment from procurement to decommissioning.</p>
                    </div>
                    
                    <div class="bg-white p-6 rounded-lg shadow-lg card-hover transition duration-300">
                        <div class="w-16 h-16 bg-primary rounded-full flex items-center justify-center mb-4 mx-auto">
                            <i class="fas fa-lightbulb text-white text-2xl"></i>
                        </div>
                        <h3 class="text-xl font-bold mb-3 text-center text-primary" id="feature3-title">Innovative Solutions</h3>
                        <p class="text-gray-600 text-center" id="feature3-text">Develop creative approaches to bridge clinical needs with technological possibilities.</p>
                    </div>
                </div>
            </div>

            <!-- Course Overview -->
            <div class="bg-gray-100 py-16">
                <div class="container mx-auto px-4">
                    <div class="md:flex items-center">
                        <div class="md:w-1/2 mb-8 md:mb-0 md:pr-8">
                            <h2 class="text-3xl font-bold mb-4 text-primary" id="overview-title">About the Course</h2>
                            <p class="text-gray-600 mb-4" id="overview-text1">This course provides a comprehensive introduction to clinical engineering principles, covering the application of engineering concepts to healthcare technology management.</p>
                            <p class="text-gray-600 mb-6" id="overview-text2">Students will gain practical knowledge through case studies, hands-on activities, and interactive discussions on real-world clinical engineering challenges.</p>
                            <button class="bg-primary hover:bg-blue-800 text-white font-bold py-2 px-4 rounded transition duration-300" id="outline-btn">View Course Outline</button>
                        </div>
                        <div class="md:w-1/2">
                            <img src="https://images.unsplash.com/photo-1579684385127-1ef15d508118?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80" 
                                 alt="Medical Technology" 
                                 class="rounded-lg shadow-xl w-full h-auto">
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Course Outline Page (Hidden by default) -->
        <section id="outline-page" class="hidden container mx-auto px-4 py-12">
            <h1 class="text-3xl font-bold mb-8 text-center text-primary" id="outline-page-title">Course Outline</h1>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6" id="weeks-container">
                <!-- Weeks will be populated by JavaScript -->
            </div>
        </section>

        <!-- Resources Page (Hidden by default) -->
        <section id="resources-page" class="hidden container mx-auto px-4 py-12">
            <h1 class="text-3xl font-bold mb-8 text-center text-primary" id="resources-title">Course Resources</h1>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
                <div class="bg-white p-6 rounded-lg shadow-md">
                    <div class="flex items-center mb-4">
                        <i class="fas fa-book text-primary text-2xl mr-3"></i>
                        <h3 class="text-xl font-bold text-primary" id="textbook-title">Textbooks</h3>
                    </div>
                    <ul class="space-y-2" id="textbooks-list">
                        <li class="flex items-start">
                            <i class="fas fa-circle text-secondary text-xs mt-1 mr-2"></i>
                            <span>Clinical Engineering Handbook, 2nd Edition (2020)</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-circle text-secondary text-xs mt-1 mr-2"></i>
                            <span>Medical Device Technologies: A Systems Approach (2016)</span>
                        </li>
                    </ul>
                </div>
                
                <div class="bg-white p-6 rounded-lg shadow-md">
                    <div class="flex items-center mb-4">
                        <i class="fas fa-file-alt text-primary text-2xl mr-3"></i>
                        <h3 class="text-xl font-bold text-primary" id="research-title">Research Papers</h3>
                    </div>
                    <ul class="space-y-2" id="papers-list">
                        <li class="flex items-start">
                            <i class="fas fa-circle text-secondary text-xs mt-1 mr-2"></i>
                            <span>Clinical Engineering in Healthcare Organizations (2019)</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-circle text-secondary text-xs mt-1 mr-2"></i>
                            <span>Risk Management in Medical Technology (2021)</span>
                        </li>
                    </ul>
                </div>
                
                <div class="bg-white p-6 rounded-lg shadow-md">
                    <div class="flex items-center mb-4">
                        <i class="fas fa-link text-primary text-2xl mr-3"></i>
                        <h3 class="text-xl font-bold text-primary" id="links-title">Useful Links</h3>
                    </div>
                    <ul class="space-y-2" id="links-list">
                        <li class="flex items-start">
                            <i class="fas fa-circle text-secondary text-xs mt-1 mr-2"></i>
                            <a href="#" class="hover:underline text-blue-600">AAMI Standards Hub</a>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-circle text-secondary text-xs mt-1 mr-2"></i>
                            <a href="#" class="hover:underline text-blue-600">WHO Medical Device Guidelines</a>
                        </li>
                    </ul>
                </div>
            </div>
            
            <div class="bg-primary bg-opacity-10 p-6 rounded-lg">
                <h3 class="text-xl font-bold mb-4 text-primary" id="downloads-title">Course Materials</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4" id="downloads-list">
                    <div class="bg-white p-4 rounded-lg shadow-sm flex items-center justify-between">
                        <div class="flex items-center">
                            <i class="fas fa-file-pdf text-red-500 text-2xl mr-3"></i>
                            <span>Syllabus Document</span>
                        </div>
                        <button class="bg-secondary hover:bg-teal-600 text-white px-3 py-1 rounded text-sm">Download</button>
                    </div>
                    <div class="bg-white p-4 rounded-lg shadow-sm flex items-center justify-between">
                        <div class="flex items-center">
                            <i class="fas fa-file-excel text-green-600 text-2xl mr-3"></i>
                            <span>Assessment Schedule</span>
                        </div>
                        <button class="bg-secondary hover:bg-teal-600 text-white px-3 py-1 rounded text-sm">Download</button>
                    </div>
                </div>
            </div>
        </section>

        <!-- Instructor Page (Hidden by default) -->
        <section id="instructor-page" class="hidden container mx-auto px-4 py-12">
            <h1 class="text-3xl font-bold mb-8 text-center text-primary" id="instructor-title">About the Instructor</h1>
            
            <div class="flex flex-col md:flex-row items-center gap-8">
                <div class="md:w-1/3">
                    <img src="https://images.unsplash.com/photo-1633332755192-727a05c4013d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=880&q=80" 
                         alt="Dr. Mohammed Yagoub Esmail" 
                         class="rounded-full w-64 h-64 object-cover mx-auto shadow-lg">
                </div>
                
                <div class="md:w-2/3">
                    <h2 class="text-2xl font-bold mb-2 text-primary">Dr. Mohammed Yagoub Esmail</h2>
                    <p class="text-secondary font-medium mb-4">Professor of Biomedical Engineering</p>
                    
                    <div class="bg-gray-100 p-4 rounded-lg mb-6">
                        <p class="mb-4" id="bio-text">
                            Dr. Esmail has over 15 years of experience in clinical engineering education and practice. 
                            He holds a Ph.D. in Biomedical Engineering with specialization in Healthcare Technology Management. 
                            His research focuses on medical device safety and healthcare technology assessment.
                        </p>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <h4 class="font-bold mb-2" id="education-title">Education</h4>
                                <ul class="space-y-1">
                                    <li class="flex items-start">
                                        <i class="fas fa-graduation-cap text-primary mr-2 mt-1"></i>
                                        <span>Ph.D. Biomedical Engineering - University of XYZ</span>
                                    </li>
                                    <li class="flex items-start">
                                        <i class="fas fa-graduation-cap text-primary mr-2 mt-1"></i>
                                        <span>M.Sc. Clinical Engineering - University of ABC</span>
                                    </li>
                                </ul>
                            </div>
                            
                            <div>
                                <h4 class="font-bold mb-2" id="affiliation-title">Affiliation</h4>
                                <ul class="space-y-1">
                                    <li class="flex items-start">
                                        <i class="fas fa-university text-primary mr-2 mt-1"></i>
                                        <span>Nahda College</span>
                                    </li>
                                    <li class="flex items-start">
                                        <i class="fas fa-university text-primary mr-2 mt-1"></i>
                                        <span>SUST - Biomedical Engineering</span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div>
                        <h4 class="font-bold mb-2" id="contact-title">Contact Information</h4>
                        <div class="flex flex-wrap gap-4">
                            <a href="tel:+249912867327" class="flex items-center text-gray-700 hover:text-primary">
                                <i class="fas fa-phone-alt mr-2"></i>
                                <span>+249 912 867 327</span>
                            </a>
                            <a href="mailto:<EMAIL>" class="flex items-center text-gray-700 hover:text-primary">
                                <i class="fas fa-envelope mr-2"></i>
                                <span><EMAIL></span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer Section -->
    <footer class="bg-primary text-white py-8">
        <div class="container mx-auto px-4">
            <div class="md:flex md:justify-between md:items-center">
                <div class="mb-4 md:mb-0">
                    <h3 class="text-xl font-bold mb-2">Clinical Engineering Hub</h3>
                    <p class="opacity-80" id="footer-text">Bridging engineering innovation with clinical practice for better healthcare outcomes.</p>
                </div>
                
                <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div>
                        <h4 class="font-bold mb-2" id="quick-links-title">Quick Links</h4>
                        <ul class="space-y-1">
                            <li><a href="#" class="hover:underline opacity-80 hover:opacity-100" data-page="home">Home</a></li>
                            <li><a href="#" class="hover:underline opacity-80 hover:opacity-100" data-page="outline">Course Outline</a></li>
                            <li><a href="#" class="hover:underline opacity-80 hover:opacity-100" data-page="resources">Resources</a></li>
                        </ul>
                    </div>
                    
                    <div>
                        <h4 class="font-bold mb-2" id="connect-title">Connect</h4>
                        <div class="flex space-x-4 mb-2">
                            <a href="#" class="hover:text-secondary"><i class="fab fa-twitter text-xl"></i></a>
                            <a href="#" class="hover:text-secondary"><i class="fab fa-linkedin text-xl"></i></a>
                            <a href="#" class="hover:text-secondary"><i class="fab fa-researchgate text-xl"></i></a>
                        </div>
                        <p class="opacity-80" id="copyright">© 2025 Dr. Mohammed Yagoub Esmail | Nahda College</p>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <script>
        // Course Data
        const courseData = [
            {
                week: 1,
                title: {
                    en: "Introduction to Clinical Engineering",
                    ar: "مقدمة في الهندسة السريرية"
                },
                content: {
                    en: "Definition, history, roles, and healthcare technology overview. Interactive discussion on clinical engineering significance.",
                    ar: "تعريف، تاريخ، أدوار ونظرة عامة على تكنولوجيا الرعاية الصحية. مناقشة تفاعلية حول أهمية الهندسة السريرية."
                },
                activity: {
                    en: "Case study analysis: Impact of clinical engineering on patient safety",
                    ar: "تحليل دراسة حالة: أثر الهندسة السريرية على سلامة المرضى"
                }
            },
            {
                week: 2,
                title: {
                    en: "Medical Equipment Lifecycle Management",
                    ar: "إدارة دورة حياة المعدات الطبية"
                },
                content: {
                    en: "Procurement strategies, installation best practices, preventive maintenance schedules, repair processes, and disposal protocols.",
                    ar: "استراتيجيات المشتريات، أفضل ممارسات التثبيت، جداول الصيانة الوقائية، عمليات الإصلاح وبروتوكولات التخلص."
                },
                activity: {
                    en: "Hands-on workshop: Creating a PM schedule for different equipment categories",
                    ar: "ورشة عمل عملية: إنشاء جدول صيانة وقائية لأنواع مختلفة من المعدات"
                }
            },
            {
                week: 3,
                title: {
                    en: "Risk Management & Patient Safety",
                    ar: "إدارة المخاطر وسلامة المرضى"
                },
                content: {
                    en: "Systems approach to patient safety, medical device risk assessment methodologies, failure modes and effects analysis (FMEA).",
                    ar: "نهج النظم لسلامة المرضى، منهجيات تقييم مخاطر الأجهزة الطبية، تحليل أنماط الفشل وآثاره (FMEA)."
                },
                activity: {
                    en: "Group project: Analyzing failure scenarios in medical devices",
                    ar: "مشروع جماعي: تحليل سيناريوهات الفشل في الأجهزة الطبية"
                }
            },
            {
                week: 4,
                title: {
                    en: "Technology Assessment Methods",
                    ar: "طرق تقييم التكنولوجيا"
                },
                content: {
                    en: "Cost-benefit analysis, clinical utility assessment, technology adoption frameworks, and decision-making processes.",
                    ar: "تحليل التكلفة والفائدة، تقييم الفائدة السريرية، أطر اعتماد التكنولوجيا وعمليات اتخاذ القرار."
                },
                activity: {
                    en: "Comparative analysis of two medical technologies",
                    ar: "تحليل مقارن لتقنيتين طبيتين"
                }
            },
            {
                week: 5,
                title: {
                    en: "Facilities Design & Project Management",
                    ar: "تصميم المرافق وإدارة المشاريع"
                },
                content: {
                    en: "Design considerations for clinical spaces, project planning techniques in healthcare settings, interdisciplinary collaboration approaches.",
                    ar: "اعتبارات التصميم للمساحات السريرية، تقنيات تخطيط المشاريع في إعدادات الرعاية الصحية، أساليب التعاون متعدد التخصصات."
                },
                activity: {
                    en: "Workshop: Designing an optimal ICU layout",
                    ar: "ورشة عمل: تصميم تخطيط مثالي للعناية المركزة"
                }
            },
            {
                week: 6,
                title: {
                    en: "Regulatory Standards & Compliance",
                    ar: "المعايير التنظيمية والامتثال"
                },
                content: {
                    en: "Overview of global medical device regulations, FDA/CE/MHRA requirements, quality management systems in healthcare.",
                    ar: "نظرة عامة على لوائح الأجهزة الطبية العالمية، متطلبات FDA/CE/MHRA، أنظمة إدارة الجودة في الرعاية الصحية."
                },
                activity: {
                    en: "Case study: Navigating regulatory approval for a new device",
                    ar: "دراسة حالة: التنقل في الموافقة التنظيمية لجهاز جديد"
                }
            },
            {
                week: 7,
                title: {
                    en: "Emerging Technologies in Healthcare",
                    ar: "التقنيات الناشئة في الرعاية الصحية"
                },
                content: {
                    en: "AI in medical devices, telemedicine platforms, wearable technologies, and their impact on clinical engineering practice.",
                    ar: "الذكاء الاصطناعي في الأجهزة الطبية، منصات التطبيب عن بعد، التقنيات القابلة للارتداء وتأثيرها على ممارسة الهندسة السريرية."
                },
                activity: {
                    en: "Technology showcase: Presenting innovative healthcare solutions",
                    ar: "عرض تكنولوجي: عرض حلول رعاية صحية مبتكرة"
                }
            },
            {
                week: 8,
                title: {
                    en: "Career Paths in Clinical Engineering",
                    ar: "مسارات الوظائف في الهندسة السريرية"
                },
                content: {
                    en: "Hospital-based roles, consulting opportunities, regulatory agency positions, research and academic career options.",
                    ar: "الأدوار القائمة على المستشفيات، فرص الاستشارات، وظائف الوكالات التنظيمية، خيارات الوظائف البحثية والأكاديمية."
                },
                activity: {
                    en: "Panel discussion with practicing clinical engineers",
                    ar: "مناقشة مجموعة مع مهندسين سريريين ممارسين"
                }
            }
        ];

        // Translation keys
        const translations = {
            en: {
                "site-title": "Clinical Engineering Hub",
                "lang-label": "English",
                "hero-title": "Bridging Engineering Innovation with Clinical Practice",
                "hero-subtitle": "BME-305: Clinical Engineering Principles, Applications, and Management",
                "explore-btn": "Explore Course",
                "features-title": "Course Highlights",
                "feature1-title": "Patient Safety",
                "feature1-text": "Safeguarding patients through proper medical device management and risk assessment techniques.",
                "feature2-title": "Technology Management",
                "feature2-text": "Master the complete lifecycle of medical equipment from procurement to decommissioning.",
                "feature3-title": "Innovative Solutions",
                "feature3-text": "Develop creative approaches to bridge clinical needs with technological possibilities.",
                "overview-title": "About the Course",
                "overview-text1": "This course provides a comprehensive introduction to clinical engineering principles, covering the application of engineering concepts to healthcare technology management.",
                "overview-text2": "Students will gain practical knowledge through case studies, hands-on activities, and interactive discussions on real-world clinical engineering challenges.",
                "outline-btn": "View Course Outline",
                "outline-page-title": "Course Outline",
                "expand-btn": "Show Details",
                "collapse-btn": "Hide Details",
                "resources-title": "Course Resources",
                "textbook-title": "Textbooks",
                "research-title": "Research Papers",
                "links-title": "Useful Links",
                "downloads-title": "Course Materials",
                "instructor-title": "About the Instructor",
                "bio-text": "Dr. Esmail has over 15 years of experience in clinical engineering education and practice. He holds a Ph.D. in Biomedical Engineering with specialization in Healthcare Technology Management. His research focuses on medical device safety and healthcare technology assessment.",
                "education-title": "Education",
                "affiliation-title": "Affiliation",
                "contact-title": "Contact Information",
                "footer-text": "Bridging engineering innovation with clinical practice for better healthcare outcomes.",
                "quick-links-title": "Quick Links",
                "connect-title": "Connect",
                "copyright": "© 2025 Dr. Mohammed Yagoub Esmail | Nahda College",
                "view-all": "View All Weeks",
                "hide-all": "Hide All Weeks"
            },
            ar: {
                "site-title": "مركز الهندسة السريرية",
                "lang-label": "العربية",
                "hero-title": "ربط الابتكار الهندسي بالممارسة السريرية",
                "hero-subtitle": "BME-305: مبادئ الهندسة السريرية، التطبيقات والإدارة",
                "explore-btn": "استكشاف الدورة",
                "features-title": "أبرز ملامح الدورة",
                "feature1-title": "سلامة المريض",
                "feature1-text": "حماية المرضى من خلال الإدارة الصحيحة للأجهزة الطبية وتقنيات تقييم المخاطر.",
                "feature2-title": "إدارة التكنولوجيا",
                "feature2-text": "إتقان دورة حياة كاملة للمعدات الطبية من التوريد إلى إيقاف التشغيل.",
                "feature3-title": "حلول مبتكرة",
                "feature3-text": "تطوير أساليب إبداعية لربط الاحتياجات السريرية بالإمكانيات التكنولوجية.",
                "overview-title": "حول الدورة",
                "overview-text1": "توفر هذه الدورة مقدمة شاملة لمبادئ الهندسة السريرية، وتغطي تطبيق المفاهيم الهندسية على إدارة تكنولوجيا الرعاية الصحية.",
                "overview-text2": "سيحصل الطلاب على معرفة عملية من خلال دراسات الحالة والأنشطة العملية والمناقشات التفاعلية حول تحديات الهندسة السريرية في العالم الحقيقي.",
                "outline-btn": "عرض محتوى الدورة",
                "outline-page-title": "محتوى الدورة",
                "expand-btn": "عرض التفاصيل",
                "collapse-btn": "إخفاء التفاصيل",
                "resources-title": "موارد الدورة",
                "textbook-title": "الكتب المدرسية",
                "research-title": "أوراق بحثية",
                "links-title": "روابط مفيدة",
                "downloads-title": "مواد الدورة",
                "instructor-title": "حول المحاضر",
                "bio-text": "لدى الدكتور إسماعيل أكثر من 15 عامًا من الخبرة في تعليم وممارسة الهندسة السريرية. حاصل على دكتوراه في الهندسة الطبية الحيوية مع تخصص في إدارة تكنولوجيا الرعاية الصحية. يركز بحثه على سلامة الأجهزة الطبية وتقييم تكنولوجيا الرعاية الصحية.",
                "education-title": "التعليم",
                "affiliation-title": "الانتماء",
                "contact-title": "معلومات الاتصال",
                "footer-text": "ربط الابتكار الهندسي بالممارسة السريرية لتحقيق نتائج رعاية صحية أفضل.",
                "quick-links-title": "روابط سريعة",
                "connect-title": "تواصل معنا",
                "copyright": "© 2025 الدكتور محمد يعقوب إسماعيل | كلية النهضة",
                "view-all": "عرض جميع الأسابيع",
                "hide-all": "إخفاء جميع الأسابيع"
            }
        };

        // Current language (runs after DOM is loaded)
        document.addEventListener('DOMContentLoaded', function() {
            let currentLang = 'en';
            
            // Get all elements that need translation
            const translateElements = document.querySelectorAll('[id]');
            
            // Function to update the language
            function updateLanguage(lang) {
                currentLang = lang;
                document.documentElement.lang = lang;
                document.documentElement.dir = lang === 'ar' ? 'rtl' : 'ltr';
                
                // Update the language toggle position
                document.getElementById('language-toggle').checked = lang === 'ar';
                document.getElementById('mobile-language-toggle').checked = lang === 'ar';
                
                // Update all translatable elements
                translateElements.forEach(element => {
                    const key = element.id;
                    if (translations[lang] && translations[lang][key]) {
                        if (element.tagName === 'INPUT' && element.type === 'button') {
                            element.value = translations[lang][key];
                        } else {
                            element.textContent = translations[lang][key];
                        }
                    }
                });
                
                // Update the weeks display if on outline page
                if (document.getElementById('outline-page').classList.contains('hidden') === false) {
                    renderCourseOutline();
                }
            }
            
            // Initial language setup
            updateLanguage(currentLang);
            
            // Language toggle functionality
            document.getElementById('language-toggle').addEventListener('change', function() {
                updateLanguage(this.checked ? 'ar' : 'en');
            });
            
            document.getElementById('mobile-language-toggle').addEventListener('change', function() {
                updateLanguage(this.checked ? 'ar' : 'en');
            });
            
            // Mobile menu toggle
            const mobileMenu = document.getElementById('mobile-menu');
            document.getElementById('mobile-menu-btn').addEventListener('click', function() {
                mobileMenu.classList.toggle('show');
            });
            
            // Close mobile menu when clicking on a link
            const mobileLinks = document.querySelectorAll('#mobile-menu a');
            mobileLinks.forEach(link => {
                link.addEventListener('click', function() {
                    mobileMenu.classList.remove('show');
                });
            });
            
            // Navigation between pages
            function showPage(pageId) {
                document.querySelectorAll('main section').forEach(section => {
                    section.classList.add('hidden');
                });
                document.getElementById(`${pageId}-page`).classList.remove('hidden');
                document.getElementById(`${pageId}-page`).classList.add('fade-in');
            }
            
            const navLinks = document.querySelectorAll('[data-page]');
            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const page = this.getAttribute('data-page');
                    showPage(page);
                });
            });
            
            // Buttons that navigate to pages
            document.getElementById('explore-btn').addEventListener('click', function() {
                showPage('outline');
            });
            
            document.getElementById('outline-btn').addEventListener('click', function() {
                showPage('outline');
            });
            
            // Function to render course outline
            function renderCourseOutline() {
                const weeksContainer = document.getElementById('weeks-container');
                weeksContainer.innerHTML = '';
                
                courseData.forEach(week => {
                    const weekCard = document.createElement('div');
                    weekCard.className = 'bg-white rounded-lg shadow-md overflow-hidden card-hover transition duration-300';
                    weekCard.innerHTML = `
                        <div class="p-6">
                            <div class="flex justify-between items-start">
                                <div>
                                    <h3 class="text-xl font-bold text-primary">${translations[currentLang]['week']} ${week.week}: ${week.title[currentLang]}</h3>
                                    <p class="text-gray-600 mt-2"><strong>${translations[currentLang]['content']}:</strong> ${week.content[currentLang]}</p>
                                </div>
                                <button class="expand-btn text-secondary font-bold ml-4 focus:outline-none" 
                                        data-week="${week.week}">
                                    ${translations[currentLang]['expand-btn']}
                                </button>
                            </div>
                            <div class="week-content mt-4" id="week-${week.week}-content">
                                <p class="text-gray-700"><strong>${translations[currentLang]['activity']}:</strong> ${week.activity[currentLang]}</p>
                            </div>
                        </div>
                    `;
                    weeksContainer.appendChild(weekCard);
                });
                
                // Add event listeners to expand/collapse buttons
                document.querySelectorAll('.expand-btn').forEach(btn => {
                    btn.addEventListener('click', function() {
                        const weekNum = this.getAttribute('data-week');
                        const content = document.getElementById(`week-${weekNum}-content`);
                        
                        if (content.classList.contains('show')) {
                            content.classList.remove('show');
                            this.textContent = translations[currentLang]['expand-btn'];
                        } else {
                            content.classList.add('show');
                            this.textContent = translations[currentLang]['collapse-btn'];
                        }
                    });
                });
                
                // Add view all/hide all button for weeks
                const viewAllBtn = document.createElement('button');
                viewAllBtn.className = 'bg-primary hover:bg-blue-800 text-white font-bold py-2 px-4 rounded transition duration-300 block mx-auto mt-6';
                viewAllBtn.id = 'toggle-all-weeks';
                viewAllBtn.textContent = translations[currentLang]['view-all'];
                weeksContainer.parentNode.insertBefore(viewAllBtn, weeksContainer.nextSibling);
                
                viewAllBtn.addEventListener('click', function() {
                    const allContents = document.querySelectorAll('.week-content');
                    const allExpanded = Array.from(allContents).every(content => content.classList.contains('show'));
                    
                    if (allExpanded) {
                        allContents.forEach(content => content.classList.remove('show'));
                        document.querySelectorAll('.expand-btn').forEach(btn => {
                            btn.textContent = translations[currentLang]['expand-btn'];
                        });
                        this.textContent = translations[currentLang]['view-all'];
                    } else {
                        allContents.forEach(content => content.classList.add('show'));
                        document.querySelectorAll('.expand-btn').forEach(btn => {
                            btn.textContent = translations[currentLang]['collapse-btn'];
                        });
                        this.textContent = translations[currentLang]['hide-all'];
                    }
                });
            }
            
            // Initialize the home page
            showPage('home');
        });
    </script>
</body>
</html>