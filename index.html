<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clinical Engineering and Simulation 2025</title>
    <link rel="stylesheet" href="CSS/main.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <header>
        <div class="container">
            <h1>Clinical Engineering and Simulation 2025</h1>
            <nav>
                <ul>
                    <li><a href="#" class="active">Home</a></li>
                    <li><a href="#lectures">Lectures</a></li>
                    <li><a href="#simulations">Simulations</a></li>
                    <li><a href="#resources">Resources</a></li>
                    <li><a href="#about">About</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Enhanced Quick Access Bar -->
    <div style="background: linear-gradient(90deg, #0056b3 0%, #00a0e9 100%); padding: 15px 0; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
        <div class="container">
            <div style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: 15px;">
                <div style="color: white; font-weight: bold; font-size: 1.1em;">
                    🎓 Quick Access Learning Resources:
                </div>
                <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                    <!-- Simulations -->
                    <a href="clinical_engineering_simulation.html"
                       style="background: rgba(255,255,255,0.2); color: white; padding: 8px 15px; border-radius: 20px; text-decoration: none; font-size: 0.9em; transition: all 0.3s ease;"
                       onmouseover="this.style.background='rgba(255,255,255,0.3)'"
                       onmouseout="this.style.background='rgba(255,255,255,0.2)'">
                        🚀 Simulations
                    </a>
                    <!-- Hands-on Learning -->
                    <a href="hands_on_learning_medical_devices.html"
                       style="background: rgba(255,255,255,0.2); color: white; padding: 8px 15px; border-radius: 20px; text-decoration: none; font-size: 0.9em; transition: all 0.3s ease;"
                       onmouseover="this.style.background='rgba(255,255,255,0.3)'"
                       onmouseout="this.style.background='rgba(255,255,255,0.2)'">
                        🎓 Hands-on Learning
                    </a>
                    <!-- Model Answers -->
                    <a href="model_answers_activities.html"
                       style="background: rgba(255,255,255,0.2); color: white; padding: 8px 15px; border-radius: 20px; text-decoration: none; font-size: 0.9em; transition: all 0.3s ease;"
                       onmouseover="this.style.background='rgba(255,255,255,0.3)'"
                       onmouseout="this.style.background='rgba(255,255,255,0.2)'">
                        📝 Model Answers
                    </a>
                    <!-- Dropdown for specific activities -->
                    <div style="position: relative; display: inline-block;">
                        <button onclick="toggleQuickMenu()" style="background: rgba(255,255,255,0.2); color: white; padding: 8px 15px; border-radius: 20px; border: none; font-size: 0.9em; cursor: pointer; transition: all 0.3s ease;"
                                onmouseover="this.style.background='rgba(255,255,255,0.3)'"
                                onmouseout="this.style.background='rgba(255,255,255,0.2)'">
                            📋 Quick Activities ▼
                        </button>
                        <div id="quickMenu" style="display: none; position: absolute; top: 100%; right: 0; background: white; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15); z-index: 1000; min-width: 200px; margin-top: 5px;">
                            <div style="padding: 10px;">
                                <div style="font-weight: bold; color: #0056b3; margin-bottom: 8px; font-size: 0.9em;">Popular Activities:</div>
                                <a href="model_answers_activities.html?lecture=1&activity=1" style="display: block; color: #495057; text-decoration: none; padding: 5px 0; font-size: 0.8em; border-bottom: 1px solid #f0f0f0;">• CE Role Definition</a>
                                <a href="model_answers_activities.html?lecture=4&activity=1" style="display: block; color: #495057; text-decoration: none; padding: 5px 0; font-size: 0.8em; border-bottom: 1px solid #f0f0f0;">• PM Program Design</a>
                                <a href="model_answers_activities.html?lecture=7&activity=2" style="display: block; color: #495057; text-decoration: none; padding: 5px 0; font-size: 0.8em; border-bottom: 1px solid #f0f0f0;">• GUM Uncertainty</a>
                                <a href="model_answers_activities.html?lecture=5&activity=1" style="display: block; color: #495057; text-decoration: none; padding: 5px 0; font-size: 0.8em;">• Risk Assessment</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <section class="hero">
        <div class="container">
            <div class="hero-content">
                <h2>Welcome to Clinical Engineering and Simulation</h2>
                <p>Explore the intersection of healthcare technology, engineering principles, and clinical practice through advanced interactive simulations with real-time data analysis and professional-grade instrumentation.</p>

                <!-- Enhanced Hero Features -->
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 30px 0; text-align: center;">
                    <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px; backdrop-filter: blur(10px);">
                        <div style="font-size: 2em; margin-bottom: 10px;">📊</div>
                        <h4 style="margin: 5px 0; color: white;">Real-time Analytics</h4>
                        <p style="margin: 0; font-size: 0.9em; color: rgba(255,255,255,0.8);">Live ECG, vital signs, statistical analysis</p>
                    </div>
                    <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px; backdrop-filter: blur(10px);">
                        <div style="font-size: 2em; margin-bottom: 10px;">⚙️</div>
                        <h4 style="margin: 5px 0; color: white;">Virtual Instruments</h4>
                        <p style="margin: 0; font-size: 0.9em; color: rgba(255,255,255,0.8);">Professional calibrators, test equipment</p>
                    </div>
                    <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px; backdrop-filter: blur(10px);">
                        <div style="font-size: 2em; margin-bottom: 10px;">🔬</div>
                        <h4 style="margin: 5px 0; color: white;">GUM Analysis</h4>
                        <p style="margin: 0; font-size: 0.9em; color: rgba(255,255,255,0.8);">Complete uncertainty calculations</p>
                    </div>
                    <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px; backdrop-filter: blur(10px);">
                        <div style="font-size: 2em; margin-bottom: 10px;">🔧</div>
                        <h4 style="margin: 5px 0; color: white;">Troubleshooting</h4>
                        <p style="margin: 0; font-size: 0.9em; color: rgba(255,255,255,0.8);">Systematic diagnostic procedures</p>
                    </div>
                </div>

                <div class="hero-buttons">
                    <a href="#lectures" class="btn primary">View 12 Lectures</a>
                    <a href="#simulations" class="btn secondary">Launch Simulations</a>
                    <a href="clinical_engineering_simulation.html" class="btn" style="background: rgba(255,255,255,0.2); color: white; border: 2px solid white;">Quick Start Demo</a>
                </div>
            </div>
        </div>
    </section>

    <section id="course-overview" class="section">
        <div class="container">
            <h2 class="section-title">Course Overview</h2>
            <div class="course-grid">
                <div class="course-card">
                    <div class="card-icon"><i class="fas fa-book-medical"></i></div>
                    <h3>Introduction to Clinical Engineering</h3>
                    <p>Learn the fundamentals of clinical engineering and its role in healthcare delivery.</p>
                </div>
                <div class="course-card">
                    <div class="card-icon"><i class="fas fa-heartbeat"></i></div>
                    <h3>Medical Equipment Lifecycle</h3>
                    <p>Understand the complete lifecycle of medical equipment from acquisition to retirement.</p>
                </div>
                <div class="course-card">
                    <div class="card-icon"><i class="fas fa-clipboard-check"></i></div>
                    <h3>Regulatory Standards</h3>
                    <p>Explore the regulatory framework governing medical devices and healthcare technology.</p>
                </div>
                <div class="course-card">
                    <div class="card-icon"><i class="fas fa-shield-alt"></i></div>
                    <h3>Risk Management</h3>
                    <p>Learn approaches to identify, assess, and mitigate risks associated with medical technology.</p>
                </div>
                <div class="course-card">
                    <div class="card-icon"><i class="fas fa-industry"></i></div>
                    <h3>Manufacturing & Lifecycle</h3>
                    <p>Explore medical device manufacturing processes and product lifecycle management.</p>
                </div>
                <div class="course-card">
                    <div class="card-icon"><i class="fas fa-chart-line"></i></div>
                    <h3>Technology Assessment</h3>
                    <p>Learn systematic approaches to evaluating healthcare technologies for effectiveness and value.</p>
                </div>
                <div class="course-card">
                    <div class="card-icon"><i class="fas fa-users"></i></div>
                    <h3>Management & Leadership</h3>
                    <p>Develop essential leadership and management skills for clinical engineering professionals.</p>
                </div>
                <div class="course-card">
                    <div class="card-icon"><i class="fas fa-rocket"></i></div>
                    <h3>Emerging Technologies</h3>
                    <p>Explore cutting-edge technologies shaping the future of healthcare and clinical engineering.</p>
                </div>
                <div class="course-card">
                    <div class="card-icon"><i class="fas fa-graduation-cap"></i></div>
                    <h3>Professional Development</h3>
                    <p>Build a successful career through continuous learning and professional growth strategies.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Content Switcher -->
    <div style="background: #f8f9fa; padding: 20px 0; border-bottom: 1px solid #dee2e6;">
        <div class="container">
            <div style="display: flex; justify-content: center; align-items: center; gap: 30px; flex-wrap: wrap;">
                <h3 style="margin: 0; color: #495057;">Choose Your Learning Path:</h3>
                <div style="display: flex; gap: 15px; flex-wrap: wrap;">
                    <button id="lectures-tab" class="content-tab active" onclick="switchContent('lectures')">
                        📚 Lectures & Theory
                    </button>
                    <button id="simulations-tab" class="content-tab" onclick="switchContent('simulations')">
                        🚀 Interactive Simulations
                    </button>
                    <button id="answers-tab" class="content-tab" onclick="switchContent('answers')">
                        📝 Model Answers
                    </button>
                </div>
                <div style="display: flex; gap: 10px; align-items: center; flex-wrap: wrap;">
                    <span style="font-size: 0.9em; color: #6c757d;">Quick Access:</span>
                    <button onclick="switchContent('lectures')" class="toggle-btn" style="background: linear-gradient(45deg, #0056b3, #00a0e9);">
                        📚 Lectures
                    </button>
                    <button onclick="switchContent('simulations')" class="toggle-btn" style="background: linear-gradient(45deg, #28a745, #20c997);">
                        🚀 Simulations
                    </button>
                    <button onclick="switchContent('answers')" class="toggle-btn" style="background: linear-gradient(45deg, #dc3545, #e91e63);">
                        📝 Answers
                    </button>
                </div>
            </div>

            <!-- Notification Banner -->
            <div id="notification-banner" style="background: linear-gradient(90deg, #28a745, #20c997); color: white; padding: 10px 0; text-align: center; display: none;">
                <div class="container">
                    <span id="notification-text">Welcome! Use the tabs above or keyboard shortcuts (Alt+L/Alt+S) to switch between content.</span>
                    <button onclick="hideNotification()" style="background: none; border: none; color: white; margin-left: 15px; cursor: pointer;">✕</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Lectures Content -->
    <div id="lectures-content" class="content-section">
        <section id="lectures" class="section">
        <div class="container">
            <h2 class="section-title">Course Lectures</h2>
            <div class="lectures-grid">
                <div class="lecture-card">
                    <div class="lecture-number">01</div>
                    <h3>Introduction to Clinical Engineering</h3>
                    <p>Fundamentals and core concepts of clinical engineering in healthcare settings.</p>
                    <div class="lecture-links">
                        <a href="lecture_one_introduction.html">Main Content</a>
                        <a href="lecture_one_detailed_notes.html">Detailed Notes</a>
                        <a href="lecture_one_student_activities.html">Activities</a>
                    </div>
                </div>
                <div class="lecture-card">
                    <div class="lecture-number">02</div>
                    <h3>Medical Equipment Lifecycle</h3>
                    <p>Understanding acquisition, maintenance, and retirement of medical equipment.</p>
                    <div class="lecture-links">
                        <a href="lecture_two_equipment_lifecycle.html">Main Content</a>
                        <a href="lecture_two_detailed_notes.html">Detailed Notes</a>
                        <a href="lecture_two_student_activities.html">Activities</a>
                    </div>
                </div>
                <div class="lecture-card">
                    <div class="lecture-number">03</div>
                    <h3>Regulatory Standards</h3>
                    <p>Exploring FDA, ISO, and other regulatory frameworks for medical devices.</p>
                    <div class="lecture-links">
                        <a href="lecture_three_regulatory_standards.html">Main Content</a>
                        <a href="lecture_three_detailed_notes.html">Detailed Notes</a>
                        <a href="lecture_three_student_activities.html">Activities</a>
                    </div>
                </div>
                <div class="lecture-card">
                    <div class="lecture-number">04</div>
                    <h3>Risk Management</h3>
                    <p>Approaches to identify, assess, and mitigate risks in healthcare technology.</p>
                    <div class="lecture-links">
                        <a href="lecture_four_risk_management.html">Main Content</a>
                        <a href="lecture_four_detailed_notes.html">Detailed Notes</a>
                        <a href="lecture_four_student_activities.html">Activities</a>
                    </div>
                </div>
                <div class="lecture-card">
                    <div class="lecture-number">05</div>
                    <h3>Clinical Systems Engineering & Interoperability</h3>
                    <p>Connecting healthcare technologies for seamless patient care.</p>
                    <div class="lecture-links">
                        <a href="lecture_five_systems_engineering.html">Main Content</a>
                        <a href="lecture_five_detailed_notes.html">Detailed Notes</a>
                        <a href="lecture_five_student_activities.html">Activities</a>
                    </div>
                </div>
                <div class="lecture-card">
                    <div class="lecture-number">06</div>
                    <h3>Medical Instrumentation & Diagnostics</h3>
                    <p>Understanding the principles and applications of diagnostic medical equipment.</p>
                    <div class="lecture-links">
                        <a href="lecture_six_medical_instrumentation.html">Main Content</a>
                        <a href="lecture_six_detailed_notes.html">Detailed Notes</a>
                        <a href="lecture_six_student_activities.html">Activities</a>
                    </div>
                </div>
                <div class="lecture-card">
                    <div class="lecture-number">07</div>
                    <h3>Therapeutic & Life Support Equipment</h3>
                    <p>Understanding the principles, applications, and management of therapeutic and life support technologies.</p>
                    <div class="lecture-links">
                        <a href="lecture_seven_therapeutic_equipment.html">Main Content</a>
                        <a href="lecture_seven_detailed_notes.html">Detailed Notes</a>
                        <a href="lecture_seven_student_activities.html">Activities</a>
                    </div>
                </div>
                <div class="lecture-card">
                    <div class="lecture-number">08</div>
                    <h3>Manufacturing Processes & Product Lifecycle</h3>
                    <p>Understanding medical device manufacturing, quality systems, and lifecycle management in clinical engineering.</p>
                    <div class="lecture-links">
                        <a href="lecture_eight_manufacturing_lifecycle.html">Main Content</a>
                        <a href="lecture_eight_detailed_notes.html">Detailed Notes</a>
                        <a href="lecture_eight_student_activities.html">Activities</a>
                    </div>
                </div>
                <div class="lecture-card">
                    <div class="lecture-number">09</div>
                    <h3>Healthcare Technology Assessment & Evaluation</h3>
                    <p>Systematic approaches to evaluating healthcare technologies for clinical and economic effectiveness.</p>
                    <div class="lecture-links">
                        <a href="lecture_nine_technology_assessment.html">Main Content</a>
                        <a href="lecture_nine_detailed_notes.html">Detailed Notes</a>
                        <a href="lecture_nine_student_activities.html">Activities</a>
                    </div>
                </div>
                <div class="lecture-card">
                    <div class="lecture-number">10</div>
                    <h3>Clinical Engineering Management & Leadership</h3>
                    <p>Developing leadership skills and management competencies for clinical engineering professionals.</p>
                    <div class="lecture-links">
                        <a href="lecture_ten_management_leadership.html">Main Content</a>
                        <a href="lecture_ten_detailed_notes.html">Detailed Notes</a>
                        <a href="lecture_ten_student_activities.html">Activities</a>
                    </div>
                </div>
                <div class="lecture-card">
                    <div class="lecture-number">11</div>
                    <h3>Emerging Technologies & Future Trends</h3>
                    <p>Exploring cutting-edge technologies and their impact on clinical engineering and healthcare delivery.</p>
                    <div class="lecture-links">
                        <a href="lecture_eleven_emerging_technologies.html">Main Content</a>
                        <a href="lecture_eleven_detailed_notes.html">Detailed Notes</a>
                        <a href="lecture_eleven_student_activities.html">Activities</a>
                    </div>
                </div>
                <div class="lecture-card">
                    <div class="lecture-number">12</div>
                    <h3>Professional Development & Career Pathways</h3>
                    <p>Building a successful career in clinical engineering through continuous learning and professional growth.</p>
                    <div class="lecture-links">
                        <a href="lecture_twelve_professional_development.html">Main Content</a>
                        <a href="lecture_twelve_detailed_notes.html">Detailed Notes</a>
                        <a href="lecture_twelve_student_activities.html">Activities</a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section id="simulations" class="section">
        <div class="container">
            <h2 class="section-title">Interactive Clinical Engineering Simulations</h2>
            <p class="section-description">Experience hands-on learning with our comprehensive clinical engineering simulation modules featuring advanced visual aids, real-time data analysis, and professional-grade instrumentation.</p>

            <!-- Simulation Features Overview -->
            <div style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); padding: 30px; border-radius: 12px; margin-bottom: 40px;">
                <h3 style="text-align: center; color: #0056b3; margin-bottom: 25px;">🚀 Advanced Simulation Features</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; text-align: center;">
                    <div style="padding: 15px;">
                        <div style="font-size: 2.5em; color: #0056b3; margin-bottom: 10px;">📊</div>
                        <h4 style="margin: 10px 0; color: #0056b3;">Real-time Data</h4>
                        <p style="margin: 0; font-size: 0.9em;">Live ECG waveforms, vital signs monitoring, and trend analysis</p>
                    </div>
                    <div style="padding: 15px;">
                        <div style="font-size: 2.5em; color: #28a745; margin-bottom: 10px;">⚙️</div>
                        <h4 style="margin: 10px 0; color: #0056b3;">Virtual Instruments</h4>
                        <p style="margin: 0; font-size: 0.9em;">Professional calibrators, multimeters, and test equipment</p>
                    </div>
                    <div style="padding: 15px;">
                        <div style="font-size: 2.5em; color: #ffc107; margin-bottom: 10px;">📈</div>
                        <h4 style="margin: 10px 0; color: #0056b3;">Statistical Analysis</h4>
                        <p style="margin: 0; font-size: 0.9em;">GUM uncertainty analysis, accuracy assessment, and reporting</p>
                    </div>
                    <div style="padding: 15px;">
                        <div style="font-size: 2.5em; color: #dc3545; margin-bottom: 10px;">🔧</div>
                        <h4 style="margin: 10px 0; color: #0056b3;">Troubleshooting</h4>
                        <p style="margin: 0; font-size: 0.9em;">Systematic diagnostic procedures and root cause analysis</p>
                    </div>
                </div>
            </div>

            <div class="simulations-grid">
                <div class="simulation-card" style="position: relative; overflow: hidden;">
                    <div style="background: linear-gradient(45deg, #000 0%, #003300 100%); height: 200px; position: relative; display: flex; align-items: center; justify-content: center;">
                        <div style="color: #00ff00; font-family: 'Courier New', monospace; text-align: center;">
                            <div style="font-size: 1.2em; margin-bottom: 10px;">📺 PATIENT MONITOR</div>
                            <div style="font-size: 0.9em;">HR: 75 bpm | BP: 120/80 | SpO2: 98%</div>
                            <div style="width: 150px; height: 2px; background: #00ff00; margin: 10px auto; animation: pulse 1s infinite;"></div>
                        </div>
                    </div>
                    <h3>Medical Device Operation</h3>
                    <p><strong>Enhanced Features:</strong> Real-time ECG waveforms, multi-parameter monitoring, alarm systems, and patient scenario simulation with pathological conditions.</p>
                    <div style="margin: 10px 0; font-size: 0.9em; color: #6c757d;">
                        <span style="background: #e3f2fd; padding: 2px 6px; border-radius: 3px; margin-right: 5px;">ECG Simulation</span>
                        <span style="background: #f3e5f5; padding: 2px 6px; border-radius: 3px; margin-right: 5px;">Vital Signs</span>
                        <span style="background: #e8f5e8; padding: 2px 6px; border-radius: 3px;">Alarm Management</span>
                    </div>
                    <a href="clinical_engineering_simulation.html" class="btn primary">Launch Simulation</a>
                </div>

                <div class="simulation-card" style="position: relative; overflow: hidden;">
                    <div style="background: linear-gradient(45deg, #2c3e50 0%, #34495e 100%); height: 200px; position: relative; display: flex; align-items: center; justify-content: center;">
                        <div style="color: #00ff00; font-family: 'Courier New', monospace; text-align: center;">
                            <div style="font-size: 1.2em; margin-bottom: 10px;">⚙️ CALIBRATOR</div>
                            <div style="font-size: 1.5em; font-weight: bold;">100.0 mmHg</div>
                            <div style="font-size: 0.8em; margin-top: 5px;">REF STD | ±0.1 mmHg</div>
                        </div>
                    </div>
                    <h3>Calibration Laboratory</h3>
                    <p><strong>Enhanced Features:</strong> Step-by-step procedures, virtual reference standards, automated error calculations, and comprehensive documentation with pass/fail criteria.</p>
                    <div style="margin: 10px 0; font-size: 0.9em; color: #6c757d;">
                        <span style="background: #fff3e0; padding: 2px 6px; border-radius: 3px; margin-right: 5px;">Pressure Cal</span>
                        <span style="background: #fce4ec; padding: 2px 6px; border-radius: 3px; margin-right: 5px;">Temp Cal</span>
                        <span style="background: #e0f2f1; padding: 2px 6px; border-radius: 3px;">Safety Testing</span>
                    </div>
                    <a href="clinical_engineering_simulation.html#calibration" class="btn primary">Start Calibration</a>
                </div>

                <div class="simulation-card" style="position: relative; overflow: hidden;">
                    <div style="background: linear-gradient(45deg, #1a237e 0%, #3949ab 100%); height: 200px; position: relative; display: flex; align-items: center; justify-content: center;">
                        <div style="color: white; text-align: center;">
                            <div style="font-size: 1.2em; margin-bottom: 10px;">📊 STATISTICS</div>
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; font-size: 0.8em;">
                                <div>Mean: ±0.15</div>
                                <div>σ: ±0.23</div>
                                <div>CI: 95%</div>
                                <div>U: ±0.46</div>
                            </div>
                        </div>
                    </div>
                    <h3>Measurement & Analysis</h3>
                    <p><strong>Enhanced Features:</strong> GUM uncertainty analysis, statistical calculations, Bland-Altman plots, confidence intervals, and professional reporting capabilities.</p>
                    <div style="margin: 10px 0; font-size: 0.9em; color: #6c757d;">
                        <span style="background: #e8eaf6; padding: 2px 6px; border-radius: 3px; margin-right: 5px;">GUM Analysis</span>
                        <span style="background: #f3e5f5; padding: 2px 6px; border-radius: 3px; margin-right: 5px;">Statistics</span>
                        <span style="background: #e0f7fa; padding: 2px 6px; border-radius: 3px;">Reporting</span>
                    </div>
                    <a href="clinical_engineering_simulation.html#measurements" class="btn primary">Begin Measurements</a>
                </div>

                <div class="simulation-card" style="position: relative; overflow: hidden;">
                    <div style="background: linear-gradient(45deg, #b71c1c 0%, #d32f2f 100%); height: 200px; position: relative; display: flex; align-items: center; justify-content: center;">
                        <div style="color: white; text-align: center;">
                            <div style="font-size: 1.2em; margin-bottom: 10px;">🚨 ERROR E02</div>
                            <div style="font-size: 0.9em; margin-bottom: 10px;">BP Monitor Fault</div>
                            <div style="font-size: 0.8em;">Systematic Diagnosis Required</div>
                        </div>
                    </div>
                    <h3>Troubleshooting Scenarios</h3>
                    <p><strong>Enhanced Features:</strong> Real-world equipment failures, systematic diagnostic procedures, root cause analysis, CAPA documentation, and preventive measures.</p>
                    <div style="margin: 10px 0; font-size: 0.9em; color: #6c757d;">
                        <span style="background: #ffebee; padding: 2px 6px; border-radius: 3px; margin-right: 5px;">Diagnostics</span>
                        <span style="background: #fff8e1; padding: 2px 6px; border-radius: 3px; margin-right: 5px;">Root Cause</span>
                        <span style="background: #f1f8e9; padding: 2px 6px; border-radius: 3px;">CAPA</span>
                    </div>
                    <a href="clinical_engineering_simulation.html#troubleshooting" class="btn primary">Start Troubleshooting</a>
                </div>

                <div class="simulation-card" style="position: relative; overflow: hidden;">
                    <div style="background: linear-gradient(45deg, #4caf50 0%, #66bb6a 100%); height: 200px; position: relative; display: flex; align-items: center; justify-content: center;">
                        <div style="color: white; text-align: center;">
                            <div style="font-size: 1.2em; margin-bottom: 10px;">🎓 HANDS-ON TRAINING</div>
                            <div style="font-size: 0.9em; margin-bottom: 10px;">Practical Exercises</div>
                            <div style="font-size: 0.8em;">Step-by-step Learning</div>
                        </div>
                    </div>
                    <h3>Hands-on Learning</h3>
                    <p><strong>Enhanced Features:</strong> Comprehensive practical training with interactive exercises, real device operation procedures, and competency assessment.</p>
                    <div style="margin: 10px 0; font-size: 0.9em; color: #6c757d;">
                        <span style="background: #e8f5e9; padding: 2px 6px; border-radius: 3px; margin-right: 5px;">Device Operation</span>
                        <span style="background: #e3f2fd; padding: 2px 6px; border-radius: 3px; margin-right: 5px;">Measurements</span>
                        <span style="background: #fff3e0; padding: 2px 6px; border-radius: 3px;">Calibration</span>
                    </div>
                    <a href="hands_on_learning_medical_devices.html" class="btn primary">Start Training</a>
                </div>
            </div>

            <!-- Enhanced Learning Objectives Section -->
            <div style="margin-top: 40px;">
                <div style="text-align: center; padding: 30px; background: linear-gradient(135deg, #0056b3 0%, #00a0e9 100%); border-radius: 12px; color: white; margin-bottom: 30px;">
                    <h3 style="margin-bottom: 15px; font-size: 1.8em;">🎯 Comprehensive Learning Objectives</h3>
                    <p style="margin-bottom: 20px; font-size: 1.1em;">Master clinical engineering skills through interactive simulations with advanced visual aids and real-world scenarios</p>
                </div>

                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 25px;">
                    <div style="background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); border-left: 4px solid #0056b3;">
                        <h4 style="color: #0056b3; margin-bottom: 15px; display: flex; align-items: center;">
                            <span style="font-size: 1.5em; margin-right: 10px;">🔧</span>
                            Device Operation Mastery
                        </h4>
                        <ul style="margin: 0; padding-left: 20px; line-height: 1.6;">
                            <li><strong>Real-time Monitoring:</strong> ECG waveforms, vital signs, alarms</li>
                            <li><strong>Multi-parameter Systems:</strong> Patient monitors, ventilators</li>
                            <li><strong>Safety Protocols:</strong> Equipment operation procedures</li>
                            <li><strong>Data Interpretation:</strong> Clinical parameter analysis</li>
                            <li><strong>User Interface:</strong> Professional medical device controls</li>
                        </ul>
                    </div>

                    <div style="background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); border-left: 4px solid #28a745;">
                        <h4 style="color: #28a745; margin-bottom: 15px; display: flex; align-items: center;">
                            <span style="font-size: 1.5em; margin-right: 10px;">⚙️</span>
                            Advanced Calibration
                        </h4>
                        <ul style="margin: 0; padding-left: 20px; line-height: 1.6;">
                            <li><strong>Systematic Procedures:</strong> Step-by-step calibration protocols</li>
                            <li><strong>Virtual Instruments:</strong> Professional calibrators and standards</li>
                            <li><strong>Error Analysis:</strong> Automated calculations and tolerance checking</li>
                            <li><strong>Documentation:</strong> Professional calibration certificates</li>
                            <li><strong>Compliance:</strong> Regulatory standards and requirements</li>
                        </ul>
                    </div>

                    <div style="background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); border-left: 4px solid #ffc107;">
                        <h4 style="color: #e65100; margin-bottom: 15px; display: flex; align-items: center;">
                            <span style="font-size: 1.5em; margin-right: 10px;">📊</span>
                            Statistical Analysis
                        </h4>
                        <ul style="margin: 0; padding-left: 20px; line-height: 1.6;">
                            <li><strong>GUM Uncertainty:</strong> Complete uncertainty budget analysis</li>
                            <li><strong>Statistical Methods:</strong> Mean, std dev, confidence intervals</li>
                            <li><strong>Visual Analytics:</strong> Charts, graphs, trend analysis</li>
                            <li><strong>Performance Assessment:</strong> Accuracy and precision evaluation</li>
                            <li><strong>Professional Reporting:</strong> Comprehensive documentation</li>
                        </ul>
                    </div>

                    <div style="background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); border-left: 4px solid #dc3545;">
                        <h4 style="color: #dc3545; margin-bottom: 15px; display: flex; align-items: center;">
                            <span style="font-size: 1.5em; margin-right: 10px;">🔍</span>
                            Systematic Troubleshooting
                        </h4>
                        <ul style="margin: 0; padding-left: 20px; line-height: 1.6;">
                            <li><strong>Diagnostic Methodology:</strong> 6-step systematic approach</li>
                            <li><strong>Root Cause Analysis:</strong> Fishbone diagrams, 5-Why technique</li>
                            <li><strong>Equipment Scenarios:</strong> Real-world failure modes</li>
                            <li><strong>CAPA Documentation:</strong> Corrective and preventive actions</li>
                            <li><strong>Prevention Strategies:</strong> Maintenance optimization</li>
                        </ul>
                    </div>
                </div>

                <!-- Technical Specifications -->
                <div style="margin-top: 30px; padding: 25px; background: #f8f9fa; border-radius: 12px; border: 1px solid #dee2e6;">
                    <h4 style="color: #0056b3; margin-bottom: 20px; text-align: center;">📋 Technical Specifications & Standards</h4>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; text-align: center;">
                        <div>
                            <h5 style="color: #495057; margin-bottom: 10px;">Calibration Standards</h5>
                            <p style="margin: 0; font-size: 0.9em; color: #6c757d;">
                                ISO/IEC 17025<br>
                                AAMI Standards<br>
                                FDA Guidelines
                            </p>
                        </div>
                        <div>
                            <h5 style="color: #495057; margin-bottom: 10px;">Measurement Accuracy</h5>
                            <p style="margin: 0; font-size: 0.9em; color: #6c757d;">
                                ±0.1% Reference Standards<br>
                                GUM Uncertainty Analysis<br>
                                95% Confidence Intervals
                            </p>
                        </div>
                        <div>
                            <h5 style="color: #495057; margin-bottom: 10px;">Device Coverage</h5>
                            <p style="margin: 0; font-size: 0.9em; color: #6c757d;">
                                Patient Monitors<br>
                                Ventilators & Pumps<br>
                                Diagnostic Equipment
                            </p>
                        </div>
                        <div>
                            <h5 style="color: #495057; margin-bottom: 10px;">Data Analysis</h5>
                            <p style="margin: 0; font-size: 0.9em; color: #6c757d;">
                                Real-time Statistics<br>
                                Trend Analysis<br>
                                Professional Reporting
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    </div>

    <!-- Simulations Content -->
    <div id="simulations-content" class="content-section" style="display: none;">
        <!-- Interactive Simulations Section (Moved from above) -->
        <section id="simulations" class="section">
            <div class="container">
                <h2 class="section-title">Interactive Clinical Engineering Simulations</h2>
                <p class="section-description">Experience hands-on learning with our comprehensive clinical engineering simulation modules featuring advanced visual aids, real-time data analysis, and professional-grade instrumentation.</p>

                <!-- Simulation Features Overview -->
                <div style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); padding: 30px; border-radius: 12px; margin-bottom: 40px;">
                    <h3 style="text-align: center; color: #0056b3; margin-bottom: 25px;">🚀 Advanced Simulation Features</h3>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; text-align: center;">
                        <div style="padding: 15px;">
                            <div style="font-size: 2.5em; color: #0056b3; margin-bottom: 10px;">📊</div>
                            <h4 style="margin: 10px 0; color: #0056b3;">Real-time Data</h4>
                            <p style="margin: 0; font-size: 0.9em;">Live ECG waveforms, vital signs monitoring, and trend analysis</p>
                        </div>
                        <div style="padding: 15px;">
                            <div style="font-size: 2.5em; color: #28a745; margin-bottom: 10px;">⚙️</div>
                            <h4 style="margin: 10px 0; color: #0056b3;">Virtual Instruments</h4>
                            <p style="margin: 0; font-size: 0.9em;">Professional calibrators, multimeters, and test equipment</p>
                        </div>
                        <div style="padding: 15px;">
                            <div style="font-size: 2.5em; color: #ffc107; margin-bottom: 10px;">📈</div>
                            <h4 style="margin: 10px 0; color: #0056b3;">Statistical Analysis</h4>
                            <p style="margin: 0; font-size: 0.9em;">GUM uncertainty analysis, accuracy assessment, and reporting</p>
                        </div>
                        <div style="padding: 15px;">
                            <div style="font-size: 2.5em; color: #dc3545; margin-bottom: 10px;">🔧</div>
                            <h4 style="margin: 10px 0; color: #0056b3;">Troubleshooting</h4>
                            <p style="margin: 0; font-size: 0.9em;">Systematic diagnostic procedures and root cause analysis</p>
                        </div>
                    </div>
                </div>

                <div class="simulations-grid">
                    <div class="simulation-card" style="position: relative; overflow: hidden;">
                        <div style="background: linear-gradient(45deg, #000 0%, #003300 100%); height: 200px; position: relative; display: flex; align-items: center; justify-content: center;">
                            <div style="color: #00ff00; font-family: 'Courier New', monospace; text-align: center;">
                                <div style="font-size: 1.2em; margin-bottom: 10px;">📺 PATIENT MONITOR</div>
                                <div style="font-size: 0.9em;">HR: 75 bpm | BP: 120/80 | SpO2: 98%</div>
                                <div style="width: 150px; height: 2px; background: #00ff00; margin: 10px auto; animation: pulse 1s infinite;"></div>
                            </div>
                        </div>
                        <h3>Medical Device Operation</h3>
                        <p><strong>Enhanced Features:</strong> Real-time ECG waveforms, multi-parameter monitoring, alarm systems, and patient scenario simulation with pathological conditions.</p>
                        <div style="margin: 10px 0; font-size: 0.9em; color: #6c757d;">
                            <span style="background: #e3f2fd; padding: 2px 6px; border-radius: 3px; margin-right: 5px;">ECG Simulation</span>
                            <span style="background: #f3e5f5; padding: 2px 6px; border-radius: 3px; margin-right: 5px;">Vital Signs</span>
                            <span style="background: #e8f5e8; padding: 2px 6px; border-radius: 3px;">Alarm Management</span>
                        </div>
                        <a href="clinical_engineering_simulation.html" class="btn primary">Launch Simulation</a>
                    </div>

                    <div class="simulation-card" style="position: relative; overflow: hidden;">
                        <div style="background: linear-gradient(45deg, #2c3e50 0%, #34495e 100%); height: 200px; position: relative; display: flex; align-items: center; justify-content: center;">
                            <div style="color: #00ff00; font-family: 'Courier New', monospace; text-align: center;">
                                <div style="font-size: 1.2em; margin-bottom: 10px;">⚙️ CALIBRATOR</div>
                                <div style="font-size: 1.5em; font-weight: bold;">100.0 mmHg</div>
                                <div style="font-size: 0.8em; margin-top: 5px;">REF STD | ±0.1 mmHg</div>
                            </div>
                        </div>
                        <h3>Calibration Laboratory</h3>
                        <p><strong>Enhanced Features:</strong> Step-by-step procedures, virtual reference standards, automated error calculations, and comprehensive documentation with pass/fail criteria.</p>
                        <div style="margin: 10px 0; font-size: 0.9em; color: #6c757d;">
                            <span style="background: #fff3e0; padding: 2px 6px; border-radius: 3px; margin-right: 5px;">Pressure Cal</span>
                            <span style="background: #fce4ec; padding: 2px 6px; border-radius: 3px; margin-right: 5px;">Temp Cal</span>
                            <span style="background: #e0f2f1; padding: 2px 6px; border-radius: 3px;">Safety Testing</span>
                        </div>
                        <a href="clinical_engineering_simulation.html#calibration" class="btn primary">Start Calibration</a>
                    </div>

                    <div class="simulation-card" style="position: relative; overflow: hidden;">
                        <div style="background: linear-gradient(45deg, #1a237e 0%, #3949ab 100%); height: 200px; position: relative; display: flex; align-items: center; justify-content: center;">
                            <div style="color: white; text-align: center;">
                                <div style="font-size: 1.2em; margin-bottom: 10px;">📊 STATISTICS</div>
                                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; font-size: 0.8em;">
                                    <div>Mean: ±0.15</div>
                                    <div>σ: ±0.23</div>
                                    <div>CI: 95%</div>
                                    <div>U: ±0.46</div>
                                </div>
                            </div>
                        </div>
                        <h3>Measurement & Analysis</h3>
                        <p><strong>Enhanced Features:</strong> GUM uncertainty analysis, statistical calculations, Bland-Altman plots, confidence intervals, and professional reporting capabilities.</p>
                        <div style="margin: 10px 0; font-size: 0.9em; color: #6c757d;">
                            <span style="background: #e8eaf6; padding: 2px 6px; border-radius: 3px; margin-right: 5px;">GUM Analysis</span>
                            <span style="background: #f3e5f5; padding: 2px 6px; border-radius: 3px; margin-right: 5px;">Statistics</span>
                            <span style="background: #e0f7fa; padding: 2px 6px; border-radius: 3px;">Reporting</span>
                        </div>
                        <a href="clinical_engineering_simulation.html#measurements" class="btn primary">Begin Measurements</a>
                    </div>

                    <div class="simulation-card" style="position: relative; overflow: hidden;">
                        <div style="background: linear-gradient(45deg, #b71c1c 0%, #d32f2f 100%); height: 200px; position: relative; display: flex; align-items: center; justify-content: center;">
                            <div style="color: white; text-align: center;">
                                <div style="font-size: 1.2em; margin-bottom: 10px;">🚨 ERROR E02</div>
                                <div style="font-size: 0.9em; margin-bottom: 10px;">BP Monitor Fault</div>
                                <div style="font-size: 0.8em;">Systematic Diagnosis Required</div>
                            </div>
                        </div>
                        <h3>Troubleshooting Scenarios</h3>
                        <p><strong>Enhanced Features:</strong> Real-world equipment failures, systematic diagnostic procedures, root cause analysis, CAPA documentation, and preventive measures.</p>
                        <div style="margin: 10px 0; font-size: 0.9em; color: #6c757d;">
                            <span style="background: #ffebee; padding: 2px 6px; border-radius: 3px; margin-right: 5px;">Diagnostics</span>
                            <span style="background: #fff8e1; padding: 2px 6px; border-radius: 3px; margin-right: 5px;">Root Cause</span>
                            <span style="background: #f1f8e9; padding: 2px 6px; border-radius: 3px;">CAPA</span>
                        </div>
                        <a href="clinical_engineering_simulation.html#troubleshooting" class="btn primary">Start Troubleshooting</a>
                    </div>

                    <div class="simulation-card" style="position: relative; overflow: hidden;">
                        <div style="background: linear-gradient(45deg, #4caf50 0%, #66bb6a 100%); height: 200px; position: relative; display: flex; align-items: center; justify-content: center;">
                            <div style="color: white; text-align: center;">
                                <div style="font-size: 1.2em; margin-bottom: 10px;">🎓 HANDS-ON TRAINING</div>
                                <div style="font-size: 0.9em; margin-bottom: 10px;">Practical Exercises</div>
                                <div style="font-size: 0.8em;">Step-by-step Learning</div>
                            </div>
                        </div>
                        <h3>Hands-on Learning</h3>
                        <p><strong>Enhanced Features:</strong> Comprehensive practical training with interactive exercises, real device operation procedures, and competency assessment.</p>
                        <div style="margin: 10px 0; font-size: 0.9em; color: #6c757d;">
                            <span style="background: #e8f5e9; padding: 2px 6px; border-radius: 3px; margin-right: 5px;">Device Operation</span>
                            <span style="background: #e3f2fd; padding: 2px 6px; border-radius: 3px; margin-right: 5px;">Measurements</span>
                            <span style="background: #fff3e0; padding: 2px 6px; border-radius: 3px;">Calibration</span>
                        </div>
                        <a href="hands_on_learning_medical_devices.html" class="btn primary">Start Training</a>
                    </div>
                </div>
            </div>
        </section>

    <!-- New Simulation Technology Section -->
    <section class="section" style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);">
        <div class="container">
            <h2 class="section-title" style="color: #0056b3;">🔬 Simulation Technology & Methodologies</h2>
            <p class="section-description">Explore the advanced technologies and mathematical foundations powering our clinical engineering simulations</p>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 30px; margin-top: 40px;">
                <!-- Mathematical Formulas -->
                <div style="background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
                    <h3 style="color: #0056b3; margin-bottom: 20px; display: flex; align-items: center;">
                        <span style="font-size: 1.5em; margin-right: 10px;">📐</span>
                        Mathematical Foundations
                    </h3>
                    <div style="background: #f8f9ff; padding: 15px; border-radius: 8px; font-family: 'Times New Roman', serif;">
                        <h4 style="margin-bottom: 10px; color: #495057;">Calibration Formulas:</h4>
                        <p style="margin: 5px 0; font-size: 0.9em;"><strong>Error:</strong> E = X<sub>device</sub> - X<sub>reference</sub></p>
                        <p style="margin: 5px 0; font-size: 0.9em;"><strong>% Error:</strong> %E = (E / X<sub>ref</sub>) × 100%</p>
                        <p style="margin: 5px 0; font-size: 0.9em;"><strong>Uncertainty:</strong> U = k × u<sub>c</sub>(y)</p>

                        <h4 style="margin: 15px 0 10px 0; color: #495057;">Statistical Analysis:</h4>
                        <p style="margin: 5px 0; font-size: 0.9em;"><strong>Mean:</strong> x̄ = (Σx<sub>i</sub>) / n</p>
                        <p style="margin: 5px 0; font-size: 0.9em;"><strong>Std Dev:</strong> σ = √[Σ(x<sub>i</sub> - x̄)² / (n-1)]</p>
                        <p style="margin: 5px 0; font-size: 0.9em;"><strong>CI:</strong> x̄ ± (t × SE)</p>
                    </div>
                </div>

                <!-- Simulation Technologies -->
                <div style="background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
                    <h3 style="color: #28a745; margin-bottom: 20px; display: flex; align-items: center;">
                        <span style="font-size: 1.5em; margin-right: 10px;">💻</span>
                        Simulation Technologies
                    </h3>
                    <ul style="list-style: none; padding: 0;">
                        <li style="margin: 10px 0; padding: 10px; background: #f0f9e8; border-radius: 6px;">
                            <strong>Real-time ECG Generation:</strong> Canvas-based waveform rendering with physiological accuracy
                        </li>
                        <li style="margin: 10px 0; padding: 10px; background: #e8f4fd; border-radius: 6px;">
                            <strong>Virtual Instrumentation:</strong> Digital calibrators and measurement devices
                        </li>
                        <li style="margin: 10px 0; padding: 10px; background: #fff3e0; border-radius: 6px;">
                            <strong>Statistical Engine:</strong> GUM-compliant uncertainty calculations
                        </li>
                        <li style="margin: 10px 0; padding: 10px; background: #fce4ec; border-radius: 6px;">
                            <strong>Interactive Diagnostics:</strong> Systematic troubleshooting workflows
                        </li>
                    </ul>
                </div>

                <!-- Data Visualization -->
                <div style="background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
                    <h3 style="color: #ffc107; margin-bottom: 20px; display: flex; align-items: center;">
                        <span style="font-size: 1.5em; margin-right: 10px;">📈</span>
                        Visual Analytics
                    </h3>
                    <div style="background: #000; color: #00ff00; padding: 15px; border-radius: 8px; font-family: 'Courier New', monospace; margin-bottom: 15px;">
                        <div style="text-align: center; font-size: 0.9em;">
                            ┌─ ECG Waveform Display ─┐<br>
                            │ ∩     ∩     ∩     ∩   │<br>
                            │∕ ∖   ∕ ∖   ∕ ∖   ∕ ∖  │<br>
                            └─────────────────────────┘<br>
                            HR: 75 bpm | Lead II
                        </div>
                    </div>
                    <ul style="margin: 0; padding-left: 20px; font-size: 0.9em;">
                        <li>Real-time waveform generation</li>
                        <li>Statistical distribution charts</li>
                        <li>Bland-Altman analysis plots</li>
                        <li>Uncertainty contribution diagrams</li>
                        <li>Professional measurement tables</li>
                    </ul>
                </div>
            </div>

            <!-- Standards and Compliance -->
            <div style="margin-top: 40px; padding: 25px; background: white; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
                <h3 style="color: #0056b3; margin-bottom: 20px; text-align: center;">📋 Standards & Compliance Framework</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
                    <div style="text-align: center; padding: 15px;">
                        <div style="font-size: 2em; color: #0056b3; margin-bottom: 10px;">🏛️</div>
                        <h4 style="margin: 10px 0; color: #495057;">Regulatory Standards</h4>
                        <p style="margin: 0; font-size: 0.9em; color: #6c757d;">
                            FDA 21 CFR Part 820<br>
                            ISO 13485:2016<br>
                            IEC 62304
                        </p>
                    </div>
                    <div style="text-align: center; padding: 15px;">
                        <div style="font-size: 2em; color: #28a745; margin-bottom: 10px;">⚖️</div>
                        <h4 style="margin: 10px 0; color: #495057;">Calibration Standards</h4>
                        <p style="margin: 0; font-size: 0.9em; color: #6c757d;">
                            ISO/IEC 17025:2017<br>
                            ANSI/AAMI ES60601<br>
                            NIST Traceability
                        </p>
                    </div>
                    <div style="text-align: center; padding: 15px;">
                        <div style="font-size: 2em; color: #ffc107; margin-bottom: 10px;">🔬</div>
                        <h4 style="margin: 10px 0; color: #495057;">Measurement Standards</h4>
                        <p style="margin: 0; font-size: 0.9em; color: #6c757d;">
                            GUM (JCGM 100:2008)<br>
                            ISO 5725 Series<br>
                            VIM (JCGM 200:2012)
                        </p>
                    </div>
                    <div style="text-align: center; padding: 15px;">
                        <div style="font-size: 2em; color: #dc3545; margin-bottom: 10px;">🛡️</div>
                        <h4 style="margin: 10px 0; color: #495057;">Safety Standards</h4>
                        <p style="margin: 0; font-size: 0.9em; color: #6c757d;">
                            IEC 60601 Series<br>
                            AAMI/ANSI ES60601<br>
                            ISO 14971:2019
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Simulation Statistics Section -->
    <section class="section" style="background: linear-gradient(135deg, #0056b3 0%, #00a0e9 100%); color: white;">
        <div class="container">
            <h2 class="section-title" style="color: white; margin-bottom: 40px;">📈 Simulation Capabilities & Performance</h2>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 30px; margin-bottom: 40px;">
                <div style="text-align: center; padding: 20px;">
                    <div style="font-size: 3em; font-weight: bold; margin-bottom: 10px;">12</div>
                    <h4 style="margin: 10px 0; color: rgba(255,255,255,0.9);">Comprehensive Lectures</h4>
                    <p style="margin: 0; color: rgba(255,255,255,0.7);">Complete curriculum coverage</p>
                </div>
                <div style="text-align: center; padding: 20px;">
                    <div style="font-size: 3em; font-weight: bold; margin-bottom: 10px;">4</div>
                    <h4 style="margin: 10px 0; color: rgba(255,255,255,0.9);">Simulation Modules</h4>
                    <p style="margin: 0; color: rgba(255,255,255,0.7);">Interactive learning environments</p>
                </div>
                <div style="text-align: center; padding: 20px;">
                    <div style="font-size: 3em; font-weight: bold; margin-bottom: 10px;">50+</div>
                    <h4 style="margin: 10px 0; color: rgba(255,255,255,0.9);">Virtual Instruments</h4>
                    <p style="margin: 0; color: rgba(255,255,255,0.7);">Professional test equipment</p>
                </div>
                <div style="text-align: center; padding: 20px;">
                    <div style="font-size: 3em; font-weight: bold; margin-bottom: 10px;">∞</div>
                    <h4 style="margin: 10px 0; color: rgba(255,255,255,0.9);">Scenarios</h4>
                    <p style="margin: 0; color: rgba(255,255,255,0.7);">Unlimited practice opportunities</p>
                </div>
            </div>

            <!-- Technical Specifications -->
            <div style="background: rgba(255,255,255,0.1); padding: 30px; border-radius: 12px; backdrop-filter: blur(10px);">
                <h3 style="text-align: center; margin-bottom: 30px; color: white;">🔬 Technical Specifications</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 25px;">
                    <div>
                        <h4 style="color: rgba(255,255,255,0.9); margin-bottom: 15px;">Measurement Accuracy</h4>
                        <ul style="color: rgba(255,255,255,0.8); margin: 0; padding-left: 20px;">
                            <li>Reference Standards: ±0.1% accuracy</li>
                            <li>Calibration Uncertainty: ±0.05%</li>
                            <li>Statistical Confidence: 95% CI</li>
                            <li>GUM Compliance: Full uncertainty budget</li>
                        </ul>
                    </div>
                    <div>
                        <h4 style="color: rgba(255,255,255,0.9); margin-bottom: 15px;">Device Coverage</h4>
                        <ul style="color: rgba(255,255,255,0.8); margin: 0; padding-left: 20px;">
                            <li>Patient Monitors: Multi-parameter</li>
                            <li>ECG Systems: 12-lead capability</li>
                            <li>Ventilators: Volume/Pressure modes</li>
                            <li>Infusion Pumps: Various flow rates</li>
                        </ul>
                    </div>
                    <div>
                        <h4 style="color: rgba(255,255,255,0.9); margin-bottom: 15px;">Analysis Tools</h4>
                        <ul style="color: rgba(255,255,255,0.8); margin: 0; padding-left: 20px;">
                            <li>Real-time Statistics: Mean, SD, CV</li>
                            <li>Trend Analysis: Performance tracking</li>
                            <li>Bland-Altman Plots: Agreement analysis</li>
                            <li>Distribution Charts: Error analysis</li>
                        </ul>
                    </div>
                    <div>
                        <h4 style="color: rgba(255,255,255,0.9); margin-bottom: 15px;">Compliance Standards</h4>
                        <ul style="color: rgba(255,255,255,0.8); margin: 0; padding-left: 20px;">
                            <li>ISO/IEC 17025: Calibration labs</li>
                            <li>FDA 21 CFR 820: Quality systems</li>
                            <li>IEC 60601: Medical device safety</li>
                            <li>AAMI Standards: Biomedical equipment</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </section>
    </div>

    <!-- Model Answers Content -->
    <div id="answers-content" class="content-section" style="display: none;">
        <section id="answers" class="section">
            <div class="container">
                <h2 class="section-title">Model Answers & Assessment Guide</h2>
                <p class="section-description">Comprehensive solutions and assessment criteria for all student activities across the 12-lecture Clinical Engineering course.</p>

                <!-- Quick Access to Model Answers -->
                <div style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); padding: 30px; border-radius: 12px; margin-bottom: 40px;">
                    <h3 style="text-align: center; color: #0056b3; margin-bottom: 25px;">📝 Complete Solutions Guide</h3>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; text-align: center;">
                        <div style="padding: 15px;">
                            <div style="font-size: 2.5em; color: #0056b3; margin-bottom: 10px;">📚</div>
                            <h4 style="margin: 10px 0; color: #0056b3;">73 Activities</h4>
                            <p style="margin: 0; font-size: 0.9em;">Comprehensive model answers for all lecture activities</p>
                        </div>
                        <div style="padding: 15px;">
                            <div style="font-size: 2.5em; color: #28a745; margin-bottom: 10px;">📊</div>
                            <h4 style="margin: 10px 0; color: #0056b3;">Assessment Criteria</h4>
                            <p style="margin: 0; font-size: 0.9em;">Detailed grading rubrics and evaluation guidelines</p>
                        </div>
                        <div style="padding: 15px;">
                            <div style="font-size: 2.5em; color: #ffc107; margin-bottom: 10px;">🎯</div>
                            <h4 style="margin: 10px 0; color: #0056b3;">Key Points</h4>
                            <p style="margin: 0; font-size: 0.9em;">Essential concepts and learning objectives</p>
                        </div>
                        <div style="padding: 15px;">
                            <div style="font-size: 2.5em; color: #dc3545; margin-bottom: 10px;">📋</div>
                            <h4 style="margin: 10px 0; color: #0056b3;">Case Studies</h4>
                            <p style="margin: 0; font-size: 0.9em;">Real-world scenarios with detailed solutions</p>
                        </div>
                    </div>
                </div>

                <!-- Lecture Categories -->
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 25px; margin: 30px 0;">
                    <div style="background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); border-left: 4px solid #0056b3;">
                        <h4 style="color: #0056b3; margin-bottom: 15px; display: flex; align-items: center;">
                            <span style="font-size: 1.5em; margin-right: 10px;">📖</span>
                            Fundamentals (Lectures 1-3)
                        </h4>
                        <ul style="margin: 0; padding-left: 20px; line-height: 1.6;">
                            <li><strong>Introduction to Clinical Engineering:</strong> Role definition, scope analysis</li>
                            <li><strong>Healthcare Technology Management:</strong> HTM frameworks, strategic planning</li>
                            <li><strong>Regulatory Standards:</strong> FDA, ISO compliance, quality systems</li>
                        </ul>
                        <div style="display: flex; gap: 10px; margin-top: 15px; flex-wrap: wrap;">
                            <a href="model_answers_activities.html?lecture=1" class="btn primary" style="font-size: 0.9em;">Lecture 1</a>
                            <a href="model_answers_activities.html?lecture=2" class="btn primary" style="font-size: 0.9em;">Lecture 2</a>
                            <a href="model_answers_activities.html?lecture=3" class="btn primary" style="font-size: 0.9em;">Lecture 3</a>
                            <a href="model_answers_activities.html#fundamentals" class="btn" style="background: #0056b3; color: white; font-size: 0.9em;">All Fundamentals</a>
                        </div>
                    </div>

                    <div style="background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); border-left: 4px solid #28a745;">
                        <h4 style="color: #28a745; margin-bottom: 15px; display: flex; align-items: center;">
                            <span style="font-size: 1.5em; margin-right: 10px;">🔧</span>
                            Management (Lectures 4-6)
                        </h4>
                        <ul style="margin: 0; padding-left: 20px; line-height: 1.6;">
                            <li><strong>Equipment Management:</strong> PM programs, CMMS implementation</li>
                            <li><strong>Safety & Risk Management:</strong> Risk assessment, incident investigation</li>
                            <li><strong>Financial Management:</strong> TCO analysis, economic impact</li>
                        </ul>
                        <div style="display: flex; gap: 10px; margin-top: 15px; flex-wrap: wrap;">
                            <a href="model_answers_activities.html?lecture=4" class="btn primary" style="font-size: 0.9em;">Lecture 4</a>
                            <a href="model_answers_activities.html?lecture=5" class="btn primary" style="font-size: 0.9em;">Lecture 5</a>
                            <a href="model_answers_activities.html?lecture=6" class="btn primary" style="font-size: 0.9em;">Lecture 6</a>
                            <a href="model_answers_activities.html#management" class="btn" style="background: #28a745; color: white; font-size: 0.9em;">All Management</a>
                        </div>
                    </div>

                    <div style="background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); border-left: 4px solid #ffc107;">
                        <h4 style="color: #e65100; margin-bottom: 15px; display: flex; align-items: center;">
                            <span style="font-size: 1.5em; margin-right: 10px;">🔬</span>
                            Advanced (Lectures 7-9)
                        </h4>
                        <ul style="margin: 0; padding-left: 20px; line-height: 1.6;">
                            <li><strong>Testing & Calibration:</strong> Calibration programs, GUM uncertainty</li>
                            <li><strong>Manufacturing & Lifecycle:</strong> Design controls, validation</li>
                            <li><strong>Troubleshooting:</strong> Systematic methodology, root cause analysis</li>
                        </ul>
                        <div style="display: flex; gap: 10px; margin-top: 15px; flex-wrap: wrap;">
                            <a href="model_answers_activities.html?lecture=7" class="btn primary" style="font-size: 0.9em;">Lecture 7</a>
                            <a href="model_answers_activities.html?lecture=8" class="btn primary" style="font-size: 0.9em;">Lecture 8</a>
                            <a href="model_answers_activities.html?lecture=9" class="btn primary" style="font-size: 0.9em;">Lecture 9</a>
                            <a href="model_answers_activities.html#advanced" class="btn" style="background: #ffc107; color: white; font-size: 0.9em;">All Advanced</a>
                        </div>
                    </div>

                    <div style="background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); border-left: 4px solid #dc3545;">
                        <h4 style="color: #dc3545; margin-bottom: 15px; display: flex; align-items: center;">
                            <span style="font-size: 1.5em; margin-right: 10px;">🌐</span>
                            Specialized (Lectures 10-12)
                        </h4>
                        <ul style="margin: 0; padding-left: 20px; line-height: 1.6;">
                            <li><strong>Emerging Technologies:</strong> AI/ML impact, digital transformation</li>
                            <li><strong>Professional Development:</strong> Career planning, competency development</li>
                            <li><strong>Future of Clinical Engineering:</strong> Trends analysis, strategic responses</li>
                        </ul>
                        <div style="display: flex; gap: 10px; margin-top: 15px; flex-wrap: wrap;">
                            <a href="model_answers_activities.html?lecture=10" class="btn primary" style="font-size: 0.9em;">Lecture 10</a>
                            <a href="model_answers_activities.html?lecture=11" class="btn primary" style="font-size: 0.9em;">Lecture 11</a>
                            <a href="model_answers_activities.html?lecture=12" class="btn primary" style="font-size: 0.9em;">Lecture 12</a>
                            <a href="model_answers_activities.html#specialized" class="btn" style="background: #dc3545; color: white; font-size: 0.9em;">All Specialized</a>
                        </div>
                    </div>
                </div>

                <!-- Quick Activity Access -->
                <div style="background: #f8f9fa; padding: 25px; border-radius: 12px; margin: 30px 0;">
                    <h3 style="text-align: center; color: #0056b3; margin-bottom: 20px;">🔍 Quick Activity Access</h3>
                    <p style="text-align: center; margin-bottom: 20px; color: #6c757d;">Jump directly to specific activities and their model answers</p>

                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px;">
                        <div style="background: white; padding: 15px; border-radius: 8px; border-left: 3px solid #0056b3;">
                            <h5 style="margin: 0 0 10px 0; color: #0056b3;">🎯 High-Priority Activities</h5>
                            <div style="display: flex; flex-direction: column; gap: 5px;">
                                <a href="model_answers_activities.html?lecture=1&activity=1" style="color: #495057; text-decoration: none; font-size: 0.9em;">• CE Role Definition</a>
                                <a href="model_answers_activities.html?lecture=4&activity=1" style="color: #495057; text-decoration: none; font-size: 0.9em;">• PM Program Design</a>
                                <a href="model_answers_activities.html?lecture=5&activity=1" style="color: #495057; text-decoration: none; font-size: 0.9em;">• Risk Assessment</a>
                                <a href="model_answers_activities.html?lecture=7&activity=2" style="color: #495057; text-decoration: none; font-size: 0.9em;">• GUM Uncertainty Analysis</a>
                            </div>
                        </div>

                        <div style="background: white; padding: 15px; border-radius: 8px; border-left: 3px solid #28a745;">
                            <h5 style="margin: 0 0 10px 0; color: #28a745;">💼 Management Focus</h5>
                            <div style="display: flex; flex-direction: column; gap: 5px;">
                                <a href="model_answers_activities.html?lecture=2&activity=1" style="color: #495057; text-decoration: none; font-size: 0.9em;">• HTM Framework</a>
                                <a href="model_answers_activities.html?lecture=4&activity=2" style="color: #495057; text-decoration: none; font-size: 0.9em;">• CMMS Implementation</a>
                                <a href="model_answers_activities.html?lecture=6&activity=1" style="color: #495057; text-decoration: none; font-size: 0.9em;">• Capital Acquisition</a>
                                <a href="model_answers_activities.html?lecture=6&activity=2" style="color: #495057; text-decoration: none; font-size: 0.9em;">• Economic Impact</a>
                            </div>
                        </div>

                        <div style="background: white; padding: 15px; border-radius: 8px; border-left: 3px solid #ffc107;">
                            <h5 style="margin: 0 0 10px 0; color: #e65100;">🔬 Technical Skills</h5>
                            <div style="display: flex; flex-direction: column; gap: 5px;">
                                <a href="model_answers_activities.html?lecture=7&activity=1" style="color: #495057; text-decoration: none; font-size: 0.9em;">• Calibration Program</a>
                                <a href="model_answers_activities.html?lecture=8&activity=1" style="color: #495057; text-decoration: none; font-size: 0.9em;">• Design Controls</a>
                                <a href="model_answers_activities.html?lecture=9&activity=1" style="color: #495057; text-decoration: none; font-size: 0.9em;">• Troubleshooting Method</a>
                                <a href="model_answers_activities.html?lecture=3&activity=2" style="color: #495057; text-decoration: none; font-size: 0.9em;">• Compliance Strategy</a>
                            </div>
                        </div>

                        <div style="background: white; padding: 15px; border-radius: 8px; border-left: 3px solid #dc3545;">
                            <h5 style="margin: 0 0 10px 0; color: #dc3545;">🚀 Future-Focused</h5>
                            <div style="display: flex; flex-direction: column; gap: 5px;">
                                <a href="model_answers_activities.html?lecture=10&activity=1" style="color: #495057; text-decoration: none; font-size: 0.9em;">• AI/ML Impact</a>
                                <a href="model_answers_activities.html?lecture=11&activity=1" style="color: #495057; text-decoration: none; font-size: 0.9em;">• Career Development</a>
                                <a href="model_answers_activities.html?lecture=12&activity=1" style="color: #495057; text-decoration: none; font-size: 0.9em;">• Future Trends</a>
                                <a href="model_answers_activities.html?lecture=5&activity=2" style="color: #495057; text-decoration: none; font-size: 0.9em;">• Incident Investigation</a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Direct Access Button -->
                <div style="text-align: center; margin: 40px 0;">
                    <a href="model_answers_activities.html" class="btn" style="background: linear-gradient(45deg, #dc3545, #e91e63); color: white; font-size: 1.2em; padding: 15px 30px; text-decoration: none; border-radius: 8px; display: inline-block; margin-right: 15px;">
                        📝 Complete Model Answers Guide
                    </a>
                    <a href="model_answers_activities.html?search=true" class="btn" style="background: linear-gradient(45deg, #0056b3, #00a0e9); color: white; font-size: 1.1em; padding: 12px 25px; text-decoration: none; border-radius: 8px; display: inline-block;">
                        🔍 Search Activities
                    </a>
                </div>
            </div>
        </section>
    </div>

    <section id="resources" class="section">
        <div class="container">
            <h2 class="section-title">Additional Resources</h2>
            <div class="resources-grid">
                <div class="resource-card">
                    <div class="resource-icon"><i class="fas fa-file-pdf"></i></div>
                    <h3>Course Materials</h3>
                    <p>Access lecture slides, handouts, and supplementary reading materials.</p>
                    <a href="#" class="resource-link">View Materials</a>
                </div>
                <div class="resource-card">
                    <div class="resource-icon"><i class="fas fa-video"></i></div>
                    <h3>Video Tutorials</h3>
                    <p>Watch instructional videos on clinical engineering concepts and practices.</p>
                    <a href="#" class="resource-link">Watch Videos</a>
                </div>
                <div class="resource-card">
                    <div class="resource-icon"><i class="fas fa-book"></i></div>
                    <h3>Recommended Reading</h3>
                    <p>Explore textbooks, journals, and articles related to clinical engineering.</p>
                    <a href="#" class="resource-link">View Reading List</a>
                </div>
                <div class="resource-card">
                    <div class="resource-icon"><i class="fas fa-link"></i></div>
                    <h3>External Links</h3>
                    <p>Access professional organizations, regulatory bodies, and industry resources.</p>
                    <a href="#" class="resource-link">Browse Links</a>
                </div>
            </div>
        </div>
    </section>

    <section id="about" class="section">
        <div class="container">
            <h2 class="section-title">About This Course</h2>
            <div class="about-content">
                <div class="about-text">
                    <p>The Clinical Engineering and Simulation course is designed to provide students with a comprehensive understanding of the principles, practices, and challenges in the field of clinical engineering. Through a combination of theoretical learning and practical simulations, students will develop the knowledge and skills necessary to manage healthcare technology effectively and safely.</p>
                    <p>This course covers the entire spectrum of clinical engineering, from basic principles to advanced concepts, regulatory compliance, risk management, and future trends in healthcare technology.</p>
                    <h3>Learning Objectives</h3>
                    <ul>
                        <li>Understand the role of clinical engineering in healthcare delivery</li>
                        <li>Apply principles of medical equipment management throughout the technology lifecycle</li>
                        <li>Interpret and implement regulatory standards and guidelines</li>
                        <li>Develop risk management strategies for healthcare technology</li>
                        <li>Gain practical experience through interactive simulations</li>
                    </ul>
                </div>
                <div class="instructor-info">
                    <img src="https://via.placeholder.com/150" alt="Dr. Mohammed Yagoub Esmail" class="instructor-image">
                    <h3>Course Instructor</h3>
                    <p>Dr. Mohammed Yagoub Esmail</p>
                    <p>Professor of Clinical Engineering</p>
                    <p>Email: <EMAIL></p>
                    <p>Phone: +************ | +************</p>
                </div>
            </div>
        </div>
    </section>

    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-logo">
                    <h2>Clinical Engineering and Simulation</h2>
                    <p>© 2025 All Rights Reserved</p>
                </div>
                <div class="footer-links">
                    <h3>Quick Links</h3>
                    <ul>
                        <li><a href="#">Home</a></li>
                        <li><a href="#lectures">Lectures</a></li>
                        <li><a href="#simulations">Simulations</a></li>
                        <li><a href="#resources">Resources</a></li>
                        <li><a href="#about">About</a></li>
                    </ul>
                </div>
                <div class="footer-contact">
                    <h3>Contact Us</h3>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-phone"></i> +************ | +************</p>
                    <p><i class="fas fa-map-marker-alt"></i> Nahda College, Sudan</p>
                </div>
            </div>
        </div>
    </footer>

    <script src="JS/main.js"></script>

    <script>
        // Content Switcher Functionality
        let currentContent = 'lectures';

        function switchContent(contentType) {
            // Update current content
            currentContent = contentType;

            // Hide all content sections
            document.getElementById('lectures-content').style.display = 'none';
            document.getElementById('simulations-content').style.display = 'none';
            document.getElementById('answers-content').style.display = 'none';

            // Show selected content
            const selectedContent = document.getElementById(contentType + '-content');
            selectedContent.style.display = 'block';
            selectedContent.classList.add('fade-in');

            // Update tab states
            document.querySelectorAll('.content-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            document.getElementById(contentType + '-tab').classList.add('active');

            // Update URL hash
            window.location.hash = contentType;

            // Smooth scroll to content
            selectedContent.scrollIntoView({ behavior: 'smooth' });

            // Track user interaction
            trackUserInteraction('content_switch', contentType);
        }

        function toggleContent() {
            if (currentContent === 'lectures') {
                switchContent('simulations');
            } else if (currentContent === 'simulations') {
                switchContent('answers');
            } else {
                switchContent('lectures');
            }
        }

        // Navigation enhancement
        function enhanceNavigation() {
            // Update main navigation based on current content
            const navLinks = document.querySelectorAll('nav a');
            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    if (this.getAttribute('href') === '#lectures') {
                        e.preventDefault();
                        switchContent('lectures');
                    } else if (this.getAttribute('href') === '#simulations') {
                        e.preventDefault();
                        switchContent('simulations');
                    }
                });
            });

            // Update hero buttons
            const heroButtons = document.querySelectorAll('.hero-buttons a');
            heroButtons.forEach(button => {
                if (button.getAttribute('href') === '#lectures') {
                    button.addEventListener('click', function(e) {
                        e.preventDefault();
                        switchContent('lectures');
                    });
                } else if (button.getAttribute('href') === '#simulations') {
                    button.addEventListener('click', function(e) {
                        e.preventDefault();
                        switchContent('simulations');
                    });
                }
            });
        }

        // Keyboard shortcuts
        function setupKeyboardShortcuts() {
            document.addEventListener('keydown', function(e) {
                // Alt + L for Lectures
                if (e.altKey && e.key === 'l') {
                    e.preventDefault();
                    switchContent('lectures');
                }
                // Alt + S for Simulations
                if (e.altKey && e.key === 's') {
                    e.preventDefault();
                    switchContent('simulations');
                }
                // Alt + A for Answers
                if (e.altKey && e.key === 'a') {
                    e.preventDefault();
                    switchContent('answers');
                }
                // Alt + T for Toggle (cycles through all three)
                if (e.altKey && e.key === 't') {
                    e.preventDefault();
                    toggleContent();
                }
            });
        }

        // Progress tracking
        function trackProgress() {
            const visitedSections = JSON.parse(localStorage.getItem('visitedSections') || '[]');

            // Mark current section as visited
            if (!visitedSections.includes(currentContent)) {
                visitedSections.push(currentContent);
                localStorage.setItem('visitedSections', JSON.stringify(visitedSections));
            }

            // Update progress indicator
            updateProgressIndicator(visitedSections);
        }

        function updateProgressIndicator(visitedSections) {
            const totalSections = 3; // lectures, simulations, and answers
            const progress = (visitedSections.length / totalSections) * 100;

            // Create or update progress bar
            let progressBar = document.getElementById('progress-bar');
            if (!progressBar) {
                progressBar = document.createElement('div');
                progressBar.id = 'progress-bar';
                progressBar.style.cssText = `
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: ${progress}%;
                    height: 3px;
                    background: linear-gradient(90deg, #28a745, #20c997);
                    z-index: 1000;
                    transition: width 0.5s ease;
                `;
                document.body.appendChild(progressBar);
            } else {
                progressBar.style.width = progress + '%';
            }
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            // Check URL hash for initial content
            const hash = window.location.hash.substring(1);
            if (hash === 'simulations') {
                switchContent('simulations');
            } else if (hash === 'answers') {
                switchContent('answers');
            } else {
                switchContent('lectures');
            }

            // Setup enhancements
            enhanceNavigation();
            setupKeyboardShortcuts();
            trackProgress();
            addTooltips();
            addReturnToTop();
            showWelcomeMessage();
        });

        function addTooltips() {
            // Add keyboard shortcut tooltips
            const lecturesTab = document.getElementById('lectures-tab');
            const simulationsTab = document.getElementById('simulations-tab');
            const answersTab = document.getElementById('answers-tab');

            if (lecturesTab) lecturesTab.title = 'Switch to Lectures (Alt + L)';
            if (simulationsTab) simulationsTab.title = 'Switch to Simulations (Alt + S)';
            if (answersTab) answersTab.title = 'Switch to Model Answers (Alt + A)';
        }

        // Return to top functionality
        function addReturnToTop() {
            const returnBtn = document.createElement('button');
            returnBtn.innerHTML = '↑ Top';
            returnBtn.style.cssText = `
                position: fixed;
                bottom: 20px;
                right: 20px;
                background: #0056b3;
                color: white;
                border: none;
                padding: 10px 15px;
                border-radius: 25px;
                cursor: pointer;
                font-weight: 600;
                box-shadow: 0 4px 12px rgba(0, 86, 179, 0.3);
                transition: all 0.3s ease;
                z-index: 1000;
                display: none;
            `;

            returnBtn.addEventListener('click', function() {
                window.scrollTo({ top: 0, behavior: 'smooth' });
            });

            // Show/hide based on scroll position
            window.addEventListener('scroll', function() {
                if (window.scrollY > 300) {
                    returnBtn.style.display = 'block';
                } else {
                    returnBtn.style.display = 'none';
                }
            });

            document.body.appendChild(returnBtn);
        }

        // Analytics tracking
        function trackUserInteraction(action, section) {
            console.log(`User Action: ${action} in ${section}`);
            // Integration point for analytics services
        }

        // Quick menu toggle
        function toggleQuickMenu() {
            const menu = document.getElementById('quickMenu');
            if (menu.style.display === 'none' || menu.style.display === '') {
                menu.style.display = 'block';
            } else {
                menu.style.display = 'none';
            }
        }

        // Close quick menu when clicking outside
        document.addEventListener('click', function(event) {
            const menu = document.getElementById('quickMenu');
            const button = event.target.closest('button');

            if (menu && !button && !menu.contains(event.target)) {
                menu.style.display = 'none';
            }
        });

        // Notification system
        function showNotification(message, type = 'info') {
            const banner = document.getElementById('notification-banner');
            const text = document.getElementById('notification-text');

            text.textContent = message;
            banner.style.display = 'block';

            // Auto-hide after 5 seconds
            setTimeout(() => {
                hideNotification();
            }, 5000);
        }

        function hideNotification() {
            const banner = document.getElementById('notification-banner');
            banner.style.display = 'none';
        }

        // Enhanced switch content with notifications
        function switchContent(contentType) {
            // Update current content
            currentContent = contentType;

            // Hide all content sections
            document.getElementById('lectures-content').style.display = 'none';
            document.getElementById('simulations-content').style.display = 'none';
            document.getElementById('answers-content').style.display = 'none';

            // Show selected content
            const selectedContent = document.getElementById(contentType + '-content');
            selectedContent.style.display = 'block';
            selectedContent.classList.add('fade-in');

            // Update tab states
            document.querySelectorAll('.content-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            document.getElementById(contentType + '-tab').classList.add('active');

            // Show contextual notifications
            if (contentType === 'lectures') {
                showNotification('📚 Now viewing Lectures & Theory content. Access comprehensive academic materials and course curriculum.');
            } else if (contentType === 'simulations') {
                showNotification('🚀 Now viewing Interactive Simulations. Experience hands-on learning with virtual instruments and real-time analysis.');
            } else if (contentType === 'answers') {
                showNotification('📝 Now viewing Model Answers. Access comprehensive solutions and assessment criteria for all activities.');
            }

            // Update URL hash
            window.location.hash = contentType;

            // Smooth scroll to content
            selectedContent.scrollIntoView({ behavior: 'smooth' });

            // Track user interaction
            trackUserInteraction('content_switch', contentType);
        }

        // Welcome message on first visit
        function showWelcomeMessage() {
            const hasVisited = localStorage.getItem('hasVisited');
            if (!hasVisited) {
                setTimeout(() => {
                    showNotification('🎓 Welcome to Clinical Engineering 2025! Navigate between Lectures, Simulations, and Model Answers using the tabs above. Keyboard shortcuts: Alt+L (Lectures), Alt+S (Simulations), Alt+A (Answers), Alt+T (Toggle)');
                    localStorage.setItem('hasVisited', 'true');
                }, 2000);
            }
        }
    </script>
</body>
</html>