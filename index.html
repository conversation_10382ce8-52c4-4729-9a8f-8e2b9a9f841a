<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clinical Engineering and Simulation 2025</title>
    <link rel="stylesheet" href="CSS/main.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <header>
        <div class="container">
            <h1>Clinical Engineering and Simulation 2025</h1>
            <nav>
                <ul>
                    <li><a href="#" class="active">Home</a></li>
                    <li><a href="#lectures">Lectures</a></li>
                    <li><a href="#simulations">Simulations</a></li>
                    <li><a href="#resources">Resources</a></li>
                    <li><a href="#about">About</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Quick Access Simulation Bar -->
    <div style="background: linear-gradient(90deg, #0056b3 0%, #00a0e9 100%); padding: 15px 0; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
        <div class="container">
            <div style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: 15px;">
                <div style="color: white; font-weight: bold; font-size: 1.1em;">
                    🚀 Quick Access Simulations:
                </div>
                <div style="display: flex; gap: 15px; flex-wrap: wrap;">
                    <a href="clinical_engineering_simulation.html#device-simulation"
                       style="background: rgba(255,255,255,0.2); color: white; padding: 8px 15px; border-radius: 20px; text-decoration: none; font-size: 0.9em; transition: all 0.3s ease;"
                       onmouseover="this.style.background='rgba(255,255,255,0.3)'"
                       onmouseout="this.style.background='rgba(255,255,255,0.2)'">
                        📺 Device Operation
                    </a>
                    <a href="clinical_engineering_simulation.html#calibration"
                       style="background: rgba(255,255,255,0.2); color: white; padding: 8px 15px; border-radius: 20px; text-decoration: none; font-size: 0.9em; transition: all 0.3s ease;"
                       onmouseover="this.style.background='rgba(255,255,255,0.3)'"
                       onmouseout="this.style.background='rgba(255,255,255,0.2)'">
                        ⚙️ Calibration Lab
                    </a>
                    <a href="clinical_engineering_simulation.html#measurements"
                       style="background: rgba(255,255,255,0.2); color: white; padding: 8px 15px; border-radius: 20px; text-decoration: none; font-size: 0.9em; transition: all 0.3s ease;"
                       onmouseover="this.style.background='rgba(255,255,255,0.3)'"
                       onmouseout="this.style.background='rgba(255,255,255,0.2)'">
                        📊 Measurements
                    </a>
                    <a href="clinical_engineering_simulation.html#troubleshooting"
                       style="background: rgba(255,255,255,0.2); color: white; padding: 8px 15px; border-radius: 20px; text-decoration: none; font-size: 0.9em; transition: all 0.3s ease;"
                       onmouseover="this.style.background='rgba(255,255,255,0.3)'"
                       onmouseout="this.style.background='rgba(255,255,255,0.2)'">
                        🔧 Troubleshooting
                    </a>
                </div>
            </div>
        </div>
    </div>

    <section class="hero">
        <div class="container">
            <div class="hero-content">
                <h2>Welcome to Clinical Engineering and Simulation</h2>
                <p>Explore the intersection of healthcare technology, engineering principles, and clinical practice through advanced interactive simulations with real-time data analysis and professional-grade instrumentation.</p>

                <!-- Enhanced Hero Features -->
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 30px 0; text-align: center;">
                    <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px; backdrop-filter: blur(10px);">
                        <div style="font-size: 2em; margin-bottom: 10px;">📊</div>
                        <h4 style="margin: 5px 0; color: white;">Real-time Analytics</h4>
                        <p style="margin: 0; font-size: 0.9em; color: rgba(255,255,255,0.8);">Live ECG, vital signs, statistical analysis</p>
                    </div>
                    <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px; backdrop-filter: blur(10px);">
                        <div style="font-size: 2em; margin-bottom: 10px;">⚙️</div>
                        <h4 style="margin: 5px 0; color: white;">Virtual Instruments</h4>
                        <p style="margin: 0; font-size: 0.9em; color: rgba(255,255,255,0.8);">Professional calibrators, test equipment</p>
                    </div>
                    <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px; backdrop-filter: blur(10px);">
                        <div style="font-size: 2em; margin-bottom: 10px;">🔬</div>
                        <h4 style="margin: 5px 0; color: white;">GUM Analysis</h4>
                        <p style="margin: 0; font-size: 0.9em; color: rgba(255,255,255,0.8);">Complete uncertainty calculations</p>
                    </div>
                    <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px; backdrop-filter: blur(10px);">
                        <div style="font-size: 2em; margin-bottom: 10px;">🔧</div>
                        <h4 style="margin: 5px 0; color: white;">Troubleshooting</h4>
                        <p style="margin: 0; font-size: 0.9em; color: rgba(255,255,255,0.8);">Systematic diagnostic procedures</p>
                    </div>
                </div>

                <div class="hero-buttons">
                    <a href="#lectures" class="btn primary">View 12 Lectures</a>
                    <a href="#simulations" class="btn secondary">Launch Simulations</a>
                    <a href="clinical_engineering_simulation.html" class="btn" style="background: rgba(255,255,255,0.2); color: white; border: 2px solid white;">Quick Start Demo</a>
                </div>
            </div>
        </div>
    </section>

    <section id="course-overview" class="section">
        <div class="container">
            <h2 class="section-title">Course Overview</h2>
            <div class="course-grid">
                <div class="course-card">
                    <div class="card-icon"><i class="fas fa-book-medical"></i></div>
                    <h3>Introduction to Clinical Engineering</h3>
                    <p>Learn the fundamentals of clinical engineering and its role in healthcare delivery.</p>
                </div>
                <div class="course-card">
                    <div class="card-icon"><i class="fas fa-heartbeat"></i></div>
                    <h3>Medical Equipment Lifecycle</h3>
                    <p>Understand the complete lifecycle of medical equipment from acquisition to retirement.</p>
                </div>
                <div class="course-card">
                    <div class="card-icon"><i class="fas fa-clipboard-check"></i></div>
                    <h3>Regulatory Standards</h3>
                    <p>Explore the regulatory framework governing medical devices and healthcare technology.</p>
                </div>
                <div class="course-card">
                    <div class="card-icon"><i class="fas fa-shield-alt"></i></div>
                    <h3>Risk Management</h3>
                    <p>Learn approaches to identify, assess, and mitigate risks associated with medical technology.</p>
                </div>
                <div class="course-card">
                    <div class="card-icon"><i class="fas fa-industry"></i></div>
                    <h3>Manufacturing & Lifecycle</h3>
                    <p>Explore medical device manufacturing processes and product lifecycle management.</p>
                </div>
                <div class="course-card">
                    <div class="card-icon"><i class="fas fa-chart-line"></i></div>
                    <h3>Technology Assessment</h3>
                    <p>Learn systematic approaches to evaluating healthcare technologies for effectiveness and value.</p>
                </div>
                <div class="course-card">
                    <div class="card-icon"><i class="fas fa-users"></i></div>
                    <h3>Management & Leadership</h3>
                    <p>Develop essential leadership and management skills for clinical engineering professionals.</p>
                </div>
                <div class="course-card">
                    <div class="card-icon"><i class="fas fa-rocket"></i></div>
                    <h3>Emerging Technologies</h3>
                    <p>Explore cutting-edge technologies shaping the future of healthcare and clinical engineering.</p>
                </div>
                <div class="course-card">
                    <div class="card-icon"><i class="fas fa-graduation-cap"></i></div>
                    <h3>Professional Development</h3>
                    <p>Build a successful career through continuous learning and professional growth strategies.</p>
                </div>
            </div>
        </div>
    </section>

    <section id="lectures" class="section">
        <div class="container">
            <h2 class="section-title">Course Lectures</h2>
            <div class="lectures-grid">
                <div class="lecture-card">
                    <div class="lecture-number">01</div>
                    <h3>Introduction to Clinical Engineering</h3>
                    <p>Fundamentals and core concepts of clinical engineering in healthcare settings.</p>
                    <div class="lecture-links">
                        <a href="lecture_one_introduction.html">Main Content</a>
                        <a href="lecture_one_detailed_notes.html">Detailed Notes</a>
                        <a href="lecture_one_student_activities.html">Activities</a>
                    </div>
                </div>
                <div class="lecture-card">
                    <div class="lecture-number">02</div>
                    <h3>Medical Equipment Lifecycle</h3>
                    <p>Understanding acquisition, maintenance, and retirement of medical equipment.</p>
                    <div class="lecture-links">
                        <a href="lecture_two_equipment_lifecycle.html">Main Content</a>
                        <a href="lecture_two_detailed_notes.html">Detailed Notes</a>
                        <a href="lecture_two_student_activities.html">Activities</a>
                    </div>
                </div>
                <div class="lecture-card">
                    <div class="lecture-number">03</div>
                    <h3>Regulatory Standards</h3>
                    <p>Exploring FDA, ISO, and other regulatory frameworks for medical devices.</p>
                    <div class="lecture-links">
                        <a href="lecture_three_regulatory_standards.html">Main Content</a>
                        <a href="lecture_three_detailed_notes.html">Detailed Notes</a>
                        <a href="lecture_three_student_activities.html">Activities</a>
                    </div>
                </div>
                <div class="lecture-card">
                    <div class="lecture-number">04</div>
                    <h3>Risk Management</h3>
                    <p>Approaches to identify, assess, and mitigate risks in healthcare technology.</p>
                    <div class="lecture-links">
                        <a href="lecture_four_risk_management.html">Main Content</a>
                        <a href="lecture_four_detailed_notes.html">Detailed Notes</a>
                        <a href="lecture_four_student_activities.html">Activities</a>
                    </div>
                </div>
                <div class="lecture-card">
                    <div class="lecture-number">05</div>
                    <h3>Clinical Systems Engineering & Interoperability</h3>
                    <p>Connecting healthcare technologies for seamless patient care.</p>
                    <div class="lecture-links">
                        <a href="lecture_five_systems_engineering.html">Main Content</a>
                        <a href="lecture_five_detailed_notes.html">Detailed Notes</a>
                        <a href="lecture_five_student_activities.html">Activities</a>
                    </div>
                </div>
                <div class="lecture-card">
                    <div class="lecture-number">06</div>
                    <h3>Medical Instrumentation & Diagnostics</h3>
                    <p>Understanding the principles and applications of diagnostic medical equipment.</p>
                    <div class="lecture-links">
                        <a href="lecture_six_medical_instrumentation.html">Main Content</a>
                        <a href="lecture_six_detailed_notes.html">Detailed Notes</a>
                        <a href="lecture_six_student_activities.html">Activities</a>
                    </div>
                </div>
                <div class="lecture-card">
                    <div class="lecture-number">07</div>
                    <h3>Therapeutic & Life Support Equipment</h3>
                    <p>Understanding the principles, applications, and management of therapeutic and life support technologies.</p>
                    <div class="lecture-links">
                        <a href="lecture_seven_therapeutic_equipment.html">Main Content</a>
                        <a href="lecture_seven_detailed_notes.html">Detailed Notes</a>
                        <a href="lecture_seven_student_activities.html">Activities</a>
                    </div>
                </div>
                <div class="lecture-card">
                    <div class="lecture-number">08</div>
                    <h3>Manufacturing Processes & Product Lifecycle</h3>
                    <p>Understanding medical device manufacturing, quality systems, and lifecycle management in clinical engineering.</p>
                    <div class="lecture-links">
                        <a href="lecture_eight_manufacturing_lifecycle.html">Main Content</a>
                        <a href="lecture_eight_detailed_notes.html">Detailed Notes</a>
                        <a href="lecture_eight_student_activities.html">Activities</a>
                    </div>
                </div>
                <div class="lecture-card">
                    <div class="lecture-number">09</div>
                    <h3>Healthcare Technology Assessment & Evaluation</h3>
                    <p>Systematic approaches to evaluating healthcare technologies for clinical and economic effectiveness.</p>
                    <div class="lecture-links">
                        <a href="lecture_nine_technology_assessment.html">Main Content</a>
                        <a href="lecture_nine_detailed_notes.html">Detailed Notes</a>
                        <a href="lecture_nine_student_activities.html">Activities</a>
                    </div>
                </div>
                <div class="lecture-card">
                    <div class="lecture-number">10</div>
                    <h3>Clinical Engineering Management & Leadership</h3>
                    <p>Developing leadership skills and management competencies for clinical engineering professionals.</p>
                    <div class="lecture-links">
                        <a href="lecture_ten_management_leadership.html">Main Content</a>
                        <a href="lecture_ten_detailed_notes.html">Detailed Notes</a>
                        <a href="lecture_ten_student_activities.html">Activities</a>
                    </div>
                </div>
                <div class="lecture-card">
                    <div class="lecture-number">11</div>
                    <h3>Emerging Technologies & Future Trends</h3>
                    <p>Exploring cutting-edge technologies and their impact on clinical engineering and healthcare delivery.</p>
                    <div class="lecture-links">
                        <a href="lecture_eleven_emerging_technologies.html">Main Content</a>
                        <a href="lecture_eleven_detailed_notes.html">Detailed Notes</a>
                        <a href="lecture_eleven_student_activities.html">Activities</a>
                    </div>
                </div>
                <div class="lecture-card">
                    <div class="lecture-number">12</div>
                    <h3>Professional Development & Career Pathways</h3>
                    <p>Building a successful career in clinical engineering through continuous learning and professional growth.</p>
                    <div class="lecture-links">
                        <a href="lecture_twelve_professional_development.html">Main Content</a>
                        <a href="lecture_twelve_detailed_notes.html">Detailed Notes</a>
                        <a href="lecture_twelve_student_activities.html">Activities</a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section id="simulations" class="section">
        <div class="container">
            <h2 class="section-title">Interactive Clinical Engineering Simulations</h2>
            <p class="section-description">Experience hands-on learning with our comprehensive clinical engineering simulation modules featuring advanced visual aids, real-time data analysis, and professional-grade instrumentation.</p>

            <!-- Simulation Features Overview -->
            <div style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); padding: 30px; border-radius: 12px; margin-bottom: 40px;">
                <h3 style="text-align: center; color: #0056b3; margin-bottom: 25px;">🚀 Advanced Simulation Features</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; text-align: center;">
                    <div style="padding: 15px;">
                        <div style="font-size: 2.5em; color: #0056b3; margin-bottom: 10px;">📊</div>
                        <h4 style="margin: 10px 0; color: #0056b3;">Real-time Data</h4>
                        <p style="margin: 0; font-size: 0.9em;">Live ECG waveforms, vital signs monitoring, and trend analysis</p>
                    </div>
                    <div style="padding: 15px;">
                        <div style="font-size: 2.5em; color: #28a745; margin-bottom: 10px;">⚙️</div>
                        <h4 style="margin: 10px 0; color: #0056b3;">Virtual Instruments</h4>
                        <p style="margin: 0; font-size: 0.9em;">Professional calibrators, multimeters, and test equipment</p>
                    </div>
                    <div style="padding: 15px;">
                        <div style="font-size: 2.5em; color: #ffc107; margin-bottom: 10px;">📈</div>
                        <h4 style="margin: 10px 0; color: #0056b3;">Statistical Analysis</h4>
                        <p style="margin: 0; font-size: 0.9em;">GUM uncertainty analysis, accuracy assessment, and reporting</p>
                    </div>
                    <div style="padding: 15px;">
                        <div style="font-size: 2.5em; color: #dc3545; margin-bottom: 10px;">🔧</div>
                        <h4 style="margin: 10px 0; color: #0056b3;">Troubleshooting</h4>
                        <p style="margin: 0; font-size: 0.9em;">Systematic diagnostic procedures and root cause analysis</p>
                    </div>
                </div>
            </div>

            <div class="simulations-grid">
                <div class="simulation-card" style="position: relative; overflow: hidden;">
                    <div style="background: linear-gradient(45deg, #000 0%, #003300 100%); height: 200px; position: relative; display: flex; align-items: center; justify-content: center;">
                        <div style="color: #00ff00; font-family: 'Courier New', monospace; text-align: center;">
                            <div style="font-size: 1.2em; margin-bottom: 10px;">📺 PATIENT MONITOR</div>
                            <div style="font-size: 0.9em;">HR: 75 bpm | BP: 120/80 | SpO2: 98%</div>
                            <div style="width: 150px; height: 2px; background: #00ff00; margin: 10px auto; animation: pulse 1s infinite;"></div>
                        </div>
                    </div>
                    <h3>Medical Device Operation</h3>
                    <p><strong>Enhanced Features:</strong> Real-time ECG waveforms, multi-parameter monitoring, alarm systems, and patient scenario simulation with pathological conditions.</p>
                    <div style="margin: 10px 0; font-size: 0.9em; color: #6c757d;">
                        <span style="background: #e3f2fd; padding: 2px 6px; border-radius: 3px; margin-right: 5px;">ECG Simulation</span>
                        <span style="background: #f3e5f5; padding: 2px 6px; border-radius: 3px; margin-right: 5px;">Vital Signs</span>
                        <span style="background: #e8f5e8; padding: 2px 6px; border-radius: 3px;">Alarm Management</span>
                    </div>
                    <a href="clinical_engineering_simulation.html" class="btn primary">Launch Simulation</a>
                </div>

                <div class="simulation-card" style="position: relative; overflow: hidden;">
                    <div style="background: linear-gradient(45deg, #2c3e50 0%, #34495e 100%); height: 200px; position: relative; display: flex; align-items: center; justify-content: center;">
                        <div style="color: #00ff00; font-family: 'Courier New', monospace; text-align: center;">
                            <div style="font-size: 1.2em; margin-bottom: 10px;">⚙️ CALIBRATOR</div>
                            <div style="font-size: 1.5em; font-weight: bold;">100.0 mmHg</div>
                            <div style="font-size: 0.8em; margin-top: 5px;">REF STD | ±0.1 mmHg</div>
                        </div>
                    </div>
                    <h3>Calibration Laboratory</h3>
                    <p><strong>Enhanced Features:</strong> Step-by-step procedures, virtual reference standards, automated error calculations, and comprehensive documentation with pass/fail criteria.</p>
                    <div style="margin: 10px 0; font-size: 0.9em; color: #6c757d;">
                        <span style="background: #fff3e0; padding: 2px 6px; border-radius: 3px; margin-right: 5px;">Pressure Cal</span>
                        <span style="background: #fce4ec; padding: 2px 6px; border-radius: 3px; margin-right: 5px;">Temp Cal</span>
                        <span style="background: #e0f2f1; padding: 2px 6px; border-radius: 3px;">Safety Testing</span>
                    </div>
                    <a href="clinical_engineering_simulation.html#calibration" class="btn primary">Start Calibration</a>
                </div>

                <div class="simulation-card" style="position: relative; overflow: hidden;">
                    <div style="background: linear-gradient(45deg, #1a237e 0%, #3949ab 100%); height: 200px; position: relative; display: flex; align-items: center; justify-content: center;">
                        <div style="color: white; text-align: center;">
                            <div style="font-size: 1.2em; margin-bottom: 10px;">📊 STATISTICS</div>
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; font-size: 0.8em;">
                                <div>Mean: ±0.15</div>
                                <div>σ: ±0.23</div>
                                <div>CI: 95%</div>
                                <div>U: ±0.46</div>
                            </div>
                        </div>
                    </div>
                    <h3>Measurement & Analysis</h3>
                    <p><strong>Enhanced Features:</strong> GUM uncertainty analysis, statistical calculations, Bland-Altman plots, confidence intervals, and professional reporting capabilities.</p>
                    <div style="margin: 10px 0; font-size: 0.9em; color: #6c757d;">
                        <span style="background: #e8eaf6; padding: 2px 6px; border-radius: 3px; margin-right: 5px;">GUM Analysis</span>
                        <span style="background: #f3e5f5; padding: 2px 6px; border-radius: 3px; margin-right: 5px;">Statistics</span>
                        <span style="background: #e0f7fa; padding: 2px 6px; border-radius: 3px;">Reporting</span>
                    </div>
                    <a href="clinical_engineering_simulation.html#measurements" class="btn primary">Begin Measurements</a>
                </div>

                <div class="simulation-card" style="position: relative; overflow: hidden;">
                    <div style="background: linear-gradient(45deg, #b71c1c 0%, #d32f2f 100%); height: 200px; position: relative; display: flex; align-items: center; justify-content: center;">
                        <div style="color: white; text-align: center;">
                            <div style="font-size: 1.2em; margin-bottom: 10px;">🚨 ERROR E02</div>
                            <div style="font-size: 0.9em; margin-bottom: 10px;">BP Monitor Fault</div>
                            <div style="font-size: 0.8em;">Systematic Diagnosis Required</div>
                        </div>
                    </div>
                    <h3>Troubleshooting Scenarios</h3>
                    <p><strong>Enhanced Features:</strong> Real-world equipment failures, systematic diagnostic procedures, root cause analysis, CAPA documentation, and preventive measures.</p>
                    <div style="margin: 10px 0; font-size: 0.9em; color: #6c757d;">
                        <span style="background: #ffebee; padding: 2px 6px; border-radius: 3px; margin-right: 5px;">Diagnostics</span>
                        <span style="background: #fff8e1; padding: 2px 6px; border-radius: 3px; margin-right: 5px;">Root Cause</span>
                        <span style="background: #f1f8e9; padding: 2px 6px; border-radius: 3px;">CAPA</span>
                    </div>
                    <a href="clinical_engineering_simulation.html#troubleshooting" class="btn primary">Start Troubleshooting</a>
                </div>
            </div>

            <!-- Enhanced Learning Objectives Section -->
            <div style="margin-top: 40px;">
                <div style="text-align: center; padding: 30px; background: linear-gradient(135deg, #0056b3 0%, #00a0e9 100%); border-radius: 12px; color: white; margin-bottom: 30px;">
                    <h3 style="margin-bottom: 15px; font-size: 1.8em;">🎯 Comprehensive Learning Objectives</h3>
                    <p style="margin-bottom: 20px; font-size: 1.1em;">Master clinical engineering skills through interactive simulations with advanced visual aids and real-world scenarios</p>
                </div>

                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 25px;">
                    <div style="background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); border-left: 4px solid #0056b3;">
                        <h4 style="color: #0056b3; margin-bottom: 15px; display: flex; align-items: center;">
                            <span style="font-size: 1.5em; margin-right: 10px;">🔧</span>
                            Device Operation Mastery
                        </h4>
                        <ul style="margin: 0; padding-left: 20px; line-height: 1.6;">
                            <li><strong>Real-time Monitoring:</strong> ECG waveforms, vital signs, alarms</li>
                            <li><strong>Multi-parameter Systems:</strong> Patient monitors, ventilators</li>
                            <li><strong>Safety Protocols:</strong> Equipment operation procedures</li>
                            <li><strong>Data Interpretation:</strong> Clinical parameter analysis</li>
                            <li><strong>User Interface:</strong> Professional medical device controls</li>
                        </ul>
                    </div>

                    <div style="background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); border-left: 4px solid #28a745;">
                        <h4 style="color: #28a745; margin-bottom: 15px; display: flex; align-items: center;">
                            <span style="font-size: 1.5em; margin-right: 10px;">⚙️</span>
                            Advanced Calibration
                        </h4>
                        <ul style="margin: 0; padding-left: 20px; line-height: 1.6;">
                            <li><strong>Systematic Procedures:</strong> Step-by-step calibration protocols</li>
                            <li><strong>Virtual Instruments:</strong> Professional calibrators and standards</li>
                            <li><strong>Error Analysis:</strong> Automated calculations and tolerance checking</li>
                            <li><strong>Documentation:</strong> Professional calibration certificates</li>
                            <li><strong>Compliance:</strong> Regulatory standards and requirements</li>
                        </ul>
                    </div>

                    <div style="background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); border-left: 4px solid #ffc107;">
                        <h4 style="color: #e65100; margin-bottom: 15px; display: flex; align-items: center;">
                            <span style="font-size: 1.5em; margin-right: 10px;">📊</span>
                            Statistical Analysis
                        </h4>
                        <ul style="margin: 0; padding-left: 20px; line-height: 1.6;">
                            <li><strong>GUM Uncertainty:</strong> Complete uncertainty budget analysis</li>
                            <li><strong>Statistical Methods:</strong> Mean, std dev, confidence intervals</li>
                            <li><strong>Visual Analytics:</strong> Charts, graphs, trend analysis</li>
                            <li><strong>Performance Assessment:</strong> Accuracy and precision evaluation</li>
                            <li><strong>Professional Reporting:</strong> Comprehensive documentation</li>
                        </ul>
                    </div>

                    <div style="background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); border-left: 4px solid #dc3545;">
                        <h4 style="color: #dc3545; margin-bottom: 15px; display: flex; align-items: center;">
                            <span style="font-size: 1.5em; margin-right: 10px;">🔍</span>
                            Systematic Troubleshooting
                        </h4>
                        <ul style="margin: 0; padding-left: 20px; line-height: 1.6;">
                            <li><strong>Diagnostic Methodology:</strong> 6-step systematic approach</li>
                            <li><strong>Root Cause Analysis:</strong> Fishbone diagrams, 5-Why technique</li>
                            <li><strong>Equipment Scenarios:</strong> Real-world failure modes</li>
                            <li><strong>CAPA Documentation:</strong> Corrective and preventive actions</li>
                            <li><strong>Prevention Strategies:</strong> Maintenance optimization</li>
                        </ul>
                    </div>
                </div>

                <!-- Technical Specifications -->
                <div style="margin-top: 30px; padding: 25px; background: #f8f9fa; border-radius: 12px; border: 1px solid #dee2e6;">
                    <h4 style="color: #0056b3; margin-bottom: 20px; text-align: center;">📋 Technical Specifications & Standards</h4>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; text-align: center;">
                        <div>
                            <h5 style="color: #495057; margin-bottom: 10px;">Calibration Standards</h5>
                            <p style="margin: 0; font-size: 0.9em; color: #6c757d;">
                                ISO/IEC 17025<br>
                                AAMI Standards<br>
                                FDA Guidelines
                            </p>
                        </div>
                        <div>
                            <h5 style="color: #495057; margin-bottom: 10px;">Measurement Accuracy</h5>
                            <p style="margin: 0; font-size: 0.9em; color: #6c757d;">
                                ±0.1% Reference Standards<br>
                                GUM Uncertainty Analysis<br>
                                95% Confidence Intervals
                            </p>
                        </div>
                        <div>
                            <h5 style="color: #495057; margin-bottom: 10px;">Device Coverage</h5>
                            <p style="margin: 0; font-size: 0.9em; color: #6c757d;">
                                Patient Monitors<br>
                                Ventilators & Pumps<br>
                                Diagnostic Equipment
                            </p>
                        </div>
                        <div>
                            <h5 style="color: #495057; margin-bottom: 10px;">Data Analysis</h5>
                            <p style="margin: 0; font-size: 0.9em; color: #6c757d;">
                                Real-time Statistics<br>
                                Trend Analysis<br>
                                Professional Reporting
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- New Simulation Technology Section -->
    <section class="section" style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);">
        <div class="container">
            <h2 class="section-title" style="color: #0056b3;">🔬 Simulation Technology & Methodologies</h2>
            <p class="section-description">Explore the advanced technologies and mathematical foundations powering our clinical engineering simulations</p>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 30px; margin-top: 40px;">
                <!-- Mathematical Formulas -->
                <div style="background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
                    <h3 style="color: #0056b3; margin-bottom: 20px; display: flex; align-items: center;">
                        <span style="font-size: 1.5em; margin-right: 10px;">📐</span>
                        Mathematical Foundations
                    </h3>
                    <div style="background: #f8f9ff; padding: 15px; border-radius: 8px; font-family: 'Times New Roman', serif;">
                        <h4 style="margin-bottom: 10px; color: #495057;">Calibration Formulas:</h4>
                        <p style="margin: 5px 0; font-size: 0.9em;"><strong>Error:</strong> E = X<sub>device</sub> - X<sub>reference</sub></p>
                        <p style="margin: 5px 0; font-size: 0.9em;"><strong>% Error:</strong> %E = (E / X<sub>ref</sub>) × 100%</p>
                        <p style="margin: 5px 0; font-size: 0.9em;"><strong>Uncertainty:</strong> U = k × u<sub>c</sub>(y)</p>

                        <h4 style="margin: 15px 0 10px 0; color: #495057;">Statistical Analysis:</h4>
                        <p style="margin: 5px 0; font-size: 0.9em;"><strong>Mean:</strong> x̄ = (Σx<sub>i</sub>) / n</p>
                        <p style="margin: 5px 0; font-size: 0.9em;"><strong>Std Dev:</strong> σ = √[Σ(x<sub>i</sub> - x̄)² / (n-1)]</p>
                        <p style="margin: 5px 0; font-size: 0.9em;"><strong>CI:</strong> x̄ ± (t × SE)</p>
                    </div>
                </div>

                <!-- Simulation Technologies -->
                <div style="background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
                    <h3 style="color: #28a745; margin-bottom: 20px; display: flex; align-items: center;">
                        <span style="font-size: 1.5em; margin-right: 10px;">💻</span>
                        Simulation Technologies
                    </h3>
                    <ul style="list-style: none; padding: 0;">
                        <li style="margin: 10px 0; padding: 10px; background: #f0f9e8; border-radius: 6px;">
                            <strong>Real-time ECG Generation:</strong> Canvas-based waveform rendering with physiological accuracy
                        </li>
                        <li style="margin: 10px 0; padding: 10px; background: #e8f4fd; border-radius: 6px;">
                            <strong>Virtual Instrumentation:</strong> Digital calibrators and measurement devices
                        </li>
                        <li style="margin: 10px 0; padding: 10px; background: #fff3e0; border-radius: 6px;">
                            <strong>Statistical Engine:</strong> GUM-compliant uncertainty calculations
                        </li>
                        <li style="margin: 10px 0; padding: 10px; background: #fce4ec; border-radius: 6px;">
                            <strong>Interactive Diagnostics:</strong> Systematic troubleshooting workflows
                        </li>
                    </ul>
                </div>

                <!-- Data Visualization -->
                <div style="background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
                    <h3 style="color: #ffc107; margin-bottom: 20px; display: flex; align-items: center;">
                        <span style="font-size: 1.5em; margin-right: 10px;">📈</span>
                        Visual Analytics
                    </h3>
                    <div style="background: #000; color: #00ff00; padding: 15px; border-radius: 8px; font-family: 'Courier New', monospace; margin-bottom: 15px;">
                        <div style="text-align: center; font-size: 0.9em;">
                            ┌─ ECG Waveform Display ─┐<br>
                            │ ∩     ∩     ∩     ∩   │<br>
                            │∕ ∖   ∕ ∖   ∕ ∖   ∕ ∖  │<br>
                            └─────────────────────────┘<br>
                            HR: 75 bpm | Lead II
                        </div>
                    </div>
                    <ul style="margin: 0; padding-left: 20px; font-size: 0.9em;">
                        <li>Real-time waveform generation</li>
                        <li>Statistical distribution charts</li>
                        <li>Bland-Altman analysis plots</li>
                        <li>Uncertainty contribution diagrams</li>
                        <li>Professional measurement tables</li>
                    </ul>
                </div>
            </div>

            <!-- Standards and Compliance -->
            <div style="margin-top: 40px; padding: 25px; background: white; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
                <h3 style="color: #0056b3; margin-bottom: 20px; text-align: center;">📋 Standards & Compliance Framework</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
                    <div style="text-align: center; padding: 15px;">
                        <div style="font-size: 2em; color: #0056b3; margin-bottom: 10px;">🏛️</div>
                        <h4 style="margin: 10px 0; color: #495057;">Regulatory Standards</h4>
                        <p style="margin: 0; font-size: 0.9em; color: #6c757d;">
                            FDA 21 CFR Part 820<br>
                            ISO 13485:2016<br>
                            IEC 62304
                        </p>
                    </div>
                    <div style="text-align: center; padding: 15px;">
                        <div style="font-size: 2em; color: #28a745; margin-bottom: 10px;">⚖️</div>
                        <h4 style="margin: 10px 0; color: #495057;">Calibration Standards</h4>
                        <p style="margin: 0; font-size: 0.9em; color: #6c757d;">
                            ISO/IEC 17025:2017<br>
                            ANSI/AAMI ES60601<br>
                            NIST Traceability
                        </p>
                    </div>
                    <div style="text-align: center; padding: 15px;">
                        <div style="font-size: 2em; color: #ffc107; margin-bottom: 10px;">🔬</div>
                        <h4 style="margin: 10px 0; color: #495057;">Measurement Standards</h4>
                        <p style="margin: 0; font-size: 0.9em; color: #6c757d;">
                            GUM (JCGM 100:2008)<br>
                            ISO 5725 Series<br>
                            VIM (JCGM 200:2012)
                        </p>
                    </div>
                    <div style="text-align: center; padding: 15px;">
                        <div style="font-size: 2em; color: #dc3545; margin-bottom: 10px;">🛡️</div>
                        <h4 style="margin: 10px 0; color: #495057;">Safety Standards</h4>
                        <p style="margin: 0; font-size: 0.9em; color: #6c757d;">
                            IEC 60601 Series<br>
                            AAMI/ANSI ES60601<br>
                            ISO 14971:2019
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Simulation Statistics Section -->
    <section class="section" style="background: linear-gradient(135deg, #0056b3 0%, #00a0e9 100%); color: white;">
        <div class="container">
            <h2 class="section-title" style="color: white; margin-bottom: 40px;">📈 Simulation Capabilities & Performance</h2>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 30px; margin-bottom: 40px;">
                <div style="text-align: center; padding: 20px;">
                    <div style="font-size: 3em; font-weight: bold; margin-bottom: 10px;">12</div>
                    <h4 style="margin: 10px 0; color: rgba(255,255,255,0.9);">Comprehensive Lectures</h4>
                    <p style="margin: 0; color: rgba(255,255,255,0.7);">Complete curriculum coverage</p>
                </div>
                <div style="text-align: center; padding: 20px;">
                    <div style="font-size: 3em; font-weight: bold; margin-bottom: 10px;">4</div>
                    <h4 style="margin: 10px 0; color: rgba(255,255,255,0.9);">Simulation Modules</h4>
                    <p style="margin: 0; color: rgba(255,255,255,0.7);">Interactive learning environments</p>
                </div>
                <div style="text-align: center; padding: 20px;">
                    <div style="font-size: 3em; font-weight: bold; margin-bottom: 10px;">50+</div>
                    <h4 style="margin: 10px 0; color: rgba(255,255,255,0.9);">Virtual Instruments</h4>
                    <p style="margin: 0; color: rgba(255,255,255,0.7);">Professional test equipment</p>
                </div>
                <div style="text-align: center; padding: 20px;">
                    <div style="font-size: 3em; font-weight: bold; margin-bottom: 10px;">∞</div>
                    <h4 style="margin: 10px 0; color: rgba(255,255,255,0.9);">Scenarios</h4>
                    <p style="margin: 0; color: rgba(255,255,255,0.7);">Unlimited practice opportunities</p>
                </div>
            </div>

            <!-- Technical Specifications -->
            <div style="background: rgba(255,255,255,0.1); padding: 30px; border-radius: 12px; backdrop-filter: blur(10px);">
                <h3 style="text-align: center; margin-bottom: 30px; color: white;">🔬 Technical Specifications</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 25px;">
                    <div>
                        <h4 style="color: rgba(255,255,255,0.9); margin-bottom: 15px;">Measurement Accuracy</h4>
                        <ul style="color: rgba(255,255,255,0.8); margin: 0; padding-left: 20px;">
                            <li>Reference Standards: ±0.1% accuracy</li>
                            <li>Calibration Uncertainty: ±0.05%</li>
                            <li>Statistical Confidence: 95% CI</li>
                            <li>GUM Compliance: Full uncertainty budget</li>
                        </ul>
                    </div>
                    <div>
                        <h4 style="color: rgba(255,255,255,0.9); margin-bottom: 15px;">Device Coverage</h4>
                        <ul style="color: rgba(255,255,255,0.8); margin: 0; padding-left: 20px;">
                            <li>Patient Monitors: Multi-parameter</li>
                            <li>ECG Systems: 12-lead capability</li>
                            <li>Ventilators: Volume/Pressure modes</li>
                            <li>Infusion Pumps: Various flow rates</li>
                        </ul>
                    </div>
                    <div>
                        <h4 style="color: rgba(255,255,255,0.9); margin-bottom: 15px;">Analysis Tools</h4>
                        <ul style="color: rgba(255,255,255,0.8); margin: 0; padding-left: 20px;">
                            <li>Real-time Statistics: Mean, SD, CV</li>
                            <li>Trend Analysis: Performance tracking</li>
                            <li>Bland-Altman Plots: Agreement analysis</li>
                            <li>Distribution Charts: Error analysis</li>
                        </ul>
                    </div>
                    <div>
                        <h4 style="color: rgba(255,255,255,0.9); margin-bottom: 15px;">Compliance Standards</h4>
                        <ul style="color: rgba(255,255,255,0.8); margin: 0; padding-left: 20px;">
                            <li>ISO/IEC 17025: Calibration labs</li>
                            <li>FDA 21 CFR 820: Quality systems</li>
                            <li>IEC 60601: Medical device safety</li>
                            <li>AAMI Standards: Biomedical equipment</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section id="resources" class="section">
        <div class="container">
            <h2 class="section-title">Additional Resources</h2>
            <div class="resources-grid">
                <div class="resource-card">
                    <div class="resource-icon"><i class="fas fa-file-pdf"></i></div>
                    <h3>Course Materials</h3>
                    <p>Access lecture slides, handouts, and supplementary reading materials.</p>
                    <a href="#" class="resource-link">View Materials</a>
                </div>
                <div class="resource-card">
                    <div class="resource-icon"><i class="fas fa-video"></i></div>
                    <h3>Video Tutorials</h3>
                    <p>Watch instructional videos on clinical engineering concepts and practices.</p>
                    <a href="#" class="resource-link">Watch Videos</a>
                </div>
                <div class="resource-card">
                    <div class="resource-icon"><i class="fas fa-book"></i></div>
                    <h3>Recommended Reading</h3>
                    <p>Explore textbooks, journals, and articles related to clinical engineering.</p>
                    <a href="#" class="resource-link">View Reading List</a>
                </div>
                <div class="resource-card">
                    <div class="resource-icon"><i class="fas fa-link"></i></div>
                    <h3>External Links</h3>
                    <p>Access professional organizations, regulatory bodies, and industry resources.</p>
                    <a href="#" class="resource-link">Browse Links</a>
                </div>
            </div>
        </div>
    </section>

    <section id="about" class="section">
        <div class="container">
            <h2 class="section-title">About This Course</h2>
            <div class="about-content">
                <div class="about-text">
                    <p>The Clinical Engineering and Simulation course is designed to provide students with a comprehensive understanding of the principles, practices, and challenges in the field of clinical engineering. Through a combination of theoretical learning and practical simulations, students will develop the knowledge and skills necessary to manage healthcare technology effectively and safely.</p>
                    <p>This course covers the entire spectrum of clinical engineering, from basic principles to advanced concepts, regulatory compliance, risk management, and future trends in healthcare technology.</p>
                    <h3>Learning Objectives</h3>
                    <ul>
                        <li>Understand the role of clinical engineering in healthcare delivery</li>
                        <li>Apply principles of medical equipment management throughout the technology lifecycle</li>
                        <li>Interpret and implement regulatory standards and guidelines</li>
                        <li>Develop risk management strategies for healthcare technology</li>
                        <li>Gain practical experience through interactive simulations</li>
                    </ul>
                </div>
                <div class="instructor-info">
                    <img src="https://via.placeholder.com/150" alt="Dr. Mohammed Yagoub Esmail" class="instructor-image">
                    <h3>Course Instructor</h3>
                    <p>Dr. Mohammed Yagoub Esmail</p>
                    <p>Professor of Clinical Engineering</p>
                    <p>Email: <EMAIL></p>
                    <p>Phone: +************ | +************</p>
                </div>
            </div>
        </div>
    </section>

    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-logo">
                    <h2>Clinical Engineering and Simulation</h2>
                    <p>© 2025 All Rights Reserved</p>
                </div>
                <div class="footer-links">
                    <h3>Quick Links</h3>
                    <ul>
                        <li><a href="#">Home</a></li>
                        <li><a href="#lectures">Lectures</a></li>
                        <li><a href="#simulations">Simulations</a></li>
                        <li><a href="#resources">Resources</a></li>
                        <li><a href="#about">About</a></li>
                    </ul>
                </div>
                <div class="footer-contact">
                    <h3>Contact Us</h3>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-phone"></i> +************ | +************</p>
                    <p><i class="fas fa-map-marker-alt"></i> Nahda College, Sudan</p>
                </div>
            </div>
        </div>
    </footer>

    <script src="JS/main.js"></script>
</body>
</html>