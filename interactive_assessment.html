<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التقييم التفاعلي - الهندسة السريرية | Interactive Assessment - Clinical Engineering</title>
    <style>
        body {
            font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            direction: rtl;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        
        header {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            color: white;
            padding: 30px 0;
            text-align: center;
            border-radius: 15px;
            margin-bottom: 30px;
        }
        
        .assessment-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        
        .assessment-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
        }
        
        .question {
            font-size: 1.2rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 20px;
            padding: 15px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 10px;
            border-right: 4px solid #3498db;
        }
        
        .options {
            display: grid;
            gap: 12px;
            margin-bottom: 20px;
        }
        
        .option {
            padding: 15px 20px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }
        
        .option:hover {
            border-color: #3498db;
            background: #e3f2fd;
        }
        
        .option.selected {
            border-color: #3498db;
            background: #3498db;
            color: white;
        }
        
        .option.correct {
            border-color: #27ae60;
            background: #27ae60;
            color: white;
        }
        
        .option.incorrect {
            border-color: #e74c3c;
            background: #e74c3c;
            color: white;
        }
        
        .explanation {
            background: linear-gradient(135deg, #e8f5e9 0%, #c8e6c9 100%);
            padding: 20px;
            border-radius: 10px;
            margin-top: 15px;
            border-right: 4px solid #27ae60;
            display: none;
        }
        
        .explanation.show {
            display: block;
            animation: fadeIn 0.5s ease;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .btn {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            margin: 5px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(52, 152, 219, 0.3);
        }
        
        .btn-success {
            background: linear-gradient(45deg, #27ae60, #2ecc71);
        }
        
        .btn-danger {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
        }
        
        .progress-container {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .progress-bar {
            background: #ecf0f1;
            border-radius: 25px;
            height: 20px;
            overflow: hidden;
            margin-bottom: 15px;
        }
        
        .progress-fill {
            background: linear-gradient(90deg, #27ae60, #2ecc71);
            height: 100%;
            width: 0%;
            transition: width 0.5s ease;
            border-radius: 25px;
        }
        
        .score-display {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .score-item {
            text-align: center;
            padding: 15px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 10px;
        }
        
        .score-number {
            font-size: 2rem;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .score-label {
            color: #7f8c8d;
            font-size: 0.9rem;
        }
        
        .nav-buttons {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 30px;
            flex-wrap: wrap;
            gap: 15px;
        }
        
        .home-btn {
            position: fixed;
            bottom: 20px;
            left: 20px;
            background: linear-gradient(45deg, #9b59b6, #8e44ad);
            color: white;
            border: none;
            padding: 12px 16px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: 600;
            font-size: 0.9em;
            line-height: 1.2;
            text-align: center;
            box-shadow: 0 4px 12px rgba(155, 89, 182, 0.3);
            transition: all 0.3s ease;
            z-index: 1000;
            min-width: 80px;
        }
        
        .home-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 16px rgba(155, 89, 182, 0.4);
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }
            
            .assessment-card {
                padding: 20px;
            }
            
            .nav-buttons {
                flex-direction: column;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>التقييم التفاعلي للهندسة السريرية</h1>
            <p style="font-size: 1.1rem; margin-bottom: 20px;">Interactive Assessment for Clinical Engineering</p>
            <div style="display: flex; justify-content: center; gap: 15px; flex-wrap: wrap;">
                <a href="index.html" style="background: rgba(255,255,255,0.2); color: white; padding: 8px 16px; border-radius: 20px; text-decoration: none;">🏠 الرئيسية | Home</a>
                <a href="arabic_course_lectures.html" style="background: rgba(255,255,255,0.2); color: white; padding: 8px 16px; border-radius: 20px; text-decoration: none;">📚 المحاضرات</a>
                <a href="model_answers_activities.html" style="background: rgba(255,255,255,0.2); color: white; padding: 8px 16px; border-radius: 20px; text-decoration: none;">📝 الإجابات النموذجية</a>
            </div>
        </header>

        <!-- Progress Container -->
        <div class="progress-container">
            <h3 style="text-align: center; color: #2c3e50; margin-bottom: 20px;">تقدم التقييم | Assessment Progress</h3>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div style="text-align: center; color: #7f8c8d;">
                <span id="currentQuestion">1</span> من <span id="totalQuestions">10</span> أسئلة
            </div>
            
            <div class="score-display">
                <div class="score-item">
                    <div class="score-number" id="correctAnswers">0</div>
                    <div class="score-label">إجابات صحيحة | Correct</div>
                </div>
                <div class="score-item">
                    <div class="score-number" id="incorrectAnswers">0</div>
                    <div class="score-label">إجابات خاطئة | Incorrect</div>
                </div>
                <div class="score-item">
                    <div class="score-number" id="scorePercentage">0%</div>
                    <div class="score-label">النتيجة | Score</div>
                </div>
                <div class="score-item">
                    <div class="score-number" id="timeElapsed">0:00</div>
                    <div class="score-label">الوقت المنقضي | Time</div>
                </div>
            </div>
        </div>

        <!-- Assessment Questions Container -->
        <div id="questionsContainer">
            <!-- Questions will be loaded here dynamically -->
        </div>

        <!-- Navigation Buttons -->
        <div class="nav-buttons">
            <button class="btn" onclick="previousQuestion()" id="prevBtn" disabled>السؤال السابق | Previous</button>
            <div>
                <button class="btn btn-success" onclick="submitAnswer()" id="submitBtn">تأكيد الإجابة | Submit Answer</button>
                <button class="btn" onclick="nextQuestion()" id="nextBtn" disabled>السؤال التالي | Next</button>
            </div>
            <button class="btn btn-danger" onclick="resetAssessment()">إعادة البدء | Reset</button>
        </div>
    </div>

    <!-- Home Button -->
    <button class="home-btn" onclick="window.location.href='index.html'">
        🏠 الرئيسية<br>Home
    </button>

    <script>
        // Assessment data and state
        let currentQuestionIndex = 0;
        let userAnswers = [];
        let correctCount = 0;
        let incorrectCount = 0;
        let startTime = Date.now();
        let timerInterval;
        
        // Comprehensive questions database
        const questions = [
            {
                question: "ما هو الهدف الأساسي من الهندسة السريرية؟ | What is the primary goal of clinical engineering?",
                options: [
                    "صيانة الأجهزة الطبية فقط | Only maintaining medical devices",
                    "ضمان الاستخدام الآمن والفعال للتكنولوجيا الطبية | Ensuring safe and effective use of medical technology",
                    "شراء المعدات الطبية | Purchasing medical equipment",
                    "تدريب الأطباء على الأجهزة | Training doctors on devices"
                ],
                correct: 1,
                explanation: "الهدف الأساسي من الهندسة السريرية هو ضمان الاستخدام الآمن والفعال للتكنولوجيا الطبية في البيئة السريرية، مما يشمل الصيانة والإدارة والتدريب وضمان الجودة."
            },
            {
                question: "أي من المعايير التالية يُستخدم لإدارة المخاطر في الأجهزة الطبية؟ | Which standard is used for risk management in medical devices?",
                options: [
                    "ISO 9001",
                    "ISO 14971",
                    "ISO 27001",
                    "ISO 45001"
                ],
                correct: 1,
                explanation: "معيار ISO 14971 هو المعيار الدولي المخصص لإدارة المخاطر في الأجهزة الطبية، ويوفر إطار عمل منهجي لتحديد وتقييم وتخفيف المخاطر."
            },
            {
                question: "ما هو CMMS في سياق إدارة المعدات الطبية؟ | What is CMMS in the context of medical equipment management?",
                options: [
                    "نظام إدارة الصيانة المحوسب | Computerized Maintenance Management System",
                    "نظام مراقبة المرضى | Clinical Monitoring Management System",
                    "نظام إدارة التكاليف الطبية | Cost Management Medical System",
                    "نظام الاتصالات الطبية | Clinical Medical Communication System"
                ],
                correct: 0,
                explanation: "CMMS يرمز إلى نظام إدارة الصيانة المحوسب، وهو نظام برمجي يساعد في تنظيم وإدارة أنشطة الصيانة الوقائية والتصحيحية للمعدات الطبية."
            },
            {
                question: "ما هي منهجية GUM المستخدمة في المعايرة؟ | What is the GUM methodology used in calibration?",
                options: [
                    "دليل قياس الجودة | Guide to Quality Measurement",
                    "دليل التعبير عن عدم اليقين في القياس | Guide to the Expression of Uncertainty in Measurement",
                    "نظام إدارة الجودة العالمي | Global Quality Management System",
                    "معيار القياس الموحد | General Unified Measurement standard"
                ],
                correct: 1,
                explanation: "GUM هو دليل التعبير عن عدم اليقين في القياس، وهو منهجية دولية معتمدة لحساب وتقدير عدم اليقين في نتائج القياس والمعايرة."
            },
            {
                question: "أي من التالي يُعتبر من مسؤوليات المهندس السريري الأساسية؟ | Which of the following is a primary responsibility of a clinical engineer?",
                options: [
                    "إجراء العمليات الجراحية | Performing surgical operations",
                    "تشخيص المرضى | Diagnosing patients",
                    "إدارة التكنولوجيا الطبية وضمان سلامتها | Managing medical technology and ensuring its safety",
                    "وصف الأدوية للمرضى | Prescribing medications to patients"
                ],
                correct: 2,
                explanation: "المهندس السريري مسؤول عن إدارة التكنولوجيا الطبية وضمان سلامتها وفعاليتها، بما في ذلك الصيانة والمعايرة والتدريب وإدارة المخاطر."
            },
            {
                question: "ما هو FMEA في سياق إدارة المخاطر؟ | What is FMEA in the context of risk management?",
                options: [
                    "تحليل أنماط الفشل وتأثيراتها | Failure Mode and Effects Analysis",
                    "تقييم المعدات الطبية الوظيفي | Functional Medical Equipment Assessment",
                    "إدارة الملفات الطبية الإلكترونية | File Management Electronic Archive",
                    "تحليل التكلفة المالية للمعدات | Financial Medical Equipment Analysis"
                ],
                correct: 0,
                explanation: "FMEA هو تحليل أنماط الفشل وتأثيراتها، وهو أسلوب منهجي لتحديد أنماط الفشل المحتملة في النظام أو العملية وتقييم تأثيراتها على الأداء والسلامة."
            },
            {
                question: "ما هو TCO في إدارة المعدات الطبية؟ | What is TCO in medical equipment management?",
                options: [
                    "التكلفة الإجمالية للملكية | Total Cost of Ownership",
                    "مراقبة التشغيل التقني | Technical Operation Control",
                    "تحسين التكلفة التشغيلية | Total Cost Optimization",
                    "التحكم في العمليات التقنية | Technical Operations Control"
                ],
                correct: 0,
                explanation: "TCO يعني التكلفة الإجمالية للملكية، وهو مفهوم يشمل جميع التكاليف المرتبطة بالمعدة طوال دورة حياتها، من الشراء إلى التخلص النهائي."
            },
            {
                question: "أي من المعايير التالية يُستخدم لأنظمة إدارة الجودة في الأجهزة الطبية؟ | Which standard is used for quality management systems in medical devices?",
                options: [
                    "ISO 9001",
                    "ISO 13485",
                    "ISO 27001",
                    "ISO 14001"
                ],
                correct: 1,
                explanation: "معيار ISO 13485 هو المعيار المخصص لأنظمة إدارة الجودة في الأجهزة الطبية، وهو مبني على ISO 9001 مع متطلبات إضافية خاصة بالصناعات الطبية."
            },
            {
                question: "ما هو الهدف من الصيانة الوقائية؟ | What is the purpose of preventive maintenance?",
                options: [
                    "إصلاح الأعطال بعد حدوثها | Repairing failures after they occur",
                    "منع حدوث الأعطال وضمان الأداء الأمثل | Preventing failures and ensuring optimal performance",
                    "تقليل تكلفة الشراء | Reducing purchase costs",
                    "زيادة سرعة الإصلاح | Increasing repair speed"
                ],
                correct: 1,
                explanation: "الصيانة الوقائية تهدف إلى منع حدوث الأعطال قبل وقوعها وضمان الأداء الأمثل للمعدات، مما يقلل من التوقف غير المخطط له ويطيل عمر المعدة."
            },
            {
                question: "ما هي أهمية إمكانية التتبع في المعايرة؟ | What is the importance of traceability in calibration?",
                options: [
                    "تقليل تكلفة المعايرة | Reducing calibration costs",
                    "ضمان دقة القياسات وربطها بالمعايير الدولية | Ensuring measurement accuracy and linking to international standards",
                    "تسريع عملية المعايرة | Speeding up the calibration process",
                    "تقليل عدد المعايرات المطلوبة | Reducing the number of required calibrations"
                ],
                correct: 1,
                explanation: "إمكانية التتبع تضمن أن جميع القياسات يمكن ربطها بسلسلة متصلة من المقارنات وصولاً إلى المعايير الدولية، مما يضمن دقة وموثوقية النتائج."
            }
        ];
        
        // Initialize assessment
        function initializeAssessment() {
            document.getElementById('totalQuestions').textContent = questions.length;
            loadQuestion();
            startTimer();
        }
        
        // Load current question
        function loadQuestion() {
            const question = questions[currentQuestionIndex];
            const container = document.getElementById('questionsContainer');
            
            container.innerHTML = `
                <div class="assessment-card">
                    <div class="question">${question.question}</div>
                    <div class="options">
                        ${question.options.map((option, index) => `
                            <div class="option" onclick="selectOption(${index})" data-index="${index}">
                                ${String.fromCharCode(65 + index)}. ${option}
                            </div>
                        `).join('')}
                    </div>
                    <div class="explanation" id="explanation">
                        <strong>التفسير | Explanation:</strong><br>
                        ${question.explanation}
                    </div>
                </div>
            `;
            
            updateProgress();
            updateNavigationButtons();
        }
        
        // Select option
        function selectOption(index) {
            const options = document.querySelectorAll('.option');
            options.forEach(opt => opt.classList.remove('selected'));
            options[index].classList.add('selected');
            
            document.getElementById('submitBtn').disabled = false;
        }
        
        // Submit answer
        function submitAnswer() {
            const selectedOption = document.querySelector('.option.selected');
            if (!selectedOption) return;
            
            const selectedIndex = parseInt(selectedOption.dataset.index);
            const question = questions[currentQuestionIndex];
            const isCorrect = selectedIndex === question.correct;
            
            userAnswers[currentQuestionIndex] = {
                selected: selectedIndex,
                correct: isCorrect
            };
            
            // Show results
            const options = document.querySelectorAll('.option');
            options.forEach((opt, index) => {
                opt.style.pointerEvents = 'none';
                if (index === question.correct) {
                    opt.classList.add('correct');
                } else if (index === selectedIndex && !isCorrect) {
                    opt.classList.add('incorrect');
                }
            });
            
            // Update counters
            if (isCorrect) {
                correctCount++;
            } else {
                incorrectCount++;
            }
            
            // Show explanation
            document.getElementById('explanation').classList.add('show');
            
            // Update UI
            updateScoreDisplay();
            document.getElementById('submitBtn').disabled = true;
            document.getElementById('nextBtn').disabled = false;
        }
        
        // Navigation functions
        function nextQuestion() {
            if (currentQuestionIndex < questions.length - 1) {
                currentQuestionIndex++;
                loadQuestion();
            } else {
                showFinalResults();
            }
        }
        
        function previousQuestion() {
            if (currentQuestionIndex > 0) {
                currentQuestionIndex--;
                loadQuestion();
                
                // Restore previous answer if exists
                const prevAnswer = userAnswers[currentQuestionIndex];
                if (prevAnswer) {
                    const options = document.querySelectorAll('.option');
                    options[prevAnswer.selected].classList.add('selected');
                    submitAnswer();
                }
            }
        }
        
        // Update progress and UI
        function updateProgress() {
            const progress = ((currentQuestionIndex + 1) / questions.length) * 100;
            document.getElementById('progressFill').style.width = progress + '%';
            document.getElementById('currentQuestion').textContent = currentQuestionIndex + 1;
        }
        
        function updateNavigationButtons() {
            document.getElementById('prevBtn').disabled = currentQuestionIndex === 0;
            document.getElementById('nextBtn').disabled = true;
            document.getElementById('submitBtn').disabled = true;
        }
        
        function updateScoreDisplay() {
            document.getElementById('correctAnswers').textContent = correctCount;
            document.getElementById('incorrectAnswers').textContent = incorrectCount;
            
            const totalAnswered = correctCount + incorrectCount;
            const percentage = totalAnswered > 0 ? Math.round((correctCount / totalAnswered) * 100) : 0;
            document.getElementById('scorePercentage').textContent = percentage + '%';
        }
        
        // Timer functions
        function startTimer() {
            timerInterval = setInterval(() => {
                const elapsed = Date.now() - startTime;
                const minutes = Math.floor(elapsed / 60000);
                const seconds = Math.floor((elapsed % 60000) / 1000);
                document.getElementById('timeElapsed').textContent = 
                    `${minutes}:${seconds.toString().padStart(2, '0')}`;
            }, 1000);
        }
        
        // Show final results
        function showFinalResults() {
            clearInterval(timerInterval);
            const percentage = Math.round((correctCount / questions.length) * 100);
            
            let grade, message;
            if (percentage >= 90) {
                grade = "ممتاز | Excellent";
                message = "أداء رائع! لديك فهم عميق للهندسة السريرية";
            } else if (percentage >= 80) {
                grade = "جيد جداً | Very Good";
                message = "أداء جيد! تحتاج لمراجعة بعض المفاهيم";
            } else if (percentage >= 70) {
                grade = "جيد | Good";
                message = "أداء مقبول، يُنصح بمراجعة المحاضرات";
            } else {
                grade = "يحتاج تحسين | Needs Improvement";
                message = "يُنصح بمراجعة المحاضرات والتدرب أكثر";
            }
            
            alert(`النتيجة النهائية | Final Score: ${percentage}%\nالتقدير: ${grade}\n${message}`);
        }
        
        // Reset assessment
        function resetAssessment() {
            if (confirm('هل تريد إعادة بدء التقييم؟ | Do you want to restart the assessment?')) {
                currentQuestionIndex = 0;
                userAnswers = [];
                correctCount = 0;
                incorrectCount = 0;
                startTime = Date.now();
                clearInterval(timerInterval);
                initializeAssessment();
            }
        }
        
        // Initialize on page load
        document.addEventListener('DOMContentLoaded', initializeAssessment);
    </script>
</body>
</html>
