/* Lecture-specific CSS for Clinical Engineering and Simulation 2025 */

/* Lecture Header */
.lecture-header {
    background-color: var(--primary-color);
    color: var(--light-text);
    padding: 60px 0 30px;
}

.lecture-header h1 {
    font-size: 2.5rem;
    margin-bottom: 15px;
}

.lecture-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    font-size: 1rem;
    opacity: 0.8;
}

.lecture-meta span {
    display: flex;
    align-items: center;
}

.lecture-meta i {
    margin-right: 8px;
    color: var(--secondary-color);
}

/* Lecture Navigation */
.lecture-navigation {
    background-color: #fff;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 70px;
    z-index: 900;
}

.lecture-navigation ul {
    display: flex;
    overflow-x: auto;
    padding: 0;
    margin: 0;
    white-space: nowrap;
}

.lecture-navigation ul li {
    margin: 0;
}

.lecture-navigation ul li a {
    display: block;
    padding: 15px 20px;
    color: var(--primary-color);
    font-weight: 500;
    position: relative;
}

.lecture-navigation ul li a::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 3px;
    background-color: var(--secondary-color);
    transition: var(--transition);
}

.lecture-navigation ul li a:hover::after,
.lecture-navigation ul li a.active::after {
    width: 100%;
}

/* Lecture Content */
.lecture-content {
    padding: 60px 0;
    background-color: #f9f9f9;
}

.lecture-section {
    margin-bottom: 60px;
    background-color: #fff;
    border-radius: var(--border-radius);
    padding: 40px;
    box-shadow: var(--box-shadow);
}

.lecture-section h2 {
    color: var(--primary-color);
    font-size: 2rem;
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: 2px solid #f0f0f0;
}

.content-block {
    margin-bottom: 40px;
}

.content-block:last-child {
    margin-bottom: 0;
}

.content-block h3 {
    color: var(--primary-color);
    font-size: 1.5rem;
    margin-bottom: 20px;
}

.content-block p {
    margin-bottom: 15px;
    line-height: 1.7;
}

.content-block ul,
.content-block ol {
    margin: 15px 0 15px 20px;
}

.content-block li {
    margin-bottom: 10px;
    line-height: 1.6;
}

/* Special Content Boxes */
.info-box,
.example-box,
.warning-box {
    margin: 25px 0;
    padding: 20px;
    border-radius: var(--border-radius);
    position: relative;
}

.info-box {
    background-color: #e3f2fd;
    border-left: 5px solid #2196f3;
}

.example-box {
    background-color: #f1f8e9;
    border-left: 5px solid #8bc34a;
}

.warning-box {
    background-color: #fff3e0;
    border-left: 5px solid #ff9800;
}

.info-box h4,
.example-box h4,
.warning-box h4 {
    margin-bottom: 10px;
    display: flex;
    align-items: center;
}

.info-box i,
.example-box i,
.warning-box i {
    margin-right: 10px;
}

/* Learning Objectives */
.objectives-list {
    list-style-type: none;
    margin-left: 0 !important;
}

.objectives-list li {
    position: relative;
    padding-left: 30px;
    margin-bottom: 15px;
}

.objectives-list li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--secondary-color);
    font-weight: bold;
    font-size: 1.2rem;
}

/* Activities */
.activity-box {
    background-color: #f5f5f5;
    border-radius: var(--border-radius);
    padding: 25px;
    margin-bottom: 30px;
    border-left: 5px solid var(--secondary-color);
}

.activity-box h3 {
    color: var(--primary-color);
    margin-bottom: 15px;
}

.activity-box p {
    margin-bottom: 15px;
}

.activity-box ol,
.activity-box ul {
    margin-left: 20px;
    margin-bottom: 20px;
}

.activity-box li {
    margin-bottom: 10px;
}

/* Resources */
.resources-list h3 {
    color: var(--primary-color);
    font-size: 1.3rem;
    margin: 25px 0 15px;
}

.resources-list ul {
    list-style-type: none;
    margin-left: 0 !important;
}

.resources-list li {
    margin-bottom: 15px;
    padding-left: 25px;
    position: relative;
}

.resources-list li::before {
    content: '•';
    position: absolute;
    left: 0;
    color: var(--secondary-color);
    font-size: 1.5rem;
    line-height: 1;
}

.resources-list a {
    font-weight: 500;
}

/* Bottom Navigation */
.lecture-navigation.bottom {
    position: static;
    margin-bottom: 60px;
    background-color: #f9f9f9;
    border-top: 1px solid #eee;
    box-shadow: none;
}

.nav-links {
    display: flex;
    justify-content: space-between;
    padding: 20px 0;
}

.prev-link,
.next-link,
.all-lectures {
    padding: 10px 15px;
    background-color: #fff;
    border-radius: var(--border-radius);
    color: var(--primary-color);
    font-weight: 500;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
}

.prev-link:hover,
.next-link:hover,
.all-lectures:hover {
    background-color: var(--secondary-color);
    color: var(--light-text);
    transform: translateY(-2px);
}

.prev-link i,
.next-link i,
.all-lectures i {
    margin: 0 5px;
}

/* Responsive Styles */
@media (max-width: 768px) {
    .lecture-header {
        padding: 40px 0 20px;
    }
    
    .lecture-header h1 {
        font-size: 2rem;
    }
    
    .lecture-meta {
        flex-direction: column;
        gap: 10px;
    }
    
    .lecture-navigation ul {
        justify-content: flex-start;
    }
    
    .lecture-navigation ul li a {
        padding: 12px 15px;
        font-size: 0.9rem;
    }
    
    .lecture-section {
        padding: 25px;
    }
    
    .lecture-section h2 {
        font-size: 1.7rem;
    }
    
    .content-block h3 {
        font-size: 1.3rem;
    }
    
    .nav-links {
        flex-direction: column;
        gap: 15px;
        align-items: center;
    }
}

@media (max-width: 576px) {
    .lecture-navigation {
        top: 60px;
    }
    
    .lecture-navigation ul li a {
        padding: 10px 12px;
        font-size: 0.8rem;
    }
    
    .info-box,
    .example-box,
    .warning-box,
    .activity-box {
        padding: 15px;
    }
}