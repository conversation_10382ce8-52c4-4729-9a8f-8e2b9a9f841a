<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الإجابات النموذجية - الهندسة السريرية | Model Answers - Clinical Engineering</title>
    <style>
        body {
            font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
            line-height: 1.8;
            color: #333;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            direction: rtl;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        header {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            color: white;
            padding: 40px 0;
            text-align: center;
            border-radius: 15px;
            margin-bottom: 30px;
            position: relative;
            overflow: hidden;
        }
        
        header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 30% 70%, rgba(255,255,255,0.1) 0%, transparent 50%);
        }
        
        .header-content {
            position: relative;
            z-index: 1;
        }
        
        .lecture-category {
            background: white;
            border-radius: 15px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
            transition: all 0.3s ease;
        }
        
        .lecture-category:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
        }
        
        .category-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px 30px;
            position: relative;
        }
        
        .category-title {
            font-size: 1.8rem;
            font-weight: 600;
            margin: 0 0 10px 0;
        }
        
        .category-subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            margin: 0;
            font-weight: 300;
        }
        
        .category-stats {
            position: absolute;
            top: 50%;
            left: 30px;
            transform: translateY(-50%);
            text-align: center;
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            line-height: 1;
        }
        
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.8;
        }
        
        .answers-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 25px;
            padding: 30px;
        }
        
        .answer-card {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 25px;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .answer-card:hover {
            border-color: #667eea;
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
        }
        
        .lecture-number {
            position: absolute;
            top: -15px;
            right: 20px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 1.2rem;
        }
        
        .lecture-title {
            font-size: 1.4rem;
            font-weight: 600;
            color: #2c3e50;
            margin: 20px 0 15px 0;
        }
        
        .lecture-description {
            color: #495057;
            margin-bottom: 20px;
            line-height: 1.6;
        }
        
        .activities-section {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            border-right: 4px solid #2196f3;
        }
        
        .activities-title {
            color: #1976d2;
            margin: 0 0 15px 0;
            font-size: 1.1rem;
            font-weight: 600;
        }
        
        .activity-item {
            background: rgba(255,255,255,0.8);
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 12px;
            border-right: 3px solid #2196f3;
        }
        
        .activity-title {
            font-weight: 600;
            color: #1976d2;
            margin-bottom: 8px;
        }
        
        .activity-answer {
            color: #495057;
            font-size: 0.95rem;
            line-height: 1.6;
        }
        
        .key-points {
            background: linear-gradient(135deg, #e8f5e9 0%, #c8e6c9 100%);
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            border-right: 4px solid #4caf50;
        }
        
        .key-points h5 {
            color: #2e7d32;
            margin: 0 0 15px 0;
            font-size: 1.1rem;
        }
        
        .key-points ul {
            margin: 0;
            padding-right: 20px;
        }
        
        .key-points li {
            margin-bottom: 8px;
            color: #495057;
            font-size: 0.95rem;
        }
        
        .answer-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            justify-content: center;
        }
        
        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            font-size: 0.9rem;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        }
        
        .btn-success {
            background: linear-gradient(45deg, #28a745, #20c997);
        }
        
        .btn-warning {
            background: linear-gradient(45deg, #ffc107, #ffb300);
        }
        
        .btn-info {
            background: linear-gradient(45deg, #17a2b8, #138496);
        }
        
        .search-container {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .search-input {
            width: 100%;
            padding: 15px 20px;
            border: 2px solid #e9ecef;
            border-radius: 25px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        
        .search-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .filter-buttons {
            display: flex;
            gap: 10px;
            margin-top: 15px;
            flex-wrap: wrap;
            justify-content: center;
        }
        
        .filter-btn {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            color: #495057;
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }
        
        .filter-btn:hover,
        .filter-btn.active {
            background: #667eea;
            border-color: #667eea;
            color: white;
        }
        
        .home-btn {
            position: fixed;
            bottom: 20px;
            left: 20px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 16px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: 600;
            font-size: 0.9em;
            line-height: 1.2;
            text-align: center;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
            transition: all 0.3s ease;
            z-index: 1000;
            min-width: 80px;
        }
        
        .home-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);
        }
        
        .language-switch {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            border: none;
            padding: 12px 16px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: 600;
            font-size: 0.9em;
            line-height: 1.2;
            text-align: center;
            box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3);
            transition: all 0.3s ease;
            z-index: 1000;
            min-width: 80px;
        }
        
        .language-switch:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 16px rgba(255, 107, 107, 0.4);
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }
            
            .answers-grid {
                grid-template-columns: 1fr;
                padding: 20px;
            }
            
            .answer-card {
                padding: 20px;
            }
            
            .category-stats {
                position: static;
                transform: none;
                margin-top: 15px;
            }
        }
        
        .fade-in {
            animation: fadeIn 0.6s ease-in;
        }
        
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <div class="header-content">
                <!-- Enhanced Navigation Bar -->
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px; padding: 15px 0; border-bottom: 1px solid rgba(255,255,255,0.2);">
                    <div>
                        <a href="index.html" style="background: rgba(255,255,255,0.2); color: white; padding: 10px 20px; border-radius: 25px; text-decoration: none; font-size: 1em; transition: all 0.3s ease; display: inline-flex; align-items: center; gap: 10px; font-weight: 500;"
                           onmouseover="this.style.background='rgba(255,255,255,0.3)'; this.style.transform='translateY(-2px)'"
                           onmouseout="this.style.background='rgba(255,255,255,0.2)'; this.style.transform='translateY(0)'">
                            🏠 العودة للرئيسية | Back to Home
                        </a>
                    </div>
                    <div style="display: flex; gap: 12px;">
                        <a href="arabic_course_lectures.html" style="background: rgba(255,255,255,0.15); color: white; padding: 8px 16px; border-radius: 20px; text-decoration: none; font-size: 0.9em; transition: all 0.3s ease;"
                           onmouseover="this.style.background='rgba(255,255,255,0.25)'"
                           onmouseout="this.style.background='rgba(255,255,255,0.15)'">📚 المحاضرات</a>
                        <a href="arabic_practical_exercises.html" style="background: rgba(255,255,255,0.15); color: white; padding: 8px 16px; border-radius: 20px; text-decoration: none; font-size: 0.9em; transition: all 0.3s ease;"
                           onmouseover="this.style.background='rgba(255,255,255,0.25)'"
                           onmouseout="this.style.background='rgba(255,255,255,0.15)'">🔬 التمارين</a>
                        <a href="interactive_assessment.html" style="background: rgba(255,255,255,0.15); color: white; padding: 8px 16px; border-radius: 20px; text-decoration: none; font-size: 0.9em; transition: all 0.3s ease;"
                           onmouseover="this.style.background='rgba(255,255,255,0.25)'"
                           onmouseout="this.style.background='rgba(255,255,255,0.15)'">🎯 التقييم</a>
                        <a href="analytics_dashboard.html" style="background: rgba(255,255,255,0.15); color: white; padding: 8px 16px; border-radius: 20px; text-decoration: none; font-size: 0.9em; transition: all 0.3s ease;"
                           onmouseover="this.style.background='rgba(255,255,255,0.25)'"
                           onmouseout="this.style.background='rgba(255,255,255,0.15)'">📊 التحليلات</a>
                    </div>
                </div>
                
                <h1>الإجابات النموذجية للهندسة السريرية</h1>
                <p style="font-size: 1.3rem; color: rgba(255,255,255,0.9); font-weight: 300; margin-bottom: 15px;">Model Answers for Clinical Engineering</p>
                <p style="font-size: 1.1rem; color: rgba(255,255,255,0.8); max-width: 800px; margin: 0 auto;">
                    مجموعة شاملة من الإجابات النموذجية لجميع أنشطة ومهام المحاضرات الـ 12
                </p>
                <p style="font-size: 1rem; color: rgba(255,255,255,0.7); max-width: 800px; margin: 10px auto 0;">
                    Comprehensive collection of model answers for all activities and tasks of the 12 lectures
                </p>
            </div>
        </header>

        <!-- Search and Filter Section -->
        <div class="search-container">
            <h3 style="text-align: center; color: #667eea; margin-bottom: 20px;">🔍 البحث والتصفية | Search & Filter</h3>
            <input type="text" class="search-input" id="searchInput" placeholder="البحث في الإجابات النموذجية... | Search in model answers..." onkeyup="searchAnswers()">
            
            <div class="filter-buttons">
                <button class="filter-btn active" onclick="filterByCategory('all')">جميع المحاضرات | All Lectures</button>
                <button class="filter-btn" onclick="filterByCategory('fundamentals')">الأساسيات | Fundamentals</button>
                <button class="filter-btn" onclick="filterByCategory('management')">الإدارة | Management</button>
                <button class="filter-btn" onclick="filterByCategory('advanced')">المتقدم | Advanced</button>
                <button class="filter-btn" onclick="filterByCategory('specialized')">التخصصي | Specialized</button>
            </div>
        </div>

        <!-- Fundamentals Category -->
        <div class="lecture-category" data-category="fundamentals">
            <div class="category-header">
                <div class="category-stats">
                    <div class="stat-number">3</div>
                    <div class="stat-label">محاضرات</div>
                </div>
                <h2 class="category-title">🎯 المحاضرات الأساسية</h2>
                <p class="category-subtitle">Fundamental Lectures - الأسس والمبادئ الأساسية</p>
            </div>

            <div class="answers-grid">
                <!-- Lecture 1 -->
                <div class="answer-card" data-lecture="1">
                    <div class="lecture-number">1</div>
                    <h3 class="lecture-title">مقدمة في الهندسة السريرية</h3>
                    <p class="lecture-description">
                        الإجابات النموذجية للأنشطة المتعلقة بتعريف الهندسة السريرية ودور المهندس السريري في النظام الصحي.
                    </p>

                    <div class="activities-section">
                        <h5 class="activities-title">📝 الأنشطة والإجابات النموذجية:</h5>

                        <div class="activity-item">
                            <div class="activity-title">النشاط 1: تعريف دور المهندس السريري</div>
                            <div class="activity-answer">
                                <strong>الإجابة النموذجية:</strong><br>
                                المهندس السريري هو متخصص يجمع بين المعرفة الهندسية والطبية لضمان الاستخدام الآمن والفعال للتكنولوجيا الطبية. يشمل دوره:
                                <br>• إدارة وصيانة الأجهزة الطبية
                                <br>• تقييم وشراء التكنولوجيا الجديدة
                                <br>• تدريب الطاقم الطبي على استخدام الأجهزة
                                <br>• ضمان الامتثال للمعايير والأنظمة
                                <br>• إدارة المخاطر والسلامة
                            </div>
                        </div>

                        <div class="activity-item">
                            <div class="activity-title">النشاط 2: تطور الهندسة السريرية</div>
                            <div class="activity-answer">
                                <strong>الإجابة النموذجية:</strong><br>
                                تطورت الهندسة السريرية من خلال عدة مراحل:
                                <br>• الستينات: بداية استخدام الأجهزة الطبية المعقدة
                                <br>• السبعينات: تأسيس أول برامج الهندسة السريرية
                                <br>• الثمانينات: تطوير المعايير والأنظمة
                                <br>• التسعينات: التركيز على إدارة التكنولوجيا
                                <br>• الألفية الجديدة: دمج تقنيات المعلومات والذكاء الاصطناعي
                            </div>
                        </div>
                    </div>

                    <div class="key-points">
                        <h5>🔑 النقاط الرئيسية:</h5>
                        <ul>
                            <li>الهندسة السريرية تخصص متعدد التخصصات</li>
                            <li>التركيز على السلامة والفعالية</li>
                            <li>أهمية التدريب المستمر</li>
                            <li>دور حيوي في تحسين جودة الرعاية الصحية</li>
                        </ul>
                    </div>

                    <div class="answer-actions">
                        <button class="btn btn-success" onclick="viewDetailedAnswer(1)">عرض التفاصيل | View Details</button>
                        <button class="btn btn-info" onclick="downloadAnswer(1)">تحميل PDF | Download PDF</button>
                        <button class="btn btn-warning" onclick="practiceActivity(1)">تمرين عملي | Practice</button>
                    </div>
                </div>

                <!-- Lecture 2 -->
                <div class="answer-card" data-lecture="2">
                    <div class="lecture-number">2</div>
                    <h3 class="lecture-title">إدارة التكنولوجيا الصحية</h3>
                    <p class="lecture-description">
                        الإجابات النموذجية للأنشطة المتعلقة بإدارة التكنولوجيا الصحية وتقييم التكنولوجيا الطبية.
                    </p>

                    <div class="activities-section">
                        <h5 class="activities-title">📝 الأنشطة والإجابات النموذجية:</h5>

                        <div class="activity-item">
                            <div class="activity-title">النشاط 1: إطار عمل HTM</div>
                            <div class="activity-answer">
                                <strong>الإجابة النموذجية:</strong><br>
                                إطار عمل إدارة التكنولوجيا الصحية (HTM) يتضمن:
                                <br>• التخطيط الاستراتيجي للتكنولوجيا
                                <br>• تقييم الاحتياجات والمتطلبات
                                <br>• عملية الشراء والتقييم
                                <br>• التركيب والتشغيل
                                <br>• الصيانة وإدارة دورة الحياة
                                <br>• التخلص الآمن والاستبدال
                            </div>
                        </div>

                        <div class="activity-item">
                            <div class="activity-title">النشاط 2: تقييم التكنولوجيا الصحية (HTA)</div>
                            <div class="activity-answer">
                                <strong>الإجابة النموذجية:</strong><br>
                                عملية HTA تشمل تقييم:
                                <br>• الفعالية السريرية والأمان
                                <br>• التكلفة والفعالية الاقتصادية
                                <br>• التأثير على جودة الحياة
                                <br>• الاعتبارات الأخلاقية والاجتماعية
                                <br>• التأثير على النظام الصحي
                            </div>
                        </div>
                    </div>

                    <div class="key-points">
                        <h5>🔑 النقاط الرئيسية:</h5>
                        <ul>
                            <li>HTM نهج شامل لإدارة التكنولوجيا</li>
                            <li>أهمية التقييم المستمر</li>
                            <li>التوازن بين التكلفة والفائدة</li>
                            <li>دور البيانات في اتخاذ القرارات</li>
                        </ul>
                    </div>

                    <div class="answer-actions">
                        <button class="btn btn-success" onclick="viewDetailedAnswer(2)">عرض التفاصيل | View Details</button>
                        <button class="btn btn-info" onclick="downloadAnswer(2)">تحميل PDF | Download PDF</button>
                        <button class="btn btn-warning" onclick="practiceActivity(2)">تمرين عملي | Practice</button>
                    </div>
                </div>

                <!-- Lecture 3 -->
                <div class="answer-card" data-lecture="3">
                    <div class="lecture-number">3</div>
                    <h3 class="lecture-title">المعايير التنظيمية والامتثال</h3>
                    <p class="lecture-description">
                        الإجابات النموذجية للأنشطة المتعلقة بالمعايير التنظيمية وأنظمة إدارة الجودة في الأجهزة الطبية.
                    </p>

                    <div class="activities-section">
                        <h5 class="activities-title">📝 الأنشطة والإجابات النموذجية:</h5>

                        <div class="activity-item">
                            <div class="activity-title">النشاط 1: معايير FDA وISO</div>
                            <div class="activity-answer">
                                <strong>الإجابة النموذجية:</strong><br>
                                المعايير الرئيسية تشمل:
                                <br>• FDA 21 CFR Part 820: نظام الجودة للأجهزة الطبية
                                <br>• ISO 13485: نظام إدارة الجودة للأجهزة الطبية
                                <br>• ISO 14971: إدارة المخاطر للأجهزة الطبية
                                <br>• IEC 62304: عمليات دورة حياة برمجيات الأجهزة الطبية
                                <br>• ISO 27001: أمن المعلومات
                            </div>
                        </div>

                        <div class="activity-item">
                            <div class="activity-title">النشاط 2: استراتيجية الامتثال</div>
                            <div class="activity-answer">
                                <strong>الإجابة النموذجية:</strong><br>
                                استراتيجية الامتثال الفعالة تتضمن:
                                <br>• تحديد المعايير المطبقة
                                <br>• تطوير السياسات والإجراءات
                                <br>• التدريب والتأهيل
                                <br>• المراقبة والتدقيق الداخلي
                                <br>• التحسين المستمر والتحديث
                            </div>
                        </div>
                    </div>

                    <div class="key-points">
                        <h5>🔑 النقاط الرئيسية:</h5>
                        <ul>
                            <li>الامتثال ضروري لضمان السلامة</li>
                            <li>المعايير تتطور باستمرار</li>
                            <li>أهمية التوثيق والتسجيل</li>
                            <li>التدريب المستمر للفريق</li>
                        </ul>
                    </div>

                    <div class="answer-actions">
                        <button class="btn btn-success" onclick="viewDetailedAnswer(3)">عرض التفاصيل | View Details</button>
                        <button class="btn btn-info" onclick="downloadAnswer(3)">تحميل PDF | Download PDF</button>
                        <button class="btn btn-warning" onclick="practiceActivity(3)">تمرين عملي | Practice</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div style="text-align: center; margin: 40px 0; padding: 30px; background: white; border-radius: 15px; box-shadow: 0 5px 15px rgba(0,0,0,0.1);">
            <h3 style="color: #667eea; margin-bottom: 25px;">🎯 إجراءات سريعة | Quick Actions</h3>
            <div style="display: flex; gap: 15px; justify-content: center; flex-wrap: wrap;">
                <button class="btn btn-success" onclick="exportAllAnswers()">تصدير جميع الإجابات | Export All Answers</button>
                <button class="btn btn-info" onclick="printAnswers()">طباعة الإجابات | Print Answers</button>
                <button class="btn btn-warning" onclick="resetProgress()">إعادة تعيين | Reset Progress</button>
                <a href="english_model_answers.html" class="btn" style="background: linear-gradient(45deg, #ff6b6b, #ee5a24);">النسخة الإنجليزية | English Version</a>
            </div>
        </div>
    </div>

    <!-- Home Button -->
    <button class="home-btn" onclick="window.location.href='index.html'">
        🏠 الرئيسية<br>Home
    </button>

    <!-- Language Switch Button -->
    <button class="language-switch" onclick="window.location.href='english_model_answers.html'">
        🌐 English<br>إنجليزي
    </button>

    <script>
        // Model answers management system
        let currentFilter = 'all';
        let searchTerm = '';

        // Initialize page
        function initializePage() {
            // Add fade-in animation to cards
            const cards = document.querySelectorAll('.answer-card');
            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.classList.add('fade-in');
                }, index * 100);
            });

            // Load user progress if available
            loadUserProgress();
        }

        // Search functionality
        function searchAnswers() {
            searchTerm = document.getElementById('searchInput').value.toLowerCase();
            filterAnswers();
        }

        // Filter by category
        function filterByCategory(category) {
            currentFilter = category;

            // Update filter buttons
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');

            filterAnswers();
        }

        // Apply filters
        function filterAnswers() {
            const categories = document.querySelectorAll('.lecture-category');
            const cards = document.querySelectorAll('.answer-card');

            categories.forEach(category => {
                const categoryType = category.dataset.category;
                let hasVisibleCards = false;

                const categoryCards = category.querySelectorAll('.answer-card');
                categoryCards.forEach(card => {
                    const title = card.querySelector('.lecture-title').textContent.toLowerCase();
                    const description = card.querySelector('.lecture-description').textContent.toLowerCase();
                    const activities = card.querySelector('.activities-section').textContent.toLowerCase();

                    const matchesSearch = searchTerm === '' ||
                                        title.includes(searchTerm) ||
                                        description.includes(searchTerm) ||
                                        activities.includes(searchTerm);

                    const matchesFilter = currentFilter === 'all' || categoryType === currentFilter;

                    if (matchesSearch && matchesFilter) {
                        card.style.display = 'block';
                        hasVisibleCards = true;
                    } else {
                        card.style.display = 'none';
                    }
                });

                // Show/hide category based on visible cards
                if (currentFilter === 'all' || categoryType === currentFilter) {
                    category.style.display = hasVisibleCards ? 'block' : 'none';
                } else {
                    category.style.display = 'none';
                }
            });
        }

        // View detailed answer
        function viewDetailedAnswer(lectureNum) {
            const detailedAnswers = {
                1: `
تفاصيل الإجابات النموذجية - المحاضرة 1: مقدمة في الهندسة السريرية

النشاط 1: تعريف دور المهندس السريري
الإجابة المفصلة:
المهندس السريري هو محترف متخصص يجمع بين الخبرة الهندسية والمعرفة الطبية لضمان الاستخدام الآمن والفعال للتكنولوجيا الطبية في البيئة السريرية.

المسؤوليات الأساسية:
1. إدارة التكنولوجيا الطبية:
   - تخطيط وتنفيذ برامج إدارة المعدات
   - تطوير السياسات والإجراءات
   - إدارة قواعد البيانات والمخزون

2. الصيانة والمعايرة:
   - تطوير برامج الصيانة الوقائية
   - إجراء المعايرة والاختبارات
   - إدارة عقود الصيانة الخارجية

3. السلامة وإدارة المخاطر:
   - تطبيق معايير السلامة
   - إجراء تقييمات المخاطر
   - التحقيق في الحوادث والأعطال

4. التدريب والتعليم:
   - تدريب الطاقم الطبي
   - تطوير المواد التدريبية
   - إجراء ورش العمل

5. الامتثال التنظيمي:
   - ضمان الامتثال للمعايير
   - إعداد التقارير التنظيمية
   - التعامل مع عمليات التفتيش

النشاط 2: تطور الهندسة السريرية
الإجابة المفصلة:

المرحلة الأولى (1960-1970):
- ظهور الأجهزة الطبية المعقدة
- الحاجة لخبرة تقنية متخصصة
- بداية توظيف المهندسين في المستشفيات

المرحلة الثانية (1970-1980):
- تأسيس أول برامج الهندسة السريرية
- تطوير المناهج الأكاديمية
- إنشاء الجمعيات المهنية

المرحلة الثالثة (1980-1990):
- تطوير المعايير والأنظمة
- التركيز على السلامة
- نمو الاعتراف المهني

المرحلة الرابعة (1990-2000):
- التركيز على إدارة التكنولوجيا
- تطوير أنظمة المعلومات
- التوسع العالمي

المرحلة الخامسة (2000-الآن):
- دمج تقنيات المعلومات
- الذكاء الاصطناعي والتعلم الآلي
- الطب الرقمي والتطبيب عن بُعد
                `,
                2: `
تفاصيل الإجابات النموذجية - المحاضرة 2: إدارة التكنولوجيا الصحية

النشاط 1: إطار عمل HTM
الإجابة المفصلة:

إطار عمل إدارة التكنولوجيا الصحية (HTM) هو نهج شامل ومنهجي لإدارة التكنولوجيا الطبية طوال دورة حياتها.

المراحل الأساسية:

1. التخطيط الاستراتيجي:
   - تحديد الأهداف الاستراتيجية
   - تقييم الوضع الحالي
   - وضع خطط طويلة المدى
   - تخصيص الموارد

2. تقييم الاحتياجات:
   - تحليل الاحتياجات السريرية
   - دراسة الجدوى التقنية
   - تقييم التأثير المالي
   - مراجعة البدائل المتاحة

3. عملية الشراء:
   - إعداد المواصفات التقنية
   - تقييم العروض
   - التفاوض مع الموردين
   - إدارة العقود

4. التركيب والتشغيل:
   - التخطيط للتركيب
   - اختبار القبول
   - التدريب والتأهيل
   - بدء التشغيل

5. الصيانة وإدارة دورة الحياة:
   - برامج الصيانة الوقائية
   - إدارة قطع الغيار
   - مراقبة الأداء
   - التحديثات والترقيات

6. التخلص والاستبدال:
   - تقييم نهاية العمر الافتراضي
   - التخطيط للاستبدال
   - التخلص الآمن
   - استرداد القيمة

النشاط 2: تقييم التكنولوجيا الصحية (HTA)
الإجابة المفصلة:

تقييم التكنولوجيا الصحية هو عملية منهجية متعددة التخصصات لتقييم خصائص وتأثيرات التكنولوجيا الصحية.

المجالات الأساسية للتقييم:

1. الفعالية السريرية:
   - تقييم النتائج السريرية
   - مقارنة مع البدائل الموجودة
   - تحليل البيانات السريرية
   - تقييم الأدلة العلمية

2. السلامة:
   - تحديد المخاطر المحتملة
   - تقييم الآثار الجانبية
   - مراجعة تقارير السلامة
   - تحليل الحوادث

3. التكلفة والفعالية الاقتصادية:
   - تحليل التكلفة-الفعالية
   - تحليل التكلفة-المنفعة
   - تحليل التأثير على الميزانية
   - تقييم العائد على الاستثمار

4. التأثير الاجتماعي والأخلاقي:
   - تقييم القبول الاجتماعي
   - الاعتبارات الأخلاقية
   - تأثير على العدالة الصحية
   - القضايا القانونية

5. التأثير التنظيمي:
   - تأثير على سير العمل
   - متطلبات التدريب
   - تغييرات في الهيكل التنظيمي
   - تأثير على الموارد البشرية
                `,
                3: `
تفاصيل الإجابات النموذجية - المحاضرة 3: المعايير التنظيمية والامتثال

النشاط 1: معايير FDA وISO
الإجابة المفصلة:

المعايير التنظيمية الرئيسية في صناعة الأجهزة الطبية:

1. FDA 21 CFR Part 820 (نظام الجودة):
   - متطلبات نظام إدارة الجودة
   - ضوابط التصميم والتطوير
   - إدارة المستندات والسجلات
   - ضوابط الإنتاج والعمليات
   - التصحيحات والإجراءات الوقائية

2. ISO 13485 (نظام إدارة الجودة للأجهزة الطبية):
   - متطلبات شاملة لنظام الجودة
   - التركيز على متطلبات العملاء
   - إدارة المخاطر المتكاملة
   - التحسين المستمر
   - المراجعة الإدارية

3. ISO 14971 (إدارة المخاطر):
   - عملية إدارة المخاطر
   - تحليل المخاطر وتقييمها
   - ضوابط المخاطر
   - معلومات الإنتاج وما بعد الإنتاج
   - ملف إدارة المخاطر

4. IEC 62304 (برمجيات الأجهزة الطبية):
   - عمليات دورة حياة البرمجيات
   - تصنيف البرمجيات حسب السلامة
   - متطلبات التطوير والاختبار
   - إدارة التكوين
   - حل المشاكل

5. ISO 27001 (أمن المعلومات):
   - نظام إدارة أمن المعلومات
   - تقييم المخاطر الأمنية
   - ضوابط الأمان
   - المراقبة والمراجعة
   - التحسين المستمر

النشاط 2: استراتيجية الامتثال
الإجابة المفصلة:

تطوير استراتيجية امتثال فعالة:

1. تحديد المعايير المطبقة:
   - تحليل المتطلبات التنظيمية
   - تحديد الأسواق المستهدفة
   - مراجعة التحديثات التنظيمية
   - تقييم التأثير على المنتجات

2. تطوير السياسات والإجراءات:
   - إعداد دليل الجودة
   - تطوير الإجراءات التشغيلية
   - إنشاء النماذج والقوالب
   - تحديد المسؤوليات والصلاحيات

3. التدريب والتأهيل:
   - برامج التدريب الأساسي
   - التدريب المتخصص
   - التقييم والشهادات
   - التدريب المستمر

4. التنفيذ والمراقبة:
   - تطبيق الإجراءات
   - مراقبة الأداء
   - جمع البيانات والمؤشرات
   - التقارير الدورية

5. التدقيق والمراجعة:
   - التدقيق الداخلي
   - المراجعة الإدارية
   - التدقيق الخارجي
   - إجراءات التصحيح

6. التحسين المستمر:
   - تحليل البيانات
   - تحديد فرص التحسين
   - تنفيذ التحسينات
   - متابعة الفعالية
                `
            };

            const answer = detailedAnswers[lectureNum] || 'التفاصيل غير متوفرة حالياً';

            // Create modal or new window to display detailed answer
            const newWindow = window.open('', '_blank', 'width=800,height=600,scrollbars=yes');
            newWindow.document.write(`
                <html dir="rtl">
                <head>
                    <title>الإجابة المفصلة - المحاضرة ${lectureNum}</title>
                    <style>
                        body { font-family: Arial, sans-serif; padding: 20px; line-height: 1.6; }
                        h1 { color: #667eea; }
                        pre { white-space: pre-wrap; font-family: Arial, sans-serif; }
                    </style>
                </head>
                <body>
                    <h1>الإجابة المفصلة - المحاضرة ${lectureNum}</h1>
                    <pre>${answer}</pre>
                    <button onclick="window.print()" style="margin: 20px 0; padding: 10px 20px; background: #667eea; color: white; border: none; border-radius: 5px;">طباعة</button>
                    <button onclick="window.close()" style="margin: 20px 0; padding: 10px 20px; background: #6c757d; color: white; border: none; border-radius: 5px;">إغلاق</button>
                </body>
                </html>
            `);
        }

        // Download answer as PDF
        function downloadAnswer(lectureNum) {
            // Simulate PDF download
            showNotification(`جاري تحميل إجابات المحاضرة ${lectureNum} | Downloading lecture ${lectureNum} answers`, 'info');

            // In a real implementation, this would generate and download a PDF
            setTimeout(() => {
                showNotification(`تم تحميل الملف بنجاح | File downloaded successfully`, 'success');
            }, 2000);
        }

        // Practice activity
        function practiceActivity(lectureNum) {
            // Redirect to practical exercises or simulation
            window.open(`arabic_practical_exercises.html#lecture-${lectureNum}`, '_blank');
        }

        // Export all answers
        function exportAllAnswers() {
            showNotification('جاري تصدير جميع الإجابات... | Exporting all answers...', 'info');

            // Simulate export process
            setTimeout(() => {
                const content = `
تصدير جميع الإجابات النموذجية - الهندسة السريرية
Export of All Model Answers - Clinical Engineering

التاريخ | Date: ${new Date().toLocaleDateString()}

هذا الملف يحتوي على جميع الإجابات النموذجية للمحاضرات الـ 12 في الهندسة السريرية.
This file contains all model answers for the 12 lectures in Clinical Engineering.

المحاضرات المتضمنة | Included Lectures:
1. مقدمة في الهندسة السريرية | Introduction to Clinical Engineering
2. إدارة التكنولوجيا الصحية | Healthcare Technology Management
3. المعايير التنظيمية والامتثال | Regulatory Standards and Compliance
... والمزيد | and more

للحصول على التفاصيل الكاملة، يرجى زيارة الموقع الإلكتروني.
For complete details, please visit the website.
                `;

                const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `model-answers-arabic-${new Date().toISOString().split('T')[0]}.txt`;
                a.click();
                URL.revokeObjectURL(url);

                showNotification('تم تصدير الملف بنجاح | File exported successfully', 'success');
            }, 2000);
        }

        // Print answers
        function printAnswers() {
            window.print();
        }

        // Reset progress
        function resetProgress() {
            if (confirm('هل أنت متأكد من إعادة تعيين التقدم؟ | Are you sure you want to reset progress?')) {
                localStorage.removeItem('modelAnswersProgress');
                showNotification('تم إعادة تعيين التقدم | Progress reset successfully', 'warning');
            }
        }

        // Load user progress
        function loadUserProgress() {
            const progress = JSON.parse(localStorage.getItem('modelAnswersProgress')) || {};
            // Apply any saved progress indicators
        }

        // Show notification
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            const colors = {
                success: '#28a745',
                warning: '#ffc107',
                info: '#17a2b8',
                error: '#dc3545'
            };

            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${colors[type] || colors.info};
                color: white;
                padding: 15px 20px;
                border-radius: 10px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                z-index: 10000;
                font-weight: bold;
                max-width: 400px;
                animation: slideIn 0.5s ease;
            `;
            notification.textContent = message;

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.animation = 'slideOut 0.5s ease';
                setTimeout(() => {
                    if (document.body.contains(notification)) {
                        document.body.removeChild(notification);
                    }
                }, 500);
            }, 3000);
        }

        // Add CSS animations
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);

        // Initialize page on load
        document.addEventListener('DOMContentLoaded', initializePage);
    </script>
</body>
</html>
