<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clinical Engineering Device Simulation</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f4f4f4; color: #333; }
        h1 { color: #2c3e50; }
        #simulation-area {
            border: 1px solid #a0a0a0;
            padding: 20px;
            min-height: 350px;
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        .vital-sign {
            font-size: 1.2em;
            margin-bottom: 5px;
            padding: 10px;
            background-color: #e9ecef;
            border-left: 5px solid #007bff;
            border-radius: 4px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .vital-sign strong {
            color: #0056b3;
        }
        .vital-value {
            font-weight: bold;
            color: #28a745; /* Green for normal values */
        }
        .control-panel { margin-top: 20px; display: flex; gap: 10px; align-items: center; }
        button {
            padding: 12px 20px;
            margin-right: 10px;
            cursor: pointer;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 1em;
            transition: background-color 0.3s ease;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:active {
            background-color: #004085;
        }
        #device-status {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
            border-radius: 5px;
            font-weight: bold;
            text-align: center;
        }
        .status-running {
            background-color: #d4edda;
            color: #155724;
            border-color: #c3e6cb;
        }
        .status-stopped {
            background-color: #f8d7da;
            color: #721c24;
            border-color: #f5c6cb;
        }
        .device-selection {
            margin-left: 20px;
            padding: 8px;
            border-radius: 5px;
            border: 1px solid #ccc;
        }
        .scenario-selection {
            margin-top: 10px;
            padding: 8px;
            border-radius: 5px;
            border: 1px solid #ccc;
        }
        .scenario-container,
        .settings-container {
            margin-bottom: 15px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f0f0f0;
        }
        .settings-container label {
            margin-right: 10px;
        }
        .settings-container input[type="number"],
        .settings-container select {
            padding: 5px;
            border-radius: 3px;
            border: 1px solid #ccc;
        }
    </style>
</head>
<body>
    <h1>Clinical Engineering Device Simulation</h1>

    <div class="control-panel">
        <button onclick="startSimulation()">Start Device</button>
        <button onclick="stopSimulation()">Stop Device</button>
        <button onclick="resetSimulation()">Reset</button>
        <select id="deviceSelector" class="device-selection" onchange="switchDevice()">
            <option value="patientMonitor">Patient Monitor</option>
            <option value="ecgMonitor">ECG Monitor</option>
            <option value="respiratoryMonitor">Respiratory Monitor</option>
            <option value="hemodynamicMonitor">Hemodynamic Monitor</option>
            <option value="neurologicalMonitor">Neurological Monitor</option>
        </select>
    </div>

    <div id="simulation-area">
        <p>Device simulation ready. Select a device and click "Start Device" to begin monitoring.</p>
        <div id="patientMonitorDisplay" class="device-display active">
            <div class="scenario-container">
                <strong>Patient Scenario:</strong>
                <select id="patientScenario" class="scenario-selection" onchange="updateVitalSigns()">
                    <option value="normal">Normal</option>
                    <option value="tachycardia">Tachycardia</option>
                    <option value="hypotension">Hypotension</option>
                    <option value="hypoxemia">Hypoxemia</option>
                </select>
            </div>
            <div class="vital-sign">
                <strong>Heart Rate:</strong> <span id="heartRate" class="vital-value">-- bpm</span>
            </div>
            <div class="vital-sign">
                <strong>Blood Pressure:</strong> <span id="bloodPressure" class="vital-value">--/-- mmHg</span>
            </div>
            <div class="vital-sign">
                <strong>Oxygen Saturation:</strong> <span id="oxygenSaturation" class="vital-value">-- %</span>
            </div>
        </div>

        <div id="ecgMonitorDisplay" class="device-display">
            <div class="vital-sign">
                <strong>Heart Rhythm:</strong> <span id="heartRhythm" class="vital-value">--</span>
            </div>
            <div class="vital-sign">
                <strong>QRS Duration:</strong> <span id="qrsDuration" class="vital-value">-- ms</span>
            </div>
        </div>

        <div id="respiratoryMonitorDisplay" class="device-display">
            <div class="settings-container">
                <strong>Ventilator Settings:</strong><br>
                <label for="ventilationMode">Mode:</label>
                <select id="ventilationMode" onchange="updateVitalSigns()">
                    <option value="AC">Assist-Control (AC)</option>
                    <option value="SIMV">Synchronized Intermittent Mandatory Ventilation (SIMV)</option>
                    <option value="CPAP">Continuous Positive Airway Pressure (CPAP)</option>
                </select>
                <label for="fio2">FiO2 (%):</label>
                <input type="number" id="fio2" value="21" min="21" max="100" step="1" onchange="updateVitalSigns()">
            </div>
            <div class="vital-sign">
                <strong>Tidal Volume:</strong> <span id="tidalVolume" class="vital-value">-- mL</span>
            </div>
            <div class="vital-sign">
                <strong>Respiratory Rate:</strong> <span id="respiratoryRate" class="vital-value">-- bpm</span>
            </div>
            <div class="vital-sign">
                <strong>Oxygen Saturation:</strong> <span id="respOxygenSaturation" class="vital-value">-- %</span>
            </div>
            <div class="vital-sign">
                <strong>End-tidal CO2:</strong> <span id="etCO2" class="vital-value">-- mmHg</span>
            </div>
        </div>

        <div id="hemodynamicMonitorDisplay" class="device-display">
            <div class="vital-sign">
                <strong>CVP:</strong> <span id="cvp" class="vital-value">-- mmHg</span>
            </div>
            <div class="vital-sign">
                <strong>PAP:</strong> <span id="pap" class="vital-value">--/-- mmHg</span>
            </div>
            <div class="vital-sign">
                <strong>Cardiac Output:</strong> <span id="cardiacOutput" class="vital-value">-- L/min</span>
            </div>
            <div class="vital-sign">
                <strong>SVR:</strong> <span id="svr" class="vital-value">-- dynes·s/cm⁵</span>
            </div>
        </div>

        <div id="neurologicalMonitorDisplay" class="device-display">
            <div class="vital-sign">
                <strong>ICP:</strong> <span id="icp" class="vital-value">-- mmHg</span>
            </div>
            <div class="vital-sign">
                <strong>CPP:</strong> <span id="cpp" class="vital-value">-- mmHg</span>
            </div>
            <div class="vital-sign">
                <strong>GCS:</strong> <span id="gcs" class="vital-value">--</span>
            </div>
        </div>

        <div id="device-status" class="status-stopped">Device Status: Stopped</div>
    </div>

    <script>
        let simulationInterval;
        let isRunning = false;
        let activeDevice = 'patientMonitor'; // Default active device
        let patientScenario = 'normal'; // Default patient scenario

        function updateVitalSigns() {
            if (!isRunning) return;

            if (activeDevice === 'patientMonitor') {
                const scenarioSelector = document.getElementById('patientScenario');
                patientScenario = scenarioSelector ? scenarioSelector.value : 'normal';

                let heartRate, systolicBP, diastolicBP, oxygenSat;

                switch (patientScenario) {
                    case 'normal':
                        heartRate = Math.floor(Math.random() * (90 - 60 + 1)) + 60; // 60-90 bpm
                        systolicBP = Math.floor(Math.random() * (130 - 110 + 1)) + 110; // 110-130 mmHg
                        diastolicBP = Math.floor(Math.random() * (85 - 70 + 1)) + 70; // 70-85 mmHg
                        oxygenSat = Math.floor(Math.random() * (100 - 95 + 1)) + 95; // 95-100 %
                        break;
                    case 'tachycardia':
                        heartRate = Math.floor(Math.random() * (120 - 100 + 1)) + 100; // 100-120 bpm
                        systolicBP = Math.floor(Math.random() * (120 - 100 + 1)) + 100; // Slightly elevated or normal
                        diastolicBP = Math.floor(Math.random() * (80 - 60 + 1)) + 60;
                        oxygenSat = Math.floor(Math.random() * (98 - 95 + 1)) + 95;
                        break;
                    case 'hypotension':
                        heartRate = Math.floor(Math.random() * (100 - 80 + 1)) + 80; // Compensatory tachycardia
                        systolicBP = Math.floor(Math.random() * (90 - 70 + 1)) + 70; // 70-90 mmHg
                        diastolicBP = Math.floor(Math.random() * (60 - 40 + 1)) + 40; // 40-60 mmHg
                        oxygenSat = Math.floor(Math.random() * (95 - 92 + 1)) + 92; // Can be slightly lower
                        break;
                    case 'hypoxemia':
                        heartRate = Math.floor(Math.random() * (110 - 90 + 1)) + 90; // Compensatory
                        systolicBP = Math.floor(Math.random() * (130 - 110 + 1)) + 110;
                        diastolicBP = Math.floor(Math.random() * (85 - 70 + 1)) + 70;
                        oxygenSat = Math.floor(Math.random() * (90 - 85 + 1)) + 85; // Significantly lower
                        break;
                    default:
                        // Fallback to normal if scenario is unknown
                        heartRate = Math.floor(Math.random() * (90 - 60 + 1)) + 60;
                        systolicBP = Math.floor(Math.random() * (130 - 110 + 1)) + 110;
                        diastolicBP = Math.floor(Math.random() * (85 - 70 + 1)) + 70;
                        oxygenSat = Math.floor(Math.random() * (100 - 95 + 1)) + 95;
                }

                document.getElementById('heartRate').textContent = `${heartRate} bpm`;
                document.getElementById('bloodPressure').textContent = `${systolicBP}/${diastolicBP} mmHg`;
                document.getElementById('oxygenSaturation').textContent = `${oxygenSat} %`;

                // Update status based on values (simple example)
                const heartRateElement = document.getElementById('heartRate');
                heartRateElement.style.color = (heartRate > 95 || heartRate < 55) ? '#dc3545' : '#28a745';
                const bpElement = document.getElementById('bloodPressure');
                bpElement.style.color = (systolicBP < 90 || diastolicBP < 50) ? '#dc3545' : '#28a745';
                const spo2Element = document.getElementById('oxygenSaturation');
                spo2Element.style.color = (oxygenSat < 92) ? '#dc3545' : '#28a745';

            } else if (activeDevice === 'ecgMonitor') {
                // Simulate ECG parameters
                const rhythms = ["Normal Sinus Rhythm", "Sinus Tachycardia", "Sinus Bradycardia"];
                const heartRhythm = rhythms[Math.floor(Math.random() * rhythms.length)];
                const qrsDuration = Math.floor(Math.random() * (120 - 80 + 1)) + 80; // 80-120 ms

                document.getElementById('heartRhythm').textContent = heartRhythm;
                document.getElementById('qrsDuration').textContent = `${qrsDuration} ms`;

                const heartRhythmElement = document.getElementById('heartRhythm');
                if (heartRhythm !== "Normal Sinus Rhythm") {
                    heartRhythmElement.style.color = '#dc3545';
                } else {
                    heartRhythmElement.style.color = '#28a745';
                }
            } else if (activeDevice === 'respiratoryMonitor') {
                // Get ventilator settings
                const ventilationMode = document.getElementById('ventilationMode').value;
                const fio2 = parseInt(document.getElementById('fio2').value);

                let tidalVolume, respiratoryRate, respOxygenSaturation, etCO2;

                // Simulate Respiratory parameters based on settings
                // Basic logic: higher FiO2 -> higher SpO2
                // Different modes could influence RR/Vt ranges

                if (ventilationMode === 'AC') {
                    tidalVolume = Math.floor(Math.random() * (550 - 450 + 1)) + 450; // Tightly controlled
                    respiratoryRate = Math.floor(Math.random() * (18 - 14 + 1)) + 14; // Controlled rate
                } else if (ventilationMode === 'SIMV') {
                    tidalVolume = Math.floor(Math.random() * (650 - 350 + 1)) + 350; // More variability
                    respiratoryRate = Math.floor(Math.random() * (22 - 10 + 1)) + 10; // Patient can breathe spontaneously
                } else if (ventilationMode === 'CPAP') {
                    tidalVolume = Math.floor(Math.random() * (400 - 200 + 1)) + 200; // Patient's own effort
                    respiratoryRate = Math.floor(Math.random() * (25 - 12 + 1)) + 12; // Patient's own effort
                }

                // FiO2 influence on SpO2
                if (fio2 >= 80) {
                    respOxygenSaturation = Math.floor(Math.random() * (100 - 98 + 1)) + 98;
                } else if (fio2 >= 50) {
                    respOxygenSaturation = Math.floor(Math.random() * (99 - 95 + 1)) + 95;
                } else if (fio2 >= 30) {
                    respOxygenSaturation = Math.floor(Math.random() * (97 - 93 + 1)) + 93;
                } else {
                    respOxygenSaturation = Math.floor(Math.random() * (95 - 90 + 1)) + 90;
                }

                etCO2 = Math.floor(Math.random() * (45 - 35 + 1)) + 35; // 35-45 mmHg

                document.getElementById('tidalVolume').textContent = `${tidalVolume} mL`;
                document.getElementById('respiratoryRate').textContent = `${respiratoryRate} bpm`;
                document.getElementById('respOxygenSaturation').textContent = `${respOxygenSaturation} %`;
                document.getElementById('etCO2').textContent = `${etCO2} mmHg`;

                // Basic anomaly highlighting for respiratory rate and SpO2
                const rrElement = document.getElementById('respiratoryRate');
                rrElement.style.color = (respiratoryRate > 22 || respiratoryRate < 10) ? '#dc3545' : '#28a745';
                const respSpo2Element = document.getElementById('respOxygenSaturation');
                respSpo2Element.style.color = (respOxygenSaturation < 92) ? '#dc3545' : '#28a745';

            } else if (activeDevice === 'hemodynamicMonitor') {
                // Simulate Hemodynamic parameters
                const cvp = (Math.random() * (12 - 2) + 2).toFixed(1); // 2-12 mmHg
                const papSystolic = Math.floor(Math.random() * (30 - 18 + 1)) + 18; // 18-30 mmHg
                const papDiastolic = Math.floor(Math.random() * (15 - 5 + 1)) + 5; // 5-15 mmHg
                const cardiacOutput = (Math.random() * (6 - 4) + 4).toFixed(1); // 4-6 L/min
                const svr = Math.floor(Math.random() * (1200 - 800 + 1)) + 800; // 800-1200 dynes·s/cm⁵

                document.getElementById('cvp').textContent = `${cvp} mmHg`;
                document.getElementById('pap').textContent = `${papSystolic}/${papDiastolic} mmHg`;
                document.getElementById('cardiacOutput').textContent = `${cardiacOutput} L/min`;
                document.getElementById('svr').textContent = `${svr} dynes·s/cm⁵`;

                // Basic anomaly highlighting for CVP
                const cvpElement = document.getElementById('cvp');
                if (cvp > 8 || cvp < 4) {
                    cvpElement.style.color = '#dc3545';
                } else {
                    cvpElement.style.color = '#28a745';
                }
            } else if (activeDevice === 'neurologicalMonitor') {
                // Simulate Neurological parameters
                const icp = (Math.random() * (15 - 5) + 5).toFixed(1); // 5-15 mmHg
                const map = Math.floor(Math.random() * (100 - 70 + 1)) + 70; // 70-100 mmHg (for CPP calculation)
                const cpp = (map - icp).toFixed(1); // CPP = MAP - ICP
                const gcs = Math.floor(Math.random() * (15 - 3 + 1)) + 3; // 3-15

                document.getElementById('icp').textContent = `${icp} mmHg`;
                document.getElementById('cpp').textContent = `${cpp} mmHg`;
                document.getElementById('gcs').textContent = `${gcs}`;

                // Basic anomaly highlighting for ICP
                const icpElement = document.getElementById('icp');
                if (icp > 10) {
                    icpElement.style.color = '#dc3545';
                } else {
                    icpElement.style.color = '#28a745';
                }
            }
        }

        function switchDevice() {
            stopSimulation(); // Stop current simulation
            const selector = document.getElementById('deviceSelector');
            activeDevice = selector.value;

            // Hide all device displays
            document.getElementById('patientMonitorDisplay').classList.remove('active');
            document.getElementById('ecgMonitorDisplay').classList.remove('active');
            document.getElementById('respiratoryMonitorDisplay').classList.remove('active');
            document.getElementById('hemodynamicMonitorDisplay').classList.remove('active');
            document.getElementById('neurologicalMonitorDisplay').classList.remove('active');

            // Show the selected device display
            const selectedDisplay = document.getElementById(`${activeDevice}Display`);
            selectedDisplay.classList.add('active');

            document.getElementById('simulation-area').innerHTML = `<p>Device simulation ready. Select a device and click "Start Device" to begin monitoring.</p>`;
            document.getElementById('device-status').className = 'status-stopped';
            document.getElementById('device-status').textContent = 'Device Status: Stopped';

            // Re-append the selected display and status to the simulation area
            document.getElementById('simulation-area').appendChild(selectedDisplay);
            document.getElementById('simulation-area').appendChild(document.getElementById('device-status'));

            // Reset vital signs display for the new device
            if (activeDevice === 'patientMonitor') {
                document.getElementById('heartRate').textContent = '-- bpm';
                document.getElementById('bloodPressure').textContent = '--/-- mmHg';
                document.getElementById('oxygenSaturation').textContent = '-- %';
                // Reset scenario selector to normal when switching to patient monitor
                const scenarioSelector = document.getElementById('patientScenario');
                if (scenarioSelector) scenarioSelector.value = 'normal';
                patientScenario = 'normal';
            } else if (activeDevice === 'ecgMonitor') {
                document.getElementById('heartRhythm').textContent = '--';
                document.getElementById('qrsDuration').textContent = '-- ms';
            } else if (activeDevice === 'respiratoryMonitor') {
                document.getElementById('tidalVolume').textContent = '-- mL';
                document.getElementById('respiratoryRate').textContent = '-- bpm';
                document.getElementById('respOxygenSaturation').textContent = '-- %';
                document.getElementById('etCO2').textContent = '-- mmHg';
                // Reset ventilator settings to default
                document.getElementById('ventilationMode').value = 'AC';
                document.getElementById('fio2').value = '21';
            } else if (activeDevice === 'hemodynamicMonitor') {
                document.getElementById('cvp').textContent = '-- mmHg';
                document.getElementById('pap').textContent = '--/-- mmHg';
                document.getElementById('cardiacOutput').textContent = '-- L/min';
                document.getElementById('svr').textContent = '-- dynes·s/cm⁵';
            } else if (activeDevice === 'neurologicalMonitor') {
                document.getElementById('icp').textContent = '-- mmHg';
                document.getElementById('cpp').textContent = '-- mmHg';
                document.getElementById('gcs').textContent = '--';
            }
        }

        function startSimulation() {
            if (isRunning) return;
            isRunning = true;
            document.getElementById('device-status').className = 'status-running';
            document.getElementById('device-status').textContent = 'Device Status: Running';

            // Clear initial message and show relevant device display
            document.getElementById('simulation-area').innerHTML = '';
            const selectedDisplay = document.getElementById(`${activeDevice}Display`);
            document.getElementById('simulation-area').appendChild(selectedDisplay);
            selectedDisplay.classList.add('active');
            document.getElementById('simulation-area').appendChild(document.getElementById('device-status'));

            updateVitalSigns(); // Initial update
            simulationInterval = setInterval(updateVitalSigns, 2000); // Update every 2 seconds
            console.log('Simulation started for ' + activeDevice);
        }

        function stopSimulation() {
            if (!isRunning) return;
            isRunning = false;
            clearInterval(simulationInterval);
            document.getElementById('device-status').className = 'status-stopped';
            document.getElementById('device-status').textContent = 'Device Status: Stopped';
            console.log('Simulation stopped for ' + activeDevice);
        }

        function resetSimulation() {
            stopSimulation(); // Stop if running
            document.getElementById('simulation-area').innerHTML = `<p>Device simulation ready. Select a device and click "Start Device" to begin monitoring.</p>`;
            document.getElementById('device-status').className = 'status-stopped';
            document.getElementById('device-status').textContent = 'Device Status: Stopped';

            // Re-append the selected display and status to the simulation area
            const selectedDisplay = document.getElementById(`${activeDevice}Display`);
            document.getElementById('simulation-area').appendChild(selectedDisplay);
            selectedDisplay.classList.add('active'); // Ensure it's active after reset
            document.getElementById('simulation-area').appendChild(document.getElementById('device-status'));

            // Reset vital signs display for the current device
            if (activeDevice === 'patientMonitor') {
                document.getElementById('heartRate').textContent = '-- bpm';
                document.getElementById('bloodPressure').textContent = '--/-- mmHg';
                document.getElementById('oxygenSaturation').textContent = '-- %';
                const scenarioSelector = document.getElementById('patientScenario');
                if (scenarioSelector) scenarioSelector.value = 'normal';
                patientScenario = 'normal';
            } else if (activeDevice === 'ecgMonitor') {
                document.getElementById('heartRhythm').textContent = '--';
                document.getElementById('qrsDuration').textContent = '-- ms';
            } else if (activeDevice === 'respiratoryMonitor') {
                document.getElementById('tidalVolume').textContent = '-- mL';
                document.getElementById('respiratoryRate').textContent = '-- bpm';
                document.getElementById('respOxygenSaturation').textContent = '-- %';
                document.getElementById('etCO2').textContent = '-- mmHg';
                document.getElementById('ventilationMode').value = 'AC';
                document.getElementById('fio2').value = '21';
            } else if (activeDevice === 'hemodynamicMonitor') {
                document.getElementById('cvp').textContent = '-- mmHg';
                document.getElementById('pap').textContent = '--/-- mmHg';
                document.getElementById('cardiacOutput').textContent = '-- L/min';
                document.getElementById('svr').textContent = '-- dynes·s/cm⁵';
            } else if (activeDevice === 'neurologicalMonitor') {
                document.getElementById('icp').textContent = '-- mmHg';
                document.getElementById('cpp').textContent = '-- mmHg';
                document.getElementById('gcs').textContent = '--';
            }
            console.log('Simulation reset.');
        }

        // Initial display setup
        document.addEventListener('DOMContentLoaded', () => {
            switchDevice(); // Ensure the correct device is shown on load
        });
    </script>
</body>
</html>