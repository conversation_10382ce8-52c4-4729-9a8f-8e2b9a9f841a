<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clinical Engineering Interactive Simulations</title>
    <style>
        /* Base Styles */
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
            background-color: #f5f7fa;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        /* Header Styles */
        header {
            background: linear-gradient(135deg, #0056b3 0%, #00a0e9 100%);
            color: white;
            padding: 40px 0;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .header-content {
            max-width: 800px;
            margin: 0 auto;
        }

        h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        h2 {
            font-size: 1.8rem;
            margin-top: 30px;
            color: #0056b3;
            border-bottom: 2px solid #e1e4e8;
            padding-bottom: 10px;
        }

        h3 {
            font-size: 1.4rem;
            color: #0056b3;
            margin-top: 25px;
        }

        /* Navigation */
        .nav-bar {
            background-color: #343a40;
            padding: 15px 0;
        }

        .nav-content {
            max-width: 1400px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
        }

        .nav-links {
            display: flex;
            gap: 30px;
        }

        .nav-links a {
            color: white;
            text-decoration: none;
            padding: 10px 15px;
            border-radius: 4px;
            transition: background-color 0.3s;
        }

        .nav-links a:hover, .nav-links a.active {
            background-color: #0056b3;
        }

        /* Content Sections */
        .section {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            padding: 25px;
            margin-bottom: 30px;
        }

        /* Module Objectives */
        .objectives {
            background-color: #e8f4fd;
            border-left: 4px solid #0056b3;
            padding: 20px;
            margin: 20px 0;
            border-radius: 0 4px 4px 0;
        }

        /* Simulation Area */
        #simulation-area {
            border: 2px solid #0056b3;
            padding: 25px;
            min-height: 400px;
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        /* Simulation Controls */
        .control-panel {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            align-items: center;
            border: 1px solid #dee2e6;
        }

        .control-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .control-group label {
            font-weight: bold;
            color: #495057;
            font-size: 0.9rem;
        }

        /* Vital Signs Display */
        .vital-sign {
            font-size: 1.2em;
            margin-bottom: 8px;
            padding: 15px;
            background-color: #f8f9fa;
            border-left: 5px solid #0056b3;
            border-radius: 4px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: all 0.3s ease;
        }

        .vital-sign:hover {
            background-color: #e9ecef;
            transform: translateX(5px);
        }

        .vital-sign strong {
            color: #0056b3;
            font-weight: 600;
        }

        .vital-value {
            font-weight: bold;
            color: #28a745;
            font-size: 1.1em;
        }

        .vital-value.abnormal {
            color: #dc3545;
            animation: pulse 1s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }

        /* Device Displays */
        .device-display {
            display: none;
        }

        .device-display.active {
            display: block;
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Calibration Section */
        .calibration-panel {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }

        .calibration-step {
            background-color: white;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 15px;
            margin: 10px 0;
            position: relative;
        }

        .calibration-step.active {
            border-color: #0056b3;
            background-color: #f8f9ff;
        }

        .calibration-step.completed {
            border-color: #28a745;
            background-color: #f8fff9;
        }

        .step-number {
            position: absolute;
            top: -10px;
            left: 15px;
            background-color: #0056b3;
            color: white;
            width: 25px;
            height: 25px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .step-number.completed {
            background-color: #28a745;
        }
        /* Button Styles */
        button {
            padding: 12px 24px;
            margin: 5px;
            cursor: pointer;
            background-color: #0056b3;
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 1em;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        button:hover {
            background-color: #003d82;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }

        button:active {
            background-color: #002952;
            transform: translateY(0);
        }

        button.secondary {
            background-color: #6c757d;
        }

        button.secondary:hover {
            background-color: #545b62;
        }

        button.success {
            background-color: #28a745;
        }

        button.success:hover {
            background-color: #1e7e34;
        }

        button.warning {
            background-color: #ffc107;
            color: #212529;
        }

        button.warning:hover {
            background-color: #e0a800;
        }

        button.danger {
            background-color: #dc3545;
        }

        button.danger:hover {
            background-color: #c82333;
        }

        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
            opacity: 0.6;
        }

        button:disabled:hover {
            transform: none;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        /* Device Status */
        #device-status {
            margin-top: 20px;
            padding: 20px;
            border-radius: 8px;
            font-weight: bold;
            text-align: center;
            font-size: 1.1em;
            transition: all 0.3s ease;
        }

        .status-running {
            background-color: #d4edda;
            color: #155724;
            border: 2px solid #28a745;
            animation: statusPulse 2s infinite;
        }

        .status-stopped {
            background-color: #f8d7da;
            color: #721c24;
            border: 2px solid #dc3545;
        }

        .status-calibrating {
            background-color: #fff3cd;
            color: #856404;
            border: 2px solid #ffc107;
            animation: statusPulse 1s infinite;
        }

        @keyframes statusPulse {
            0% { opacity: 1; }
            50% { opacity: 0.8; }
            100% { opacity: 1; }
        }

        /* Form Elements */
        select, input[type="number"], input[type="text"] {
            padding: 10px;
            border: 2px solid #dee2e6;
            border-radius: 6px;
            font-size: 1em;
            transition: border-color 0.3s ease;
            background-color: white;
        }

        select:focus, input:focus {
            outline: none;
            border-color: #0056b3;
            box-shadow: 0 0 0 3px rgba(0, 86, 179, 0.1);
        }

        .device-selection {
            min-width: 200px;
        }

        .scenario-selection {
            min-width: 150px;
        }

        /* Settings and Scenario Containers */
        .scenario-container,
        .settings-container {
            margin-bottom: 20px;
            padding: 20px;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            background-color: #f8f9fa;
        }

        .settings-container label {
            display: inline-block;
            margin-right: 15px;
            margin-bottom: 10px;
            font-weight: 600;
            color: #495057;
        }

        .settings-container input[type="number"],
        .settings-container select {
            margin-right: 20px;
            margin-bottom: 10px;
        }

        /* Measurement Display */
        .measurement-display {
            background-color: #000;
            color: #00ff00;
            font-family: 'Courier New', monospace;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            font-size: 1.2em;
            text-align: center;
            border: 2px solid #333;
        }

        .measurement-value {
            font-size: 2em;
            font-weight: bold;
            margin: 10px 0;
        }

        /* Progress Indicators */
        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background-color: #0056b3;
            transition: width 0.3s ease;
            border-radius: 10px;
        }

        /* Visual Aids and Charts */
        .chart-container {
            background-color: white;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }

        .waveform-display {
            background-color: #000;
            color: #00ff00;
            font-family: 'Courier New', monospace;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            height: 200px;
            position: relative;
            overflow: hidden;
        }

        .waveform-line {
            position: absolute;
            bottom: 50%;
            left: 0;
            width: 100%;
            height: 2px;
            background-color: #00ff00;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background-color: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .data-table th {
            background-color: #0056b3;
            color: white;
            padding: 12px;
            text-align: left;
            font-weight: 600;
        }

        .data-table td {
            padding: 10px 12px;
            border-bottom: 1px solid #dee2e6;
        }

        .data-table tr:hover {
            background-color: #f8f9fa;
        }

        .formula-box {
            background-color: #f8f9ff;
            border: 2px solid #0056b3;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            font-family: 'Times New Roman', serif;
        }

        .formula {
            font-size: 1.2em;
            text-align: center;
            margin: 10px 0;
            padding: 10px;
            background-color: white;
            border-radius: 4px;
        }

        .graph-placeholder {
            background: linear-gradient(45deg, #f8f9fa 25%, transparent 25%),
                        linear-gradient(-45deg, #f8f9fa 25%, transparent 25%),
                        linear-gradient(45deg, transparent 75%, #f8f9fa 75%),
                        linear-gradient(-45deg, transparent 75%, #f8f9fa 75%);
            background-size: 20px 20px;
            background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            color: #6c757d;
            font-style: italic;
            margin: 20px 0;
            min-height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
        }

        .instrument-panel {
            background-color: #2c3e50;
            color: white;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        .digital-display {
            background-color: #000;
            color: #00ff00;
            font-family: 'Courier New', monospace;
            font-size: 2em;
            font-weight: bold;
            text-align: center;
            padding: 20px;
            border-radius: 4px;
            margin: 10px 0;
            border: 2px solid #333;
            text-shadow: 0 0 10px #00ff00;
        }

        .analog-gauge {
            width: 200px;
            height: 200px;
            border: 4px solid #333;
            border-radius: 50%;
            margin: 20px auto;
            position: relative;
            background: radial-gradient(circle, #f8f9fa 0%, #e9ecef 100%);
        }

        .gauge-needle {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 2px;
            height: 80px;
            background-color: #dc3545;
            transform-origin: bottom center;
            transform: translate(-50%, -100%) rotate(45deg);
            transition: transform 0.5s ease;
        }

        .statistics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .stat-card {
            background-color: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }

        .stat-value {
            font-size: 2em;
            font-weight: bold;
            color: #0056b3;
            margin: 10px 0;
        }

        .stat-label {
            color: #6c757d;
            font-size: 0.9em;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        /* Tabs */
        .tab-container {
            margin-bottom: 20px;
        }

        .tab-buttons {
            display: flex;
            border-bottom: 2px solid #dee2e6;
            margin-bottom: 20px;
        }

        .tab-button {
            padding: 15px 25px;
            background-color: transparent;
            border: none;
            border-bottom: 3px solid transparent;
            cursor: pointer;
            font-size: 1em;
            font-weight: 500;
            color: #6c757d;
            transition: all 0.3s ease;
        }

        .tab-button.active {
            color: #0056b3;
            border-bottom-color: #0056b3;
            background-color: #f8f9fa;
        }

        .tab-button:hover {
            color: #0056b3;
            background-color: #f8f9fa;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
            animation: fadeIn 0.3s ease-in;
        }

        /* Footer */
        footer {
            background-color: #343a40;
            color: white;
            text-align: center;
            padding: 20px 0;
            margin-top: 40px;
        }
    </style>
</head>
<body>
    <header>
        <div class="header-content">
            <h1>Clinical Engineering Interactive Simulations</h1>
            <p>Hands-on learning for medical device operation, measurement, and calibration</p>
        </div>
    </header>

    <nav class="nav-bar">
        <div class="nav-content">
            <div class="nav-links">
                <a href="#overview" class="nav-link active" onclick="showSection('overview')">Module Overview</a>
                <a href="#device-simulation" class="nav-link" onclick="showSection('device-simulation')">Device Simulation</a>
                <a href="#calibration" class="nav-link" onclick="showSection('calibration')">Calibration Lab</a>
                <a href="#measurements" class="nav-link" onclick="showSection('measurements')">Measurements</a>
                <a href="#troubleshooting" class="nav-link" onclick="showSection('troubleshooting')">Troubleshooting</a>
            </div>
            <a href="index.html" style="color: white; text-decoration: none;">← Back to Course</a>
        </div>
    </nav>

    <div class="container">
        <!-- Module Overview Section -->
        <div id="overview" class="section active-section">
            <h2>Interactive Simulations Module</h2>

            <div class="objectives">
                <h3>Learning Objectives</h3>
                <p>Upon completion of this interactive simulation module, students will be able to:</p>
                <ul>
                    <li><strong>Operate</strong> various medical devices safely and effectively</li>
                    <li><strong>Perform</strong> routine calibration procedures on biomedical equipment</li>
                    <li><strong>Conduct</strong> accurate measurements using clinical engineering instruments</li>
                    <li><strong>Troubleshoot</strong> common device malfunctions and performance issues</li>
                    <li><strong>Document</strong> calibration results and maintenance activities</li>
                    <li><strong>Apply</strong> quality assurance principles in clinical engineering practice</li>
                    <li><strong>Interpret</strong> device performance data and identify anomalies</li>
                    <li><strong>Implement</strong> preventive maintenance protocols</li>
                </ul>
            </div>

            <h3>Simulation Modules Available</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 20px 0;">
                <div class="section" style="margin: 0;">
                    <h4>🏥 Device Operation Simulator</h4>
                    <p>Interactive simulation of patient monitors, ECG machines, ventilators, and other critical care equipment.</p>
                    <ul>
                        <li>Patient Monitor Systems</li>
                        <li>ECG/EKG Monitors</li>
                        <li>Respiratory Monitors</li>
                        <li>Hemodynamic Monitors</li>
                        <li>Neurological Monitors</li>
                    </ul>
                </div>

                <div class="section" style="margin: 0;">
                    <h4>⚙️ Calibration Laboratory</h4>
                    <p>Step-by-step calibration procedures for biomedical equipment with virtual instruments.</p>
                    <ul>
                        <li>Pressure Calibration</li>
                        <li>Temperature Calibration</li>
                        <li>Flow Calibration</li>
                        <li>Electrical Safety Testing</li>
                        <li>Performance Verification</li>
                    </ul>
                </div>

                <div class="section" style="margin: 0;">
                    <h4>📊 Measurement Systems</h4>
                    <p>Hands-on experience with clinical engineering measurement techniques and data analysis.</p>
                    <ul>
                        <li>Vital Signs Measurement</li>
                        <li>Signal Analysis</li>
                        <li>Accuracy Assessment</li>
                        <li>Uncertainty Calculation</li>
                        <li>Data Documentation</li>
                    </ul>
                </div>

                <div class="section" style="margin: 0;">
                    <h4>🔧 Troubleshooting Scenarios</h4>
                    <p>Real-world problem-solving scenarios to develop diagnostic and repair skills.</p>
                    <ul>
                        <li>Fault Diagnosis</li>
                        <li>Performance Issues</li>
                        <li>Safety Concerns</li>
                        <li>User Error Analysis</li>
                        <li>Corrective Actions</li>
                    </ul>
                </div>
            </div>

            <h3>How to Use This Module</h3>
            <ol>
                <li><strong>Start with Device Simulation:</strong> Familiarize yourself with equipment operation</li>
                <li><strong>Practice Calibration:</strong> Learn systematic calibration procedures</li>
                <li><strong>Master Measurements:</strong> Develop precision measurement skills</li>
                <li><strong>Apply Troubleshooting:</strong> Solve realistic equipment problems</li>
                <li><strong>Document Everything:</strong> Practice proper record-keeping</li>
            </ol>
        </div>

        <!-- Device Simulation Section -->
        <div id="device-simulation" class="section" style="display: none;">
            <h2>Medical Device Simulation</h2>
            <p>Interactive simulation of medical devices commonly found in clinical settings. Practice device operation, parameter adjustment, and data interpretation.</p>

            <div class="control-panel">
                <div class="control-group">
                    <label>Device Type:</label>
                    <select id="deviceSelector" class="device-selection" onchange="switchDevice()">
                        <option value="patientMonitor">Patient Monitor</option>
                        <option value="ecgMonitor">ECG Monitor</option>
                        <option value="respiratoryMonitor">Respiratory Monitor</option>
                        <option value="hemodynamicMonitor">Hemodynamic Monitor</option>
                        <option value="neurologicalMonitor">Neurological Monitor</option>
                    </select>
                </div>

                <div class="control-group">
                    <label>Controls:</label>
                    <button onclick="startSimulation()">Start Device</button>
                    <button onclick="stopSimulation()" class="secondary">Stop Device</button>
                    <button onclick="resetSimulation()" class="warning">Reset</button>
                </div>

                <div class="control-group">
                    <label>Simulation Speed:</label>
                    <select id="simulationSpeed" onchange="updateSimulationSpeed()">
                        <option value="1000">Slow (1s)</option>
                        <option value="2000" selected>Normal (2s)</option>
                        <option value="500">Fast (0.5s)</option>
                    </select>
                </div>
            </div>

            <div id="simulation-area">
                <p>Device simulation ready. Select a device and click "Start Device" to begin monitoring.</p>

                <!-- Patient Monitor Display -->
                <div id="patientMonitorDisplay" class="device-display active">
                    <div class="scenario-container">
                        <strong>Patient Scenario:</strong>
                        <select id="patientScenario" class="scenario-selection" onchange="updateVitalSigns()">
                            <option value="normal">Normal Patient</option>
                            <option value="tachycardia">Tachycardia (HR >100)</option>
                            <option value="hypotension">Hypotension (SBP <90)</option>
                            <option value="hypoxemia">Hypoxemia (SpO2 <92)</option>
                            <option value="critical">Critical Patient</option>
                        </select>
                    </div>

                    <!-- Waveform Display -->
                    <div class="chart-container">
                        <h4>ECG Waveform Monitor</h4>
                        <div class="waveform-display" id="ecg-waveform">
                            <canvas id="ecgCanvas" width="800" height="150"></canvas>
                            <div style="position: absolute; top: 10px; left: 10px; font-size: 0.8em;">
                                Lead II | 25 mm/s | 10 mm/mV
                            </div>
                            <div style="position: absolute; top: 10px; right: 10px; font-size: 0.8em;">
                                <span id="ecg-hr-display">HR: -- bpm</span>
                            </div>
                        </div>
                    </div>

                    <!-- Vital Signs Display Grid -->
                    <div class="statistics-grid">
                        <div class="stat-card">
                            <div class="stat-label">Heart Rate</div>
                            <div id="heartRate" class="stat-value">-- bpm</div>
                            <div style="font-size: 0.8em; color: #6c757d;">
                                Range: 60-100 bpm
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-label">Blood Pressure</div>
                            <div id="bloodPressure" class="stat-value">--/-- mmHg</div>
                            <div style="font-size: 0.8em; color: #6c757d;">
                                MAP: <span id="meanAP">--</span> mmHg
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-label">Oxygen Saturation</div>
                            <div id="oxygenSaturation" class="stat-value">-- %</div>
                            <div class="progress-bar">
                                <div class="progress-fill" id="spo2-progress"></div>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-label">Temperature</div>
                            <div id="temperature" class="stat-value">-- °C</div>
                            <div style="font-size: 0.8em; color: #6c757d;">
                                <span id="tempFahrenheit">-- °F</span>
                            </div>
                        </div>
                    </div>

                    <!-- Alarm Status Panel -->
                    <div class="instrument-panel">
                        <h4 style="margin-top: 0;">Alarm Status</h4>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                            <div id="hr-alarm" class="digital-display" style="font-size: 1em; padding: 10px;">
                                HR: Normal
                            </div>
                            <div id="bp-alarm" class="digital-display" style="font-size: 1em; padding: 10px;">
                                BP: Normal
                            </div>
                            <div id="spo2-alarm" class="digital-display" style="font-size: 1em; padding: 10px;">
                                SpO2: Normal
                            </div>
                            <div id="temp-alarm" class="digital-display" style="font-size: 1em; padding: 10px;">
                                Temp: Normal
                            </div>
                        </div>
                    </div>

                    <!-- Trend Data Table -->
                    <div class="chart-container">
                        <h4>Vital Signs Trend (Last 10 Readings)</h4>
                        <table class="data-table" id="vitals-trend-table">
                            <thead>
                                <tr>
                                    <th>Time</th>
                                    <th>HR (bpm)</th>
                                    <th>SBP (mmHg)</th>
                                    <th>DBP (mmHg)</th>
                                    <th>SpO2 (%)</th>
                                    <th>Temp (°C)</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody id="vitals-trend-body">
                            </tbody>
                        </table>
                    </div>
                </div>

        <div id="ecgMonitorDisplay" class="device-display">
            <div class="vital-sign">
                <strong>Heart Rhythm:</strong> <span id="heartRhythm" class="vital-value">--</span>
            </div>
            <div class="vital-sign">
                <strong>QRS Duration:</strong> <span id="qrsDuration" class="vital-value">-- ms</span>
            </div>
        </div>

        <div id="respiratoryMonitorDisplay" class="device-display">
            <div class="settings-container">
                <strong>Ventilator Settings:</strong><br>
                <label for="ventilationMode">Mode:</label>
                <select id="ventilationMode" onchange="updateVitalSigns()">
                    <option value="AC">Assist-Control (AC)</option>
                    <option value="SIMV">Synchronized Intermittent Mandatory Ventilation (SIMV)</option>
                    <option value="CPAP">Continuous Positive Airway Pressure (CPAP)</option>
                </select>
                <label for="fio2">FiO2 (%):</label>
                <input type="number" id="fio2" value="21" min="21" max="100" step="1" onchange="updateVitalSigns()">
            </div>
            <div class="vital-sign">
                <strong>Tidal Volume:</strong> <span id="tidalVolume" class="vital-value">-- mL</span>
            </div>
            <div class="vital-sign">
                <strong>Respiratory Rate:</strong> <span id="respiratoryRate" class="vital-value">-- bpm</span>
            </div>
            <div class="vital-sign">
                <strong>Oxygen Saturation:</strong> <span id="respOxygenSaturation" class="vital-value">-- %</span>
            </div>
            <div class="vital-sign">
                <strong>End-tidal CO2:</strong> <span id="etCO2" class="vital-value">-- mmHg</span>
            </div>
        </div>

        <div id="hemodynamicMonitorDisplay" class="device-display">
            <div class="vital-sign">
                <strong>CVP:</strong> <span id="cvp" class="vital-value">-- mmHg</span>
            </div>
            <div class="vital-sign">
                <strong>PAP:</strong> <span id="pap" class="vital-value">--/-- mmHg</span>
            </div>
            <div class="vital-sign">
                <strong>Cardiac Output:</strong> <span id="cardiacOutput" class="vital-value">-- L/min</span>
            </div>
            <div class="vital-sign">
                <strong>SVR:</strong> <span id="svr" class="vital-value">-- dynes·s/cm⁵</span>
            </div>
        </div>

        <div id="neurologicalMonitorDisplay" class="device-display">
            <div class="vital-sign">
                <strong>ICP:</strong> <span id="icp" class="vital-value">-- mmHg</span>
            </div>
            <div class="vital-sign">
                <strong>CPP:</strong> <span id="cpp" class="vital-value">-- mmHg</span>
            </div>
            <div class="vital-sign">
                <strong>GCS:</strong> <span id="gcs" class="vital-value">--</span>
            </div>
        </div>

                <div id="device-status" class="status-stopped">Device Status: Stopped</div>
            </div>
        </div>

        <!-- Calibration Laboratory Section -->
        <div id="calibration" class="section" style="display: none;">
            <h2>Calibration Laboratory</h2>
            <p>Learn systematic calibration procedures for biomedical equipment. Follow step-by-step protocols to ensure device accuracy and compliance.</p>

            <div class="tab-container">
                <div class="tab-buttons">
                    <button class="tab-button active" onclick="showCalibrationTab('pressure')">Pressure Calibration</button>
                    <button class="tab-button" onclick="showCalibrationTab('temperature')">Temperature Calibration</button>
                    <button class="tab-button" onclick="showCalibrationTab('electrical')">Electrical Safety</button>
                    <button class="tab-button" onclick="showCalibrationTab('flow')">Flow Calibration</button>
                </div>

                <!-- Pressure Calibration Tab -->
                <div id="pressure-tab" class="tab-content active">
                    <h3>Blood Pressure Monitor Calibration</h3>
                    <p>Calibrate a non-invasive blood pressure monitor using a pressure calibrator with traceability to national standards.</p>

                    <!-- Calibration Theory -->
                    <div class="formula-box">
                        <h4>Calibration Theory & Formulas</h4>
                        <div class="formula">
                            <strong>Error Calculation:</strong><br>
                            Error = Device Reading - Reference Value
                        </div>
                        <div class="formula">
                            <strong>Percent Error:</strong><br>
                            % Error = (Error / Reference Value) × 100%
                        </div>
                        <div class="formula">
                            <strong>Accuracy Specification:</strong><br>
                            Typical: ±3 mmHg or ±2% of reading (whichever is greater)
                        </div>
                        <div class="formula">
                            <strong>Mean Arterial Pressure:</strong><br>
                            MAP = DBP + (SBP - DBP)/3
                        </div>
                    </div>

                    <div class="calibration-panel">
                        <h4>Equipment Required:</h4>
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>Equipment</th>
                                    <th>Specification</th>
                                    <th>Accuracy</th>
                                    <th>Calibration Due</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>Blood Pressure Monitor</td>
                                    <td>Device Under Test (DUT)</td>
                                    <td>±3 mmHg</td>
                                    <td>Annual</td>
                                </tr>
                                <tr>
                                    <td>Pressure Calibrator</td>
                                    <td>Fluke Biomedical ProSim 8</td>
                                    <td>±0.1 mmHg</td>
                                    <td>Valid until 2025-12-31</td>
                                </tr>
                                <tr>
                                    <td>Pressure Cuff</td>
                                    <td>Adult size (22-32 cm)</td>
                                    <td>Leak-free</td>
                                    <td>Visual inspection</td>
                                </tr>
                                <tr>
                                    <td>Connecting Tubing</td>
                                    <td>Standard BP tubing</td>
                                    <td>Leak-free</td>
                                    <td>Visual inspection</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                        <h4>Calibration Procedure:</h4>
                        <div class="calibration-step active" id="pressure-step-1">
                            <div class="step-number">1</div>
                            <h5>Pre-Calibration Setup</h5>
                            <p>Connect the pressure calibrator to the blood pressure monitor. Ensure all connections are secure and leak-free.</p>
                            <button onclick="completeCalibrationStep('pressure', 1)" class="success">Complete Step 1</button>
                        </div>

                        <div class="calibration-step" id="pressure-step-2">
                            <div class="step-number">2</div>
                            <h5>Zero Point Verification</h5>
                            <p>Set the pressure calibrator to 0 mmHg. Verify that the blood pressure monitor reads 0 ± 2 mmHg.</p>

                            <!-- Digital Instrument Display -->
                            <div class="instrument-panel">
                                <h5>Pressure Calibrator Display</h5>
                                <div class="digital-display" id="calibrator-display">
                                    0.0 mmHg
                                </div>
                                <div style="text-align: center; margin: 10px 0;">
                                    <button onclick="setPressureReference(0)" class="success">Set Zero Reference</button>
                                </div>
                            </div>

                            <!-- Measurement Results -->
                            <div class="statistics-grid">
                                <div class="stat-card">
                                    <div class="stat-label">Reference Standard</div>
                                    <div id="pressure-reference" class="stat-value">0.0 mmHg</div>
                                    <div style="font-size: 0.8em; color: #28a745;">Calibrator</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-label">Device Reading</div>
                                    <div id="pressure-device" class="stat-value">0.0 mmHg</div>
                                    <div style="font-size: 0.8em; color: #0056b3;">BP Monitor</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-label">Error</div>
                                    <div id="pressure-error" class="stat-value">0.0 mmHg</div>
                                    <div style="font-size: 0.8em;" id="error-status">Within Tolerance</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-label">Percent Error</div>
                                    <div id="pressure-percent-error" class="stat-value">0.0%</div>
                                    <div style="font-size: 0.8em;">Specification: ±2%</div>
                                </div>
                            </div>

                            <button onclick="completeCalibrationStep('pressure', 2)" class="success" disabled>Complete Step 2</button>
                        </div>

                        <div class="calibration-step" id="pressure-step-3">
                            <div class="step-number">3</div>
                            <h5>Mid-Range Calibration</h5>
                            <p>Set calibrator to 100 mmHg. Record device reading and calculate error.</p>
                            <button onclick="setPressureReference(100)">Set 100 mmHg</button>
                            <button onclick="completeCalibrationStep('pressure', 3)" class="success" disabled>Complete Step 3</button>
                        </div>

                        <div class="calibration-step" id="pressure-step-4">
                            <div class="step-number">4</div>
                            <h5>High-Range Calibration</h5>
                            <p>Set calibrator to 200 mmHg. Record device reading and calculate error.</p>
                            <button onclick="setPressureReference(200)">Set 200 mmHg</button>
                            <button onclick="completeCalibrationStep('pressure', 4)" class="success" disabled>Complete Step 4</button>
                        </div>

                        <div class="calibration-step" id="pressure-step-5">
                            <div class="step-number">5</div>
                            <h5>Documentation</h5>
                            <p>Record all measurements and determine if the device passes calibration criteria (±3 mmHg or ±2% of reading).</p>
                            <div id="pressure-results" style="display: none;">
                                <h5>Calibration Results:</h5>
                                <table style="width: 100%; border-collapse: collapse; margin: 10px 0;">
                                    <tr style="background-color: #f8f9fa;">
                                        <th style="border: 1px solid #dee2e6; padding: 8px;">Reference (mmHg)</th>
                                        <th style="border: 1px solid #dee2e6; padding: 8px;">Device Reading (mmHg)</th>
                                        <th style="border: 1px solid #dee2e6; padding: 8px;">Error (mmHg)</th>
                                        <th style="border: 1px solid #dee2e6; padding: 8px;">Status</th>
                                    </tr>
                                    <tr id="pressure-result-0"></tr>
                                    <tr id="pressure-result-100"></tr>
                                    <tr id="pressure-result-200"></tr>
                                </table>
                                <div id="pressure-pass-fail"></div>
                            </div>
                            <button onclick="completeCalibrationStep('pressure', 5)" class="success" disabled>Complete Calibration</button>
                        </div>
                    </div>
                </div>

                <!-- Temperature Calibration Tab -->
                <div id="temperature-tab" class="tab-content">
                    <h3>Temperature Probe Calibration</h3>
                    <p>Calibrate temperature probes using a temperature calibrator with ice point and body temperature references.</p>

                    <div class="calibration-panel">
                        <h4>Equipment Required:</h4>
                        <ul>
                            <li>Temperature Monitor (Device Under Test)</li>
                            <li>Temperature Calibrator/Bath</li>
                            <li>Reference Thermometer</li>
                            <li>Temperature Probes</li>
                        </ul>

                        <h4>Calibration Points:</h4>
                        <div class="control-panel">
                            <button onclick="setTemperatureReference(0)">Ice Point (0°C)</button>
                            <button onclick="setTemperatureReference(37)">Body Temp (37°C)</button>
                            <button onclick="setTemperatureReference(42)">Hyperthermia (42°C)</button>
                        </div>

                        <div class="measurement-display">
                            <div>Reference: <span id="temp-reference">--</span> °C</div>
                            <div>Device Reading: <span id="temp-device" class="measurement-value">--</span> °C</div>
                            <div>Error: <span id="temp-error">--</span> °C</div>
                        </div>
                    </div>
                </div>

                <!-- Electrical Safety Tab -->
                <div id="electrical-tab" class="tab-content">
                    <h3>Electrical Safety Testing</h3>
                    <p>Perform electrical safety tests including leakage current, ground resistance, and insulation resistance.</p>

                    <div class="calibration-panel">
                        <h4>Safety Tests:</h4>
                        <div class="control-panel">
                            <button onclick="performSafetyTest('leakage')">Leakage Current Test</button>
                            <button onclick="performSafetyTest('ground')">Ground Resistance Test</button>
                            <button onclick="performSafetyTest('insulation')">Insulation Resistance Test</button>
                        </div>

                        <div id="safety-results">
                            <div class="measurement-display">
                                <div>Test Type: <span id="safety-test-type">--</span></div>
                                <div>Measured Value: <span id="safety-value" class="measurement-value">--</span></div>
                                <div>Limit: <span id="safety-limit">--</span></div>
                                <div>Status: <span id="safety-status">--</span></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Flow Calibration Tab -->
                <div id="flow-tab" class="tab-content">
                    <h3>Flow Meter Calibration</h3>
                    <p>Calibrate flow meters used in respiratory equipment and infusion pumps.</p>

                    <div class="calibration-panel">
                        <h4>Flow Calibration Points:</h4>
                        <div class="control-panel">
                            <button onclick="setFlowReference(1)">1 L/min</button>
                            <button onclick="setFlowReference(5)">5 L/min</button>
                            <button onclick="setFlowReference(10)">10 L/min</button>
                            <button onclick="setFlowReference(15)">15 L/min</button>
                        </div>

                        <div class="measurement-display">
                            <div>Reference: <span id="flow-reference">--</span> L/min</div>
                            <div>Device Reading: <span id="flow-device" class="measurement-value">--</span> L/min</div>
                            <div>Error: <span id="flow-error">--</span> L/min</div>
                            <div>% Error: <span id="flow-percent-error">--</span> %</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Measurements Section -->
        <div id="measurements" class="section" style="display: none;">
            <h2>Clinical Engineering Measurements</h2>
            <p>Practice precision measurement techniques and data analysis used in clinical engineering.</p>

            <div class="tab-container">
                <div class="tab-buttons">
                    <button class="tab-button active" onclick="showMeasurementTab('accuracy')">Accuracy Assessment</button>
                    <button class="tab-button" onclick="showMeasurementTab('uncertainty')">Uncertainty Analysis</button>
                    <button class="tab-button" onclick="showMeasurementTab('trending')">Performance Trending</button>
                </div>

                <!-- Accuracy Assessment Tab -->
                <div id="accuracy-tab" class="tab-content active">
                    <h3>Device Accuracy Assessment</h3>
                    <p>Measure and analyze the accuracy of medical devices compared to reference standards using statistical methods.</p>

                    <!-- Statistical Formulas Reference -->
                    <div class="formula-box">
                        <h4>Statistical Analysis Formulas</h4>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px;">
                            <div class="formula">
                                <strong>Mean Error:</strong><br>
                                x̄ = (Σxi) / n
                            </div>
                            <div class="formula">
                                <strong>Standard Deviation:</strong><br>
                                σ = √[(Σ(xi - x̄)²) / (n-1)]
                            </div>
                            <div class="formula">
                                <strong>Standard Error:</strong><br>
                                SE = σ / √n
                            </div>
                            <div class="formula">
                                <strong>95% Confidence Interval:</strong><br>
                                CI = x̄ ± (t₀.₀₂₅ × SE)
                            </div>
                            <div class="formula">
                                <strong>Coefficient of Variation:</strong><br>
                                CV = (σ / x̄) × 100%
                            </div>
                            <div class="formula">
                                <strong>Bias:</strong><br>
                                Bias = Σ(Device - Reference) / n
                            </div>
                        </div>
                    </div>

                    <div class="control-panel">
                        <div class="control-group">
                            <label>Device Type:</label>
                            <select id="accuracyDevice">
                                <option value="bp">Blood Pressure Monitor</option>
                                <option value="temp">Temperature Monitor</option>
                                <option value="spo2">Pulse Oximeter</option>
                                <option value="flow">Flow Meter</option>
                            </select>
                        </div>
                        <div class="control-group">
                            <label>Number of Measurements:</label>
                            <select id="measurementCount">
                                <option value="10">10 measurements</option>
                                <option value="20">20 measurements</option>
                                <option value="30">30 measurements</option>
                            </select>
                        </div>
                        <div class="control-group">
                            <label>Test Range:</label>
                            <select id="testRange">
                                <option value="full">Full Range</option>
                                <option value="clinical">Clinical Range</option>
                                <option value="critical">Critical Range</option>
                            </select>
                        </div>
                        <button onclick="startAccuracyTest()">Start Accuracy Test</button>
                        <button onclick="generateAccuracyReport()" class="secondary">Generate Report</button>
                        <button onclick="exportData()" class="warning">Export Data</button>
                    </div>

                    <div id="accuracy-measurements" style="display: none;">
                        <h4>Measurement Data</h4>
                        <table id="accuracy-table" style="width: 100%; border-collapse: collapse; margin: 20px 0;">
                            <thead>
                                <tr style="background-color: #f8f9fa;">
                                    <th style="border: 1px solid #dee2e6; padding: 10px;">Measurement #</th>
                                    <th style="border: 1px solid #dee2e6; padding: 10px;">Reference Value</th>
                                    <th style="border: 1px solid #dee2e6; padding: 10px;">Device Reading</th>
                                    <th style="border: 1px solid #dee2e6; padding: 10px;">Error</th>
                                    <th style="border: 1px solid #dee2e6; padding: 10px;">% Error</th>
                                </tr>
                            </thead>
                            <tbody id="accuracy-tbody">
                            </tbody>
                        </table>

                        <!-- Statistical Analysis Results -->
                        <div id="accuracy-statistics">
                            <h4>Statistical Analysis Results</h4>

                            <!-- Primary Statistics -->
                            <div class="statistics-grid">
                                <div class="stat-card">
                                    <div class="stat-label">Mean Error</div>
                                    <div id="mean-error" class="stat-value">--</div>
                                    <div style="font-size: 0.8em; color: #6c757d;">Average bias</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-label">Standard Deviation</div>
                                    <div id="std-deviation" class="stat-value">--</div>
                                    <div style="font-size: 0.8em; color: #6c757d;">Precision measure</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-label">Maximum Error</div>
                                    <div id="max-error" class="stat-value">--</div>
                                    <div style="font-size: 0.8em; color: #6c757d;">Worst case</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-label">Accuracy</div>
                                    <div id="accuracy-percent" class="stat-value">--</div>
                                    <div style="font-size: 0.8em; color: #6c757d;">Overall performance</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-label">Standard Error</div>
                                    <div id="standard-error" class="stat-value">--</div>
                                    <div style="font-size: 0.8em; color: #6c757d;">SE = σ/√n</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-label">CV (%)</div>
                                    <div id="coefficient-variation" class="stat-value">--</div>
                                    <div style="font-size: 0.8em; color: #6c757d;">Relative variability</div>
                                </div>
                            </div>

                            <!-- Confidence Intervals -->
                            <div class="chart-container">
                                <h5>95% Confidence Intervals</h5>
                                <table class="data-table">
                                    <thead>
                                        <tr>
                                            <th>Parameter</th>
                                            <th>Lower Limit</th>
                                            <th>Upper Limit</th>
                                            <th>Interpretation</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>Mean Error</td>
                                            <td id="ci-mean-lower">--</td>
                                            <td id="ci-mean-upper">--</td>
                                            <td id="ci-mean-interp">--</td>
                                        </tr>
                                        <tr>
                                            <td>Standard Deviation</td>
                                            <td id="ci-std-lower">--</td>
                                            <td id="ci-std-upper">--</td>
                                            <td id="ci-std-interp">--</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>

                            <!-- Error Distribution Graph -->
                            <div class="chart-container">
                                <h5>Error Distribution Analysis</h5>
                                <div class="graph-placeholder" id="error-distribution-chart">
                                    📊 Error Distribution Histogram<br>
                                    <small>Shows the frequency distribution of measurement errors</small><br>
                                    <div style="margin-top: 20px;">
                                        <div style="display: inline-block; width: 20px; height: 20px; background-color: #0056b3; margin-right: 10px;"></div>
                                        Normal Distribution Curve<br>
                                        <div style="display: inline-block; width: 20px; height: 20px; background-color: #28a745; margin-right: 10px;"></div>
                                        Actual Error Distribution
                                    </div>
                                </div>
                            </div>

                            <!-- Bland-Altman Plot -->
                            <div class="chart-container">
                                <h5>Bland-Altman Analysis</h5>
                                <div class="graph-placeholder" id="bland-altman-plot">
                                    📈 Bland-Altman Plot<br>
                                    <small>Difference vs. Average plot for agreement analysis</small><br>
                                    <div style="margin-top: 20px;">
                                        <div style="display: inline-block; width: 20px; height: 20px; background-color: #dc3545; margin-right: 10px;"></div>
                                        Mean Difference (Bias)<br>
                                        <div style="display: inline-block; width: 20px; height: 20px; background-color: #ffc107; margin-right: 10px;"></div>
                                        ±1.96 SD Limits of Agreement
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Uncertainty Analysis Tab -->
                <div id="uncertainty-tab" class="tab-content">
                    <h3>Measurement Uncertainty Analysis</h3>
                    <p>Calculate and analyze measurement uncertainty according to GUM (Guide to the Expression of Uncertainty in Measurement) and ISO/IEC 17025 requirements.</p>

                    <!-- GUM Uncertainty Formulas -->
                    <div class="formula-box">
                        <h4>GUM Uncertainty Analysis Formulas</h4>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 15px;">
                            <div class="formula">
                                <strong>Combined Standard Uncertainty:</strong><br>
                                u<sub>c</sub>(y) = √[Σ(∂f/∂x<sub>i</sub>)² × u²(x<sub>i</sub>)]
                            </div>
                            <div class="formula">
                                <strong>Type A Uncertainty:</strong><br>
                                u<sub>A</sub> = s/√n = √[Σ(x<sub>i</sub> - x̄)²/(n-1)]/√n
                            </div>
                            <div class="formula">
                                <strong>Type B Uncertainty:</strong><br>
                                u<sub>B</sub> = a/√3 (rectangular distribution)
                            </div>
                            <div class="formula">
                                <strong>Expanded Uncertainty:</strong><br>
                                U = k × u<sub>c</sub>(y), where k = coverage factor
                            </div>
                            <div class="formula">
                                <strong>Effective Degrees of Freedom:</strong><br>
                                ν<sub>eff</sub> = u<sub>c</sub>⁴(y) / Σ[u⁴(x<sub>i</sub>)/ν<sub>i</sub>]
                            </div>
                            <div class="formula">
                                <strong>Coverage Factor (k):</strong><br>
                                k = t<sub>p</sub>(ν<sub>eff</sub>) for confidence level p
                            </div>
                        </div>
                    </div>

                    <div class="control-panel">
                        <div class="control-group">
                            <label>Measurement Type:</label>
                            <select id="uncertaintyType">
                                <option value="pressure">Pressure Measurement</option>
                                <option value="temperature">Temperature Measurement</option>
                                <option value="flow">Flow Measurement</option>
                                <option value="voltage">Voltage Measurement</option>
                            </select>
                        </div>
                        <div class="control-group">
                            <label>Confidence Level:</label>
                            <select id="confidenceLevel">
                                <option value="68.27">68.27% (k≈1)</option>
                                <option value="95.45" selected>95.45% (k≈2)</option>
                                <option value="99.73">99.73% (k≈3)</option>
                            </select>
                        </div>
                        <div class="control-group">
                            <label>Number of Measurements:</label>
                            <input type="number" id="uncertaintyMeasurements" value="10" min="3" max="100">
                        </div>
                        <button onclick="calculateUncertainty()">Calculate Uncertainty</button>
                        <button onclick="generateUncertaintyBudget()" class="secondary">Generate Budget</button>
                    </div>

                    <div id="uncertainty-analysis">
                        <h4>Uncertainty Budget Analysis</h4>

                        <!-- Uncertainty Components Table -->
                        <div class="chart-container">
                            <h5>Uncertainty Budget Components</h5>
                            <table class="data-table" id="uncertainty-budget-table">
                                <thead>
                                    <tr>
                                        <th>Source of Uncertainty</th>
                                        <th>Type</th>
                                        <th>Value</th>
                                        <th>Distribution</th>
                                        <th>Divisor</th>
                                        <th>Standard Uncertainty</th>
                                        <th>Degrees of Freedom</th>
                                        <th>% Contribution</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>Repeatability</td>
                                        <td>A</td>
                                        <td id="repeatability-value">±0.5</td>
                                        <td>Normal</td>
                                        <td>√n</td>
                                        <td id="repeatability-std">±0.16</td>
                                        <td id="repeatability-dof">9</td>
                                        <td id="repeatability-contrib">15%</td>
                                    </tr>
                                    <tr>
                                        <td>Calibration Certificate</td>
                                        <td>B</td>
                                        <td id="calibration-value">±0.3</td>
                                        <td>Normal</td>
                                        <td>2</td>
                                        <td id="calibration-std">±0.15</td>
                                        <td>∞</td>
                                        <td id="calibration-contrib">13%</td>
                                    </tr>
                                    <tr>
                                        <td>Resolution</td>
                                        <td>B</td>
                                        <td id="resolution-value">±0.1</td>
                                        <td>Rectangular</td>
                                        <td>√3</td>
                                        <td id="resolution-std">±0.06</td>
                                        <td>∞</td>
                                        <td id="resolution-contrib">2%</td>
                                    </tr>
                                    <tr>
                                        <td>Environmental</td>
                                        <td>B</td>
                                        <td id="environmental-value">±0.2</td>
                                        <td>Rectangular</td>
                                        <td>√3</td>
                                        <td id="environmental-std">±0.12</td>
                                        <td>∞</td>
                                        <td id="environmental-contrib">8%</td>
                                    </tr>
                                    <tr>
                                        <td>Drift</td>
                                        <td>B</td>
                                        <td id="drift-value">±0.1</td>
                                        <td>Rectangular</td>
                                        <td>√3</td>
                                        <td id="drift-std">±0.06</td>
                                        <td>∞</td>
                                        <td id="drift-contrib">2%</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <!-- Combined Uncertainty Results -->
                        <div class="statistics-grid">
                            <div class="stat-card">
                                <div class="stat-label">Combined Standard Uncertainty</div>
                                <div id="combined-uncertainty" class="stat-value">±0.6 units</div>
                                <div style="font-size: 0.8em; color: #6c757d;">u<sub>c</sub>(y)</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-label">Effective Degrees of Freedom</div>
                                <div id="effective-dof" class="stat-value">12</div>
                                <div style="font-size: 0.8em; color: #6c757d;">ν<sub>eff</sub></div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-label">Coverage Factor</div>
                                <div id="coverage-factor" class="stat-value">2.18</div>
                                <div style="font-size: 0.8em; color: #6c757d;">k (95.45%)</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-label">Expanded Uncertainty</div>
                                <div id="expanded-uncertainty" class="stat-value">±1.3 units</div>
                                <div style="font-size: 0.8em; color: #6c757d;">U = k × u<sub>c</sub></div>
                            </div>
                        </div>

                        <!-- Uncertainty Contribution Chart -->
                        <div class="chart-container">
                            <h5>Uncertainty Contribution Analysis</h5>
                            <div class="graph-placeholder" id="uncertainty-pie-chart">
                                🥧 Uncertainty Contribution Pie Chart<br>
                                <small>Shows relative contribution of each uncertainty source</small><br>
                                <div style="margin-top: 20px; text-align: left;">
                                    <div style="display: inline-block; width: 20px; height: 20px; background-color: #0056b3; margin-right: 10px;"></div>
                                    Repeatability (Type A): 60%<br>
                                    <div style="display: inline-block; width: 20px; height: 20px; background-color: #28a745; margin-right: 10px;"></div>
                                    Calibration (Type B): 25%<br>
                                    <div style="display: inline-block; width: 20px; height: 20px; background-color: #ffc107; margin-right: 10px;"></div>
                                    Environmental (Type B): 10%<br>
                                    <div style="display: inline-block; width: 20px; height: 20px; background-color: #dc3545; margin-right: 10px;"></div>
                                    Other Sources: 5%
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Performance Trending Tab -->
                <div id="trending-tab" class="tab-content">
                    <h3>Device Performance Trending</h3>
                    <p>Monitor device performance over time to identify drift and predict maintenance needs.</p>

                    <div class="control-panel">
                        <button onclick="generateTrendData()">Generate Trend Data</button>
                        <button onclick="analyzeTrend()" class="secondary">Analyze Trend</button>
                        <button onclick="predictMaintenance()" class="warning">Predict Maintenance</button>
                    </div>

                    <div id="trend-analysis">
                        <div class="measurement-display">
                            <div>Current Drift Rate</div>
                            <div id="drift-rate" class="measurement-value">+0.02 units/month</div>
                            <div>Predicted Out-of-Tolerance</div>
                            <div id="predicted-oot" class="measurement-value">18 months</div>
                        </div>

                        <div style="margin: 20px 0;">
                            <h4>Performance Trend (Last 12 Months)</h4>
                            <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center;">
                                <p style="color: #6c757d; font-style: italic;">
                                    📊 Trend Chart Placeholder<br>
                                    (In a real implementation, this would show an interactive chart)
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Troubleshooting Section -->
        <div id="troubleshooting" class="section" style="display: none;">
            <h2>Equipment Troubleshooting Scenarios</h2>
            <p>Practice diagnostic skills with realistic equipment problems using systematic troubleshooting methodologies.</p>

            <!-- Troubleshooting Methodology -->
            <div class="formula-box">
                <h4>Systematic Troubleshooting Methodology</h4>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px;">
                    <div class="formula">
                        <strong>1. Problem Definition:</strong><br>
                        • Identify symptoms<br>
                        • Gather user reports<br>
                        • Document error codes
                    </div>
                    <div class="formula">
                        <strong>2. Information Gathering:</strong><br>
                        • Review maintenance history<br>
                        • Check environmental conditions<br>
                        • Analyze usage patterns
                    </div>
                    <div class="formula">
                        <strong>3. Hypothesis Formation:</strong><br>
                        • List possible causes<br>
                        • Prioritize by probability<br>
                        • Consider failure modes
                    </div>
                    <div class="formula">
                        <strong>4. Testing & Verification:</strong><br>
                        • Perform diagnostic tests<br>
                        • Isolate components<br>
                        • Verify measurements
                    </div>
                    <div class="formula">
                        <strong>5. Root Cause Analysis:</strong><br>
                        • Apply 5-Why technique<br>
                        • Use fishbone diagrams<br>
                        • Identify failure mode
                    </div>
                    <div class="formula">
                        <strong>6. Corrective Action:</strong><br>
                        • Implement solution<br>
                        • Test functionality<br>
                        • Document findings
                    </div>
                </div>
            </div>

            <div class="control-panel">
                <div class="control-group">
                    <label>Scenario Type:</label>
                    <select id="troubleshootingScenario" onchange="loadTroubleshootingScenario()">
                        <option value="">Select a scenario...</option>
                        <option value="bp-error">Blood Pressure Monitor Error E02</option>
                        <option value="ventilator-alarm">Ventilator High Pressure Alarm</option>
                        <option value="monitor-noise">Patient Monitor Signal Noise</option>
                        <option value="pump-occlusion">Infusion Pump Occlusion Alert</option>
                        <option value="defibrillator-charge">Defibrillator Charge Failure</option>
                        <option value="ecg-artifact">ECG Baseline Artifact</option>
                    </select>
                </div>
                <div class="control-group">
                    <label>Difficulty Level:</label>
                    <select id="difficultyLevel">
                        <option value="beginner">Beginner</option>
                        <option value="intermediate" selected>Intermediate</option>
                        <option value="advanced">Advanced</option>
                    </select>
                </div>
                <button onclick="startTroubleshooting()" id="start-troubleshooting" disabled>Start Troubleshooting</button>
                <button onclick="showTroubleshootingGuide()" class="secondary">View Guide</button>
            </div>

            <div id="troubleshooting-content" style="display: none;">
                <!-- Scenario Description -->
                <div id="scenario-description" class="section">
                    <h3 id="scenario-title">Scenario Title</h3>
                    <p id="scenario-details">Scenario details will appear here...</p>

                    <!-- Equipment Information -->
                    <div class="chart-container">
                        <h4>Equipment Information</h4>
                        <table class="data-table" id="equipment-info-table">
                            <thead>
                                <tr>
                                    <th>Parameter</th>
                                    <th>Value</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody id="equipment-info-body">
                            </tbody>
                        </table>
                    </div>

                    <!-- Observed Symptoms -->
                    <div id="scenario-symptoms">
                        <h4>Observed Symptoms:</h4>
                        <div class="statistics-grid" id="symptoms-grid">
                        </div>
                    </div>

                    <!-- Environmental Conditions -->
                    <div class="instrument-panel">
                        <h4>Environmental Conditions</h4>
                        <div class="statistics-grid">
                            <div class="stat-card">
                                <div class="stat-label">Temperature</div>
                                <div class="stat-value" id="env-temp">22°C</div>
                                <div style="font-size: 0.8em; color: #28a745;">Normal</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-label">Humidity</div>
                                <div class="stat-value" id="env-humidity">45%</div>
                                <div style="font-size: 0.8em; color: #28a745;">Normal</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-label">Line Voltage</div>
                                <div class="stat-value" id="env-voltage">120V</div>
                                <div style="font-size: 0.8em; color: #28a745;">Normal</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-label">Frequency</div>
                                <div class="stat-value" id="env-frequency">60Hz</div>
                                <div style="font-size: 0.8em; color: #28a745;">Normal</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Diagnostic Process -->
                <div id="diagnostic-steps">
                    <h3>Systematic Diagnostic Process</h3>

                    <!-- Diagnostic Tree -->
                    <div class="chart-container">
                        <h4>Diagnostic Decision Tree</h4>
                        <div class="graph-placeholder" id="diagnostic-tree">
                            🌳 Interactive Diagnostic Tree<br>
                            <small>Follow the decision tree to systematically isolate the problem</small><br>
                            <div style="margin-top: 20px; text-align: left;">
                                <div style="margin: 5px 0;">
                                    <div style="display: inline-block; width: 20px; height: 20px; background-color: #dc3545; margin-right: 10px;"></div>
                                    Problem Identified
                                </div>
                                <div style="margin: 5px 0;">
                                    <div style="display: inline-block; width: 20px; height: 20px; background-color: #ffc107; margin-right: 10px;"></div>
                                    Test Required
                                </div>
                                <div style="margin: 5px 0;">
                                    <div style="display: inline-block; width: 20px; height: 20px; background-color: #28a745; margin-right: 10px;"></div>
                                    Test Passed
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Diagnostic Steps -->
                    <div id="troubleshooting-steps">
                        <!-- Steps will be populated by JavaScript -->
                    </div>

                    <!-- Test Results -->
                    <div class="chart-container" id="test-results-section" style="display: none;">
                        <h4>Diagnostic Test Results</h4>
                        <table class="data-table" id="test-results-table">
                            <thead>
                                <tr>
                                    <th>Test</th>
                                    <th>Expected Result</th>
                                    <th>Actual Result</th>
                                    <th>Status</th>
                                    <th>Action Required</th>
                                </tr>
                            </thead>
                            <tbody id="test-results-body">
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Troubleshooting Results -->
                <div id="troubleshooting-results" style="display: none;">
                    <h3>Troubleshooting Analysis Results</h3>

                    <!-- Root Cause Analysis -->
                    <div class="chart-container">
                        <h4>Root Cause Analysis</h4>
                        <div id="diagnosis-summary"></div>

                        <!-- Fishbone Diagram -->
                        <div class="graph-placeholder" id="fishbone-diagram">
                            🐟 Fishbone (Ishikawa) Diagram<br>
                            <small>Cause and effect analysis for the identified problem</small><br>
                            <div style="margin-top: 20px; text-align: left;">
                                <strong>Categories:</strong><br>
                                • People (User Error, Training)<br>
                                • Process (Procedures, Maintenance)<br>
                                • Equipment (Hardware, Software)<br>
                                • Environment (Temperature, Humidity)<br>
                                • Materials (Consumables, Parts)
                            </div>
                        </div>
                    </div>

                    <!-- Corrective Actions -->
                    <div class="chart-container">
                        <h4>Corrective and Preventive Actions (CAPA)</h4>
                        <div id="corrective-actions"></div>

                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>Action Type</th>
                                    <th>Description</th>
                                    <th>Responsible</th>
                                    <th>Timeline</th>
                                    <th>Verification</th>
                                </tr>
                            </thead>
                            <tbody id="capa-table-body">
                            </tbody>
                        </table>
                    </div>

                    <!-- Prevention Measures -->
                    <div class="chart-container">
                        <h4>Prevention and Risk Mitigation</h4>
                        <div id="prevention-measures"></div>
                    </div>
                </div>
            </div>
        </div>

    </div>

    <footer>
        <p>&copy; 2025 Clinical Engineering Interactive Simulations | Dr. Mohammed Yagoub Esmail</p>
        <p>Nahda College | All Rights Reserved</p>
    </footer>

    <script>
        // Global Variables
        let simulationInterval;
        let isRunning = false;
        let activeDevice = 'patientMonitor';
        let patientScenario = 'normal';
        let simulationSpeed = 2000;
        let calibrationData = {};
        let measurementData = [];
        let currentCalibrationStep = {};

        // Navigation Functions
        function showSection(sectionId) {
            // Hide all sections
            const sections = document.querySelectorAll('.section');
            sections.forEach(section => section.style.display = 'none');

            // Remove active class from all nav links
            const navLinks = document.querySelectorAll('.nav-link');
            navLinks.forEach(link => link.classList.remove('active'));

            // Show selected section
            document.getElementById(sectionId).style.display = 'block';

            // Add active class to clicked nav link
            event.target.classList.add('active');
        }

        // Device Simulation Functions
        function updateSimulationSpeed() {
            const speedSelector = document.getElementById('simulationSpeed');
            simulationSpeed = parseInt(speedSelector.value);

            if (isRunning) {
                clearInterval(simulationInterval);
                simulationInterval = setInterval(updateVitalSigns, simulationSpeed);
            }
        }

        function updateVitalSigns() {
            if (!isRunning) return;

            if (activeDevice === 'patientMonitor') {
                const scenarioSelector = document.getElementById('patientScenario');
                patientScenario = scenarioSelector ? scenarioSelector.value : 'normal';

                let heartRate, systolicBP, diastolicBP, oxygenSat;

                switch (patientScenario) {
                    case 'normal':
                        heartRate = Math.floor(Math.random() * (90 - 60 + 1)) + 60;
                        systolicBP = Math.floor(Math.random() * (130 - 110 + 1)) + 110;
                        diastolicBP = Math.floor(Math.random() * (85 - 70 + 1)) + 70;
                        oxygenSat = Math.floor(Math.random() * (100 - 95 + 1)) + 95;
                        break;
                    case 'tachycardia':
                        heartRate = Math.floor(Math.random() * (120 - 100 + 1)) + 100;
                        systolicBP = Math.floor(Math.random() * (120 - 100 + 1)) + 100;
                        diastolicBP = Math.floor(Math.random() * (80 - 60 + 1)) + 60;
                        oxygenSat = Math.floor(Math.random() * (98 - 95 + 1)) + 95;
                        break;
                    case 'hypotension':
                        heartRate = Math.floor(Math.random() * (100 - 80 + 1)) + 80;
                        systolicBP = Math.floor(Math.random() * (90 - 70 + 1)) + 70;
                        diastolicBP = Math.floor(Math.random() * (60 - 40 + 1)) + 40;
                        oxygenSat = Math.floor(Math.random() * (95 - 92 + 1)) + 92;
                        break;
                    case 'hypoxemia':
                        heartRate = Math.floor(Math.random() * (110 - 90 + 1)) + 90;
                        systolicBP = Math.floor(Math.random() * (130 - 110 + 1)) + 110;
                        diastolicBP = Math.floor(Math.random() * (85 - 70 + 1)) + 70;
                        oxygenSat = Math.floor(Math.random() * (90 - 85 + 1)) + 85;
                        break;
                    default:
                        heartRate = Math.floor(Math.random() * (90 - 60 + 1)) + 60;
                        systolicBP = Math.floor(Math.random() * (130 - 110 + 1)) + 110;
                        diastolicBP = Math.floor(Math.random() * (85 - 70 + 1)) + 70;
                        oxygenSat = Math.floor(Math.random() * (100 - 95 + 1)) + 95;
                }

                // Calculate additional parameters
                const temperature = (Math.random() * (38.5 - 36.0) + 36.0).toFixed(1);
                const tempF = (temperature * 9/5 + 32).toFixed(1);
                const meanAP = Math.round(diastolicBP + (systolicBP - diastolicBP)/3);

                // Update vital signs displays
                document.getElementById('heartRate').textContent = `${heartRate}`;
                document.getElementById('bloodPressure').textContent = `${systolicBP}/${diastolicBP}`;
                document.getElementById('oxygenSaturation').textContent = `${oxygenSat}`;
                document.getElementById('temperature').textContent = `${temperature}`;
                document.getElementById('tempFahrenheit').textContent = `${tempF}`;
                document.getElementById('meanAP').textContent = `${meanAP}`;
                document.getElementById('ecg-hr-display').textContent = `HR: ${heartRate} bpm`;

                // Update progress bar for SpO2
                const spo2Progress = document.getElementById('spo2-progress');
                if (spo2Progress) {
                    spo2Progress.style.width = `${oxygenSat}%`;
                }

                // Update alarm status
                updateAlarmStatus(heartRate, systolicBP, diastolicBP, oxygenSat, temperature);

                // Update trend table
                updateVitalsTrend(heartRate, systolicBP, diastolicBP, oxygenSat, temperature);

                // Draw ECG waveform
                drawECGWaveform(heartRate);

                // Update status based on values
                const heartRateElement = document.getElementById('heartRate');
                heartRateElement.className = 'stat-value' + ((heartRate > 95 || heartRate < 55) ? ' abnormal' : '');
                const bpElement = document.getElementById('bloodPressure');
                bpElement.className = 'stat-value' + ((systolicBP < 90 || diastolicBP < 50) ? ' abnormal' : '');
                const spo2Element = document.getElementById('oxygenSaturation');
                spo2Element.className = 'stat-value' + ((oxygenSat < 92) ? ' abnormal' : '');

            } else if (activeDevice === 'ecgMonitor') {
                const rhythms = ["Normal Sinus Rhythm", "Sinus Tachycardia", "Sinus Bradycardia"];
                const heartRhythm = rhythms[Math.floor(Math.random() * rhythms.length)];
                const qrsDuration = Math.floor(Math.random() * (120 - 80 + 1)) + 80;

                document.getElementById('heartRhythm').textContent = heartRhythm;
                document.getElementById('qrsDuration').textContent = `${qrsDuration} ms`;

                const heartRhythmElement = document.getElementById('heartRhythm');
                heartRhythmElement.className = 'vital-value' + (heartRhythm !== "Normal Sinus Rhythm" ? ' abnormal' : '');
            }
            // Additional device types would continue here...
        }

                // This code was moved to the enhanced updateVitalSigns function above
            } else if (activeDevice === 'respiratoryMonitor') {
                // Get ventilator settings
                const ventilationMode = document.getElementById('ventilationMode').value;
                const fio2 = parseInt(document.getElementById('fio2').value);

                let tidalVolume, respiratoryRate, respOxygenSaturation, etCO2;

                // Simulate Respiratory parameters based on settings
                // Basic logic: higher FiO2 -> higher SpO2
                // Different modes could influence RR/Vt ranges

                if (ventilationMode === 'AC') {
                    tidalVolume = Math.floor(Math.random() * (550 - 450 + 1)) + 450; // Tightly controlled
                    respiratoryRate = Math.floor(Math.random() * (18 - 14 + 1)) + 14; // Controlled rate
                } else if (ventilationMode === 'SIMV') {
                    tidalVolume = Math.floor(Math.random() * (650 - 350 + 1)) + 350; // More variability
                    respiratoryRate = Math.floor(Math.random() * (22 - 10 + 1)) + 10; // Patient can breathe spontaneously
                } else if (ventilationMode === 'CPAP') {
                    tidalVolume = Math.floor(Math.random() * (400 - 200 + 1)) + 200; // Patient's own effort
                    respiratoryRate = Math.floor(Math.random() * (25 - 12 + 1)) + 12; // Patient's own effort
                }

                // FiO2 influence on SpO2
                if (fio2 >= 80) {
                    respOxygenSaturation = Math.floor(Math.random() * (100 - 98 + 1)) + 98;
                } else if (fio2 >= 50) {
                    respOxygenSaturation = Math.floor(Math.random() * (99 - 95 + 1)) + 95;
                } else if (fio2 >= 30) {
                    respOxygenSaturation = Math.floor(Math.random() * (97 - 93 + 1)) + 93;
                } else {
                    respOxygenSaturation = Math.floor(Math.random() * (95 - 90 + 1)) + 90;
                }

                etCO2 = Math.floor(Math.random() * (45 - 35 + 1)) + 35; // 35-45 mmHg

                document.getElementById('tidalVolume').textContent = `${tidalVolume} mL`;
                document.getElementById('respiratoryRate').textContent = `${respiratoryRate} bpm`;
                document.getElementById('respOxygenSaturation').textContent = `${respOxygenSaturation} %`;
                document.getElementById('etCO2').textContent = `${etCO2} mmHg`;

                // Basic anomaly highlighting for respiratory rate and SpO2
                const rrElement = document.getElementById('respiratoryRate');
                rrElement.style.color = (respiratoryRate > 22 || respiratoryRate < 10) ? '#dc3545' : '#28a745';
                const respSpo2Element = document.getElementById('respOxygenSaturation');
                respSpo2Element.style.color = (respOxygenSaturation < 92) ? '#dc3545' : '#28a745';

            } else if (activeDevice === 'hemodynamicMonitor') {
                // Simulate Hemodynamic parameters
                const cvp = (Math.random() * (12 - 2) + 2).toFixed(1); // 2-12 mmHg
                const papSystolic = Math.floor(Math.random() * (30 - 18 + 1)) + 18; // 18-30 mmHg
                const papDiastolic = Math.floor(Math.random() * (15 - 5 + 1)) + 5; // 5-15 mmHg
                const cardiacOutput = (Math.random() * (6 - 4) + 4).toFixed(1); // 4-6 L/min
                const svr = Math.floor(Math.random() * (1200 - 800 + 1)) + 800; // 800-1200 dynes·s/cm⁵

                document.getElementById('cvp').textContent = `${cvp} mmHg`;
                document.getElementById('pap').textContent = `${papSystolic}/${papDiastolic} mmHg`;
                document.getElementById('cardiacOutput').textContent = `${cardiacOutput} L/min`;
                document.getElementById('svr').textContent = `${svr} dynes·s/cm⁵`;

                // Basic anomaly highlighting for CVP
                const cvpElement = document.getElementById('cvp');
                if (cvp > 8 || cvp < 4) {
                    cvpElement.style.color = '#dc3545';
                } else {
                    cvpElement.style.color = '#28a745';
                }
            } else if (activeDevice === 'neurologicalMonitor') {
                // Simulate Neurological parameters
                const icp = (Math.random() * (15 - 5) + 5).toFixed(1); // 5-15 mmHg
                const map = Math.floor(Math.random() * (100 - 70 + 1)) + 70; // 70-100 mmHg (for CPP calculation)
                const cpp = (map - icp).toFixed(1); // CPP = MAP - ICP
                const gcs = Math.floor(Math.random() * (15 - 3 + 1)) + 3; // 3-15

                document.getElementById('icp').textContent = `${icp} mmHg`;
                document.getElementById('cpp').textContent = `${cpp} mmHg`;
                document.getElementById('gcs').textContent = `${gcs}`;

                // Basic anomaly highlighting for ICP
                const icpElement = document.getElementById('icp');
                if (icp > 10) {
                    icpElement.style.color = '#dc3545';
                } else {
                    icpElement.style.color = '#28a745';
                }
            }
        }

        function switchDevice() {
            stopSimulation(); // Stop current simulation
            const selector = document.getElementById('deviceSelector');
            activeDevice = selector.value;

            // Hide all device displays
            document.getElementById('patientMonitorDisplay').classList.remove('active');
            document.getElementById('ecgMonitorDisplay').classList.remove('active');
            document.getElementById('respiratoryMonitorDisplay').classList.remove('active');
            document.getElementById('hemodynamicMonitorDisplay').classList.remove('active');
            document.getElementById('neurologicalMonitorDisplay').classList.remove('active');

            // Show the selected device display
            const selectedDisplay = document.getElementById(`${activeDevice}Display`);
            selectedDisplay.classList.add('active');

            document.getElementById('simulation-area').innerHTML = `<p>Device simulation ready. Select a device and click "Start Device" to begin monitoring.</p>`;
            document.getElementById('device-status').className = 'status-stopped';
            document.getElementById('device-status').textContent = 'Device Status: Stopped';

            // Re-append the selected display and status to the simulation area
            document.getElementById('simulation-area').appendChild(selectedDisplay);
            document.getElementById('simulation-area').appendChild(document.getElementById('device-status'));

            // Reset vital signs display for the new device
            if (activeDevice === 'patientMonitor') {
                document.getElementById('heartRate').textContent = '-- bpm';
                document.getElementById('bloodPressure').textContent = '--/-- mmHg';
                document.getElementById('oxygenSaturation').textContent = '-- %';
                // Reset scenario selector to normal when switching to patient monitor
                const scenarioSelector = document.getElementById('patientScenario');
                if (scenarioSelector) scenarioSelector.value = 'normal';
                patientScenario = 'normal';
            } else if (activeDevice === 'ecgMonitor') {
                document.getElementById('heartRhythm').textContent = '--';
                document.getElementById('qrsDuration').textContent = '-- ms';
            } else if (activeDevice === 'respiratoryMonitor') {
                document.getElementById('tidalVolume').textContent = '-- mL';
                document.getElementById('respiratoryRate').textContent = '-- bpm';
                document.getElementById('respOxygenSaturation').textContent = '-- %';
                document.getElementById('etCO2').textContent = '-- mmHg';
                // Reset ventilator settings to default
                document.getElementById('ventilationMode').value = 'AC';
                document.getElementById('fio2').value = '21';
            } else if (activeDevice === 'hemodynamicMonitor') {
                document.getElementById('cvp').textContent = '-- mmHg';
                document.getElementById('pap').textContent = '--/-- mmHg';
                document.getElementById('cardiacOutput').textContent = '-- L/min';
                document.getElementById('svr').textContent = '-- dynes·s/cm⁵';
            } else if (activeDevice === 'neurologicalMonitor') {
                document.getElementById('icp').textContent = '-- mmHg';
                document.getElementById('cpp').textContent = '-- mmHg';
                document.getElementById('gcs').textContent = '--';
            }
        }

        function startSimulation() {
            if (isRunning) return;
            isRunning = true;
            document.getElementById('device-status').className = 'status-running';
            document.getElementById('device-status').textContent = 'Device Status: Running';

            // Clear initial message and show relevant device display
            document.getElementById('simulation-area').innerHTML = '';
            const selectedDisplay = document.getElementById(`${activeDevice}Display`);
            document.getElementById('simulation-area').appendChild(selectedDisplay);
            selectedDisplay.classList.add('active');
            document.getElementById('simulation-area').appendChild(document.getElementById('device-status'));

            updateVitalSigns(); // Initial update
            simulationInterval = setInterval(updateVitalSigns, 2000); // Update every 2 seconds
            console.log('Simulation started for ' + activeDevice);
        }

        function stopSimulation() {
            if (!isRunning) return;
            isRunning = false;
            clearInterval(simulationInterval);
            document.getElementById('device-status').className = 'status-stopped';
            document.getElementById('device-status').textContent = 'Device Status: Stopped';
            console.log('Simulation stopped for ' + activeDevice);
        }

        function resetSimulation() {
            stopSimulation(); // Stop if running
            document.getElementById('simulation-area').innerHTML = `<p>Device simulation ready. Select a device and click "Start Device" to begin monitoring.</p>`;
            document.getElementById('device-status').className = 'status-stopped';
            document.getElementById('device-status').textContent = 'Device Status: Stopped';

            // Re-append the selected display and status to the simulation area
            const selectedDisplay = document.getElementById(`${activeDevice}Display`);
            document.getElementById('simulation-area').appendChild(selectedDisplay);
            selectedDisplay.classList.add('active'); // Ensure it's active after reset
            document.getElementById('simulation-area').appendChild(document.getElementById('device-status'));

            // Reset vital signs display for the current device
            if (activeDevice === 'patientMonitor') {
                document.getElementById('heartRate').textContent = '-- bpm';
                document.getElementById('bloodPressure').textContent = '--/-- mmHg';
                document.getElementById('oxygenSaturation').textContent = '-- %';
                const scenarioSelector = document.getElementById('patientScenario');
                if (scenarioSelector) scenarioSelector.value = 'normal';
                patientScenario = 'normal';
            } else if (activeDevice === 'ecgMonitor') {
                document.getElementById('heartRhythm').textContent = '--';
                document.getElementById('qrsDuration').textContent = '-- ms';
            } else if (activeDevice === 'respiratoryMonitor') {
                document.getElementById('tidalVolume').textContent = '-- mL';
                document.getElementById('respiratoryRate').textContent = '-- bpm';
                document.getElementById('respOxygenSaturation').textContent = '-- %';
                document.getElementById('etCO2').textContent = '-- mmHg';
                document.getElementById('ventilationMode').value = 'AC';
                document.getElementById('fio2').value = '21';
            } else if (activeDevice === 'hemodynamicMonitor') {
                document.getElementById('cvp').textContent = '-- mmHg';
                document.getElementById('pap').textContent = '--/-- mmHg';
                document.getElementById('cardiacOutput').textContent = '-- L/min';
                document.getElementById('svr').textContent = '-- dynes·s/cm⁵';
            } else if (activeDevice === 'neurologicalMonitor') {
                document.getElementById('icp').textContent = '-- mmHg';
                document.getElementById('cpp').textContent = '-- mmHg';
                document.getElementById('gcs').textContent = '--';
            }
            console.log('Simulation reset.');
        }

        // Calibration Functions
        function showCalibrationTab(tabName) {
            // Hide all tabs
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelectorAll('.tab-button').forEach(btn => {
                btn.classList.remove('active');
            });

            // Show selected tab
            document.getElementById(tabName + '-tab').classList.add('active');
            event.target.classList.add('active');
        }

        function completeCalibrationStep(type, stepNumber) {
            const stepElement = document.getElementById(`${type}-step-${stepNumber}`);
            stepElement.classList.add('completed');
            stepElement.querySelector('.step-number').classList.add('completed');

            // Enable next step
            const nextStep = document.getElementById(`${type}-step-${stepNumber + 1}`);
            if (nextStep) {
                nextStep.classList.add('active');
                nextStep.querySelector('button').disabled = false;
            }

            // Show results for final step
            if (type === 'pressure' && stepNumber === 5) {
                document.getElementById('pressure-results').style.display = 'block';
                generatePressureResults();
            }
        }

        function setPressureReference(value) {
            document.getElementById('pressure-reference').textContent = value;

            // Simulate device reading with some error
            const error = (Math.random() - 0.5) * 4; // ±2 mmHg random error
            const deviceReading = value + error;

            document.getElementById('pressure-device').textContent = deviceReading.toFixed(1);
            document.getElementById('pressure-error').textContent = error.toFixed(1);

            // Store calibration data
            if (!calibrationData.pressure) calibrationData.pressure = {};
            calibrationData.pressure[value] = {
                reference: value,
                device: deviceReading,
                error: error
            };

            // Enable completion button for current step
            if (value === 100) {
                document.querySelector('#pressure-step-3 button').disabled = false;
            } else if (value === 200) {
                document.querySelector('#pressure-step-4 button').disabled = false;
            }
        }

        function generatePressureResults() {
            const results = calibrationData.pressure;
            let allPass = true;

            Object.keys(results).forEach(ref => {
                const data = results[ref];
                const pass = Math.abs(data.error) <= 3; // ±3 mmHg tolerance
                allPass = allPass && pass;

                const row = document.getElementById(`pressure-result-${ref}`);
                row.innerHTML = `
                    <td style="border: 1px solid #dee2e6; padding: 8px;">${data.reference}</td>
                    <td style="border: 1px solid #dee2e6; padding: 8px;">${data.device.toFixed(1)}</td>
                    <td style="border: 1px solid #dee2e6; padding: 8px;">${data.error.toFixed(1)}</td>
                    <td style="border: 1px solid #dee2e6; padding: 8px; color: ${pass ? '#28a745' : '#dc3545'};">
                        ${pass ? 'PASS' : 'FAIL'}
                    </td>
                `;
            });

            document.getElementById('pressure-pass-fail').innerHTML = `
                <h5 style="color: ${allPass ? '#28a745' : '#dc3545'};">
                    Overall Result: ${allPass ? 'PASS' : 'FAIL'}
                </h5>
            `;
        }

        function setTemperatureReference(value) {
            document.getElementById('temp-reference').textContent = value;
            const error = (Math.random() - 0.5) * 0.4; // ±0.2°C random error
            const deviceReading = value + error;
            document.getElementById('temp-device').textContent = deviceReading.toFixed(1);
            document.getElementById('temp-error').textContent = error.toFixed(1);
        }

        function setFlowReference(value) {
            document.getElementById('flow-reference').textContent = value;
            const error = (Math.random() - 0.5) * 0.2; // ±0.1 L/min random error
            const deviceReading = value + error;
            const percentError = (error / value) * 100;

            document.getElementById('flow-device').textContent = deviceReading.toFixed(2);
            document.getElementById('flow-error').textContent = error.toFixed(2);
            document.getElementById('flow-percent-error').textContent = percentError.toFixed(1);
        }

        function performSafetyTest(testType) {
            let value, limit, unit, status;

            switch (testType) {
                case 'leakage':
                    value = (Math.random() * 50).toFixed(1); // 0-50 µA
                    limit = '100 µA';
                    unit = 'µA';
                    status = parseFloat(value) < 100 ? 'PASS' : 'FAIL';
                    break;
                case 'ground':
                    value = (Math.random() * 0.2).toFixed(3); // 0-0.2 Ω
                    limit = '0.1 Ω';
                    unit = 'Ω';
                    status = parseFloat(value) < 0.1 ? 'PASS' : 'FAIL';
                    break;
                case 'insulation':
                    value = (Math.random() * 50 + 50).toFixed(0); // 50-100 MΩ
                    limit = '10 MΩ';
                    unit = 'MΩ';
                    status = parseFloat(value) > 10 ? 'PASS' : 'FAIL';
                    break;
            }

            document.getElementById('safety-test-type').textContent = testType.charAt(0).toUpperCase() + testType.slice(1);
            document.getElementById('safety-value').textContent = value + ' ' + unit;
            document.getElementById('safety-limit').textContent = limit;
            document.getElementById('safety-status').textContent = status;
            document.getElementById('safety-status').style.color = status === 'PASS' ? '#28a745' : '#dc3545';
        }

        // Measurement Functions
        function showMeasurementTab(tabName) {
            document.querySelectorAll('#measurements .tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelectorAll('#measurements .tab-button').forEach(btn => {
                btn.classList.remove('active');
            });

            document.getElementById(tabName + '-tab').classList.add('active');
            event.target.classList.add('active');
        }

        function startAccuracyTest() {
            const device = document.getElementById('accuracyDevice').value;
            const measurementCount = parseInt(document.getElementById('measurementCount').value);
            const testRange = document.getElementById('testRange').value;

            document.getElementById('accuracy-measurements').style.display = 'block';

            // Generate measurement points based on selected parameters
            const tbody = document.getElementById('accuracy-tbody');
            tbody.innerHTML = '';
            measurementData = [];

            for (let i = 1; i <= measurementCount; i++) {
                let reference, deviceReading, error, percentError;

                switch (device) {
                    case 'bp':
                        if (testRange === 'clinical') {
                            reference = 90 + (i * 90 / measurementCount); // 90-180 mmHg
                        } else if (testRange === 'critical') {
                            reference = 60 + (i * 40 / measurementCount); // 60-100 mmHg
                        } else {
                            reference = 50 + (i * 200 / measurementCount); // 50-250 mmHg
                        }
                        error = (Math.random() - 0.5) * 6; // ±3 mmHg
                        deviceReading = reference + error;
                        break;
                    case 'temp':
                        if (testRange === 'clinical') {
                            reference = 35 + (i * 5 / measurementCount); // 35-40°C
                        } else if (testRange === 'critical') {
                            reference = 32 + (i * 10 / measurementCount); // 32-42°C
                        } else {
                            reference = 20 + (i * 30 / measurementCount); // 20-50°C
                        }
                        error = (Math.random() - 0.5) * 0.4; // ±0.2°C
                        deviceReading = reference + error;
                        break;
                    case 'spo2':
                        if (testRange === 'clinical') {
                            reference = 90 + (i * 10 / measurementCount); // 90-100%
                        } else if (testRange === 'critical') {
                            reference = 80 + (i * 15 / measurementCount); // 80-95%
                        } else {
                            reference = 70 + (i * 30 / measurementCount); // 70-100%
                        }
                        error = (Math.random() - 0.5) * 4; // ±2%
                        deviceReading = reference + error;
                        break;
                    case 'flow':
                        if (testRange === 'clinical') {
                            reference = 1 + (i * 14 / measurementCount); // 1-15 L/min
                        } else if (testRange === 'critical') {
                            reference = 0.5 + (i * 4.5 / measurementCount); // 0.5-5 L/min
                        } else {
                            reference = 0.1 + (i * 19.9 / measurementCount); // 0.1-20 L/min
                        }
                        error = (Math.random() - 0.5) * 0.2; // ±0.1 L/min
                        deviceReading = reference + error;
                        break;
                }

                percentError = (error / reference) * 100;
                measurementData.push({ reference, deviceReading, error, percentError });

                const row = tbody.insertRow();
                const statusColor = Math.abs(error) > 2 ? '#dc3545' : '#28a745';
                row.innerHTML = `
                    <td>${i}</td>
                    <td>${reference.toFixed(2)}</td>
                    <td>${deviceReading.toFixed(2)}</td>
                    <td style="color: ${statusColor};">${error.toFixed(2)}</td>
                    <td style="color: ${statusColor};">${percentError.toFixed(2)}%</td>
                `;
            }

            calculateEnhancedStatistics();
        }

        function calculateEnhancedStatistics() {
            const errors = measurementData.map(d => d.error);
            const absErrors = errors.map(e => Math.abs(e));
            const n = errors.length;

            // Basic statistics
            const meanError = errors.reduce((a, b) => a + b, 0) / n;
            const meanAbsError = absErrors.reduce((a, b) => a + b, 0) / n;
            const variance = errors.reduce((a, b) => a + Math.pow(b - meanError, 2), 0) / (n - 1);
            const stdDev = Math.sqrt(variance);
            const maxError = Math.max(...absErrors);
            const standardError = stdDev / Math.sqrt(n);

            // Calculate coefficient of variation
            const meanReference = measurementData.reduce((a, b) => a + b.reference, 0) / n;
            const coefficientVariation = (stdDev / meanReference) * 100;

            // Calculate accuracy percentage
            const accuracy = 100 - (meanAbsError / meanReference * 100);

            // Calculate confidence intervals (95%)
            const tValue = 2.262; // t-value for 95% CI with df=9 (approximate)
            const ciMeanLower = meanError - (tValue * standardError);
            const ciMeanUpper = meanError + (tValue * standardError);

            // Update primary statistics
            document.getElementById('mean-error').textContent = `±${meanAbsError.toFixed(3)}`;
            document.getElementById('std-deviation').textContent = `±${stdDev.toFixed(3)}`;
            document.getElementById('max-error').textContent = `±${maxError.toFixed(3)}`;
            document.getElementById('accuracy-percent').textContent = `${accuracy.toFixed(1)}%`;
            document.getElementById('standard-error').textContent = `±${standardError.toFixed(3)}`;
            document.getElementById('coefficient-variation').textContent = `${coefficientVariation.toFixed(1)}%`;

            // Update confidence intervals
            document.getElementById('ci-mean-lower').textContent = ciMeanLower.toFixed(3);
            document.getElementById('ci-mean-upper').textContent = ciMeanUpper.toFixed(3);
            document.getElementById('ci-mean-interp').textContent = Math.abs(meanError) < 0.1 ? 'No significant bias' : 'Bias detected';

            const ciStdLower = stdDev * 0.8; // Approximate
            const ciStdUpper = stdDev * 1.3;
            document.getElementById('ci-std-lower').textContent = ciStdLower.toFixed(3);
            document.getElementById('ci-std-upper').textContent = ciStdUpper.toFixed(3);
            document.getElementById('ci-std-interp').textContent = stdDev < 0.5 ? 'Good precision' : 'Poor precision';
        }

        // Troubleshooting Functions
        function loadTroubleshootingScenario() {
            const scenario = document.getElementById('troubleshootingScenario').value;
            const startButton = document.getElementById('start-troubleshooting');

            if (scenario) {
                startButton.disabled = false;
            } else {
                startButton.disabled = true;
            }
        }

        function startTroubleshooting() {
            const scenario = document.getElementById('troubleshootingScenario').value;
            const difficulty = document.getElementById('difficultyLevel').value;
            document.getElementById('troubleshooting-content').style.display = 'block';

            const scenarios = {
                'bp-error': {
                    title: 'Blood Pressure Monitor Error Code E02',
                    details: 'A Philips IntelliVue MP70 patient monitor is displaying error code E02 and cannot obtain blood pressure readings. The device was working normally yesterday but started showing this error this morning.',
                    equipment: [
                        { parameter: 'Model', value: 'Philips IntelliVue MP70', status: 'Identified' },
                        { parameter: 'Serial Number', value: 'DE52301234', status: 'Recorded' },
                        { parameter: 'Software Version', value: 'M.00.03', status: 'Current' },
                        { parameter: 'Last PM Date', value: '2024-11-15', status: 'Due Soon' },
                        { parameter: 'Error Code', value: 'E02', status: 'Active' },
                        { parameter: 'Cuff Size', value: 'Adult (22-32cm)', status: 'Appropriate' }
                    ],
                    symptoms: [
                        { text: 'Error code E02 displayed on screen', severity: 'high' },
                        { text: 'No blood pressure readings obtained', severity: 'high' },
                        { text: 'Cuff inflates but does not deflate properly', severity: 'medium' },
                        { text: 'Intermittent beeping sounds during inflation', severity: 'low' },
                        { text: 'Previous readings were normal', severity: 'info' }
                    ],
                    steps: [
                        {
                            title: 'Visual Inspection',
                            description: 'Perform initial visual inspection of all components',
                            tests: [
                                { test: 'Cuff condition', expected: 'No visible damage', action: 'Replace if damaged' },
                                { test: 'Tubing integrity', expected: 'No kinks or cracks', action: 'Replace if damaged' },
                                { test: 'Connection security', expected: 'Tight connections', action: 'Reconnect properly' }
                            ]
                        },
                        {
                            title: 'Leak Test',
                            description: 'Test pneumatic system for leaks',
                            tests: [
                                { test: 'Cuff inflation hold', expected: 'Maintains pressure >30s', action: 'Identify leak source' },
                                { test: 'Tubing connections', expected: 'No audible leaks', action: 'Tighten or replace' },
                                { test: 'Internal pneumatics', expected: 'System holds pressure', action: 'Service required' }
                            ]
                        },
                        {
                            title: 'Functional Testing',
                            description: 'Test with known good components',
                            tests: [
                                { test: 'Alternative cuff', expected: 'Normal operation', action: 'Replace original cuff' },
                                { test: 'Alternative tubing', expected: 'Normal operation', action: 'Replace original tubing' },
                                { test: 'Calibration check', expected: 'Within specifications', action: 'Recalibrate if needed' }
                            ]
                        }
                    ]
                },
                'ventilator-alarm': {
                    title: 'Ventilator High Pressure Alarm',
                    details: 'A Dräger Evita V500 mechanical ventilator is triggering frequent high pressure alarms during patient ventilation. Peak pressures are reaching 45 cmH2O when the limit is set to 35 cmH2O.',
                    equipment: [
                        { parameter: 'Model', value: 'Dräger Evita V500', status: 'Identified' },
                        { parameter: 'Serial Number', value: 'EV50012345', status: 'Recorded' },
                        { parameter: 'Mode', value: 'Volume Control', status: 'Set' },
                        { parameter: 'Tidal Volume', value: '450 mL', status: 'Set' },
                        { parameter: 'PEEP', value: '5 cmH2O', status: 'Set' },
                        { parameter: 'Pressure Limit', value: '35 cmH2O', status: 'Exceeded' }
                    ],
                    symptoms: [
                        { text: 'High pressure alarm activating every 2-3 breaths', severity: 'high' },
                        { text: 'Peak pressures reaching 45 cmH2O', severity: 'high' },
                        { text: 'Patient appears to be fighting the ventilator', severity: 'medium' },
                        { text: 'Decreased tidal volume delivery (350 mL vs 450 mL set)', severity: 'medium' },
                        { text: 'Increased work of breathing observed', severity: 'medium' }
                    ],
                    steps: [
                        {
                            title: 'Patient Assessment',
                            description: 'Evaluate patient-ventilator interaction',
                            tests: [
                                { test: 'Airway patency', expected: 'Clear airway', action: 'Suction if needed' },
                                { test: 'Chest movement', expected: 'Bilateral expansion', action: 'Check for pneumothorax' },
                                { test: 'Synchrony', expected: 'Patient-ventilator sync', action: 'Adjust settings' }
                            ]
                        },
                        {
                            title: 'Circuit Inspection',
                            description: 'Check ventilator circuit for obstructions',
                            tests: [
                                { test: 'Circuit kinks', expected: 'No obstructions', action: 'Straighten or replace' },
                                { test: 'Water accumulation', expected: 'No water traps', action: 'Drain water traps' },
                                { test: 'Filter condition', expected: 'Clean filters', action: 'Replace if clogged' }
                            ]
                        },
                        {
                            title: 'Pressure Sensor Check',
                            description: 'Verify pressure measurement accuracy',
                            tests: [
                                { test: 'Sensor calibration', expected: 'Within ±2 cmH2O', action: 'Recalibrate sensor' },
                                { test: 'Pressure transducer', expected: 'Proper function', action: 'Replace if faulty' },
                                { test: 'Tubing to sensor', expected: 'No blockages', action: 'Clear or replace' }
                            ]
                        }
                    ]
                }
            };

            const selectedScenario = scenarios[scenario];
            if (selectedScenario) {
                // Update scenario information
                document.getElementById('scenario-title').textContent = selectedScenario.title;
                document.getElementById('scenario-details').textContent = selectedScenario.details;

                // Update equipment information table
                const equipmentBody = document.getElementById('equipment-info-body');
                equipmentBody.innerHTML = '';
                selectedScenario.equipment.forEach(item => {
                    const row = equipmentBody.insertRow();
                    const statusColor = item.status === 'Active' || item.status === 'Exceeded' ? '#dc3545' :
                                       item.status === 'Due Soon' ? '#ffc107' : '#28a745';
                    row.innerHTML = `
                        <td>${item.parameter}</td>
                        <td>${item.value}</td>
                        <td style="color: ${statusColor}; font-weight: bold;">${item.status}</td>
                    `;
                });

                // Update symptoms grid
                const symptomsGrid = document.getElementById('symptoms-grid');
                symptomsGrid.innerHTML = '';
                selectedScenario.symptoms.forEach(symptom => {
                    const severityColors = {
                        'high': '#dc3545',
                        'medium': '#ffc107',
                        'low': '#28a745',
                        'info': '#17a2b8'
                    };

                    const symptomCard = document.createElement('div');
                    symptomCard.className = 'stat-card';
                    symptomCard.innerHTML = `
                        <div class="stat-label" style="color: ${severityColors[symptom.severity]};">
                            ${symptom.severity.toUpperCase()} PRIORITY
                        </div>
                        <div style="font-size: 1em; margin: 10px 0;">${symptom.text}</div>
                    `;
                    symptomsGrid.appendChild(symptomCard);
                });

                // Create diagnostic steps
                const stepsContainer = document.getElementById('troubleshooting-steps');
                stepsContainer.innerHTML = '';
                selectedScenario.steps.forEach((step, stepIndex) => {
                    const stepDiv = document.createElement('div');
                    stepDiv.className = 'calibration-step';
                    stepDiv.id = `troubleshoot-step-${stepIndex + 1}`;

                    let testsHTML = '';
                    step.tests.forEach((test, testIndex) => {
                        testsHTML += `
                            <div style="margin: 10px 0; padding: 10px; background-color: #f8f9fa; border-radius: 4px;">
                                <strong>Test:</strong> ${test.test}<br>
                                <strong>Expected:</strong> ${test.expected}<br>
                                <strong>Action if Failed:</strong> ${test.action}<br>
                                <button onclick="performDiagnosticTest('${scenario}', ${stepIndex}, ${testIndex})"
                                        class="secondary" style="margin-top: 5px;">Perform Test</button>
                                <span id="test-result-${stepIndex}-${testIndex}" style="margin-left: 10px;"></span>
                            </div>
                        `;
                    });

                    stepDiv.innerHTML = `
                        <div class="step-number">${stepIndex + 1}</div>
                        <h5>${step.title}</h5>
                        <p>${step.description}</p>
                        ${testsHTML}
                        <button onclick="completeTroubleshootingStep(${stepIndex + 1})" class="success" disabled>
                            Complete Step ${stepIndex + 1}
                        </button>
                    `;
                    stepsContainer.appendChild(stepDiv);
                });

                // Show test results section
                document.getElementById('test-results-section').style.display = 'block';
            }
        }

        function performDiagnosticTest(scenario, stepIndex, testIndex) {
            // Simulate test results based on scenario and difficulty
            const difficulty = document.getElementById('difficultyLevel').value;
            let testResult, resultColor, actionRequired;

            // Simulate different outcomes based on difficulty
            const passRate = difficulty === 'beginner' ? 0.8 : difficulty === 'intermediate' ? 0.6 : 0.4;
            const testPassed = Math.random() < passRate;

            if (testPassed) {
                testResult = 'PASS';
                resultColor = '#28a745';
                actionRequired = 'Continue to next test';
            } else {
                testResult = 'FAIL';
                resultColor = '#dc3545';
                actionRequired = 'Corrective action required';
            }

            // Update test result display
            const resultSpan = document.getElementById(`test-result-${stepIndex}-${testIndex}`);
            resultSpan.innerHTML = `<strong style="color: ${resultColor};">${testResult}</strong>`;

            // Add to test results table
            const testResultsBody = document.getElementById('test-results-body');
            const row = testResultsBody.insertRow();
            row.innerHTML = `
                <td>Step ${stepIndex + 1} - Test ${testIndex + 1}</td>
                <td>Normal operation</td>
                <td>${testResult}</td>
                <td style="color: ${resultColor}; font-weight: bold;">${testResult}</td>
                <td>${actionRequired}</td>
            `;

            // Check if all tests in step are complete
            checkStepCompletion(stepIndex + 1);
        }

        function checkStepCompletion(stepNumber) {
            const stepElement = document.getElementById(`troubleshoot-step-${stepNumber}`);
            const testButtons = stepElement.querySelectorAll('button.secondary');
            const completedTests = stepElement.querySelectorAll('span[id^="test-result"]');

            let allTestsPerformed = true;
            completedTests.forEach(span => {
                if (span.innerHTML === '') {
                    allTestsPerformed = false;
                }
            });

            if (allTestsPerformed) {
                const completeButton = stepElement.querySelector('button.success');
                completeButton.disabled = false;
            }
        }

        function completeTroubleshootingStep(stepNumber) {
            const steps = document.querySelectorAll('#troubleshooting-steps .calibration-step');
            if (steps[stepNumber - 1]) {
                steps[stepNumber - 1].classList.add('completed');
                steps[stepNumber - 1].querySelector('.step-number').classList.add('completed');
            }

            // Check if all steps completed
            const completedSteps = document.querySelectorAll('#troubleshooting-steps .calibration-step.completed');
            if (completedSteps.length === steps.length) {
                document.getElementById('troubleshooting-results').style.display = 'block';
                document.getElementById('diagnosis-summary').innerHTML = `
                    <h4>Diagnosis Summary</h4>
                    <p>Based on the systematic troubleshooting approach, the most likely cause has been identified and appropriate corrective actions have been determined.</p>
                `;
                document.getElementById('corrective-actions').innerHTML = `
                    <h4>Corrective Actions</h4>
                    <ul>
                        <li>Replace faulty component if identified</li>
                        <li>Recalibrate device if necessary</li>
                        <li>Document findings and actions taken</li>
                        <li>Test device functionality before returning to service</li>
                    </ul>
                `;
                document.getElementById('prevention-measures').innerHTML = `
                    <h4>Prevention Measures</h4>
                    <ul>
                        <li>Implement regular preventive maintenance schedule</li>
                        <li>Train users on proper operation procedures</li>
                        <li>Monitor device performance trends</li>
                        <li>Update maintenance protocols based on findings</li>
                    </ul>
                `;
            }
        }

        // Enhanced Visual Functions
        function updateAlarmStatus(hr, sbp, dbp, spo2, temp) {
            // Heart Rate Alarm
            const hrAlarm = document.getElementById('hr-alarm');
            if (hr > 100 || hr < 60) {
                hrAlarm.textContent = hr > 100 ? 'HR: HIGH' : 'HR: LOW';
                hrAlarm.style.backgroundColor = '#dc3545';
            } else {
                hrAlarm.textContent = 'HR: Normal';
                hrAlarm.style.backgroundColor = '#000';
            }

            // Blood Pressure Alarm
            const bpAlarm = document.getElementById('bp-alarm');
            if (sbp < 90 || sbp > 180 || dbp < 60 || dbp > 110) {
                bpAlarm.textContent = 'BP: ABNORMAL';
                bpAlarm.style.backgroundColor = '#dc3545';
            } else {
                bpAlarm.textContent = 'BP: Normal';
                bpAlarm.style.backgroundColor = '#000';
            }

            // SpO2 Alarm
            const spo2Alarm = document.getElementById('spo2-alarm');
            if (spo2 < 92) {
                spo2Alarm.textContent = 'SpO2: LOW';
                spo2Alarm.style.backgroundColor = '#dc3545';
            } else {
                spo2Alarm.textContent = 'SpO2: Normal';
                spo2Alarm.style.backgroundColor = '#000';
            }

            // Temperature Alarm
            const tempAlarm = document.getElementById('temp-alarm');
            if (temp > 38.0 || temp < 36.0) {
                tempAlarm.textContent = temp > 38.0 ? 'Temp: HIGH' : 'Temp: LOW';
                tempAlarm.style.backgroundColor = '#dc3545';
            } else {
                tempAlarm.textContent = 'Temp: Normal';
                tempAlarm.style.backgroundColor = '#000';
            }
        }

        let vitalsTrendData = [];
        function updateVitalsTrend(hr, sbp, dbp, spo2, temp) {
            const now = new Date();
            const timeStr = now.toLocaleTimeString();

            // Add new data point
            vitalsTrendData.push({
                time: timeStr,
                hr: hr,
                sbp: sbp,
                dbp: dbp,
                spo2: spo2,
                temp: temp
            });

            // Keep only last 10 readings
            if (vitalsTrendData.length > 10) {
                vitalsTrendData.shift();
            }

            // Update table
            const tbody = document.getElementById('vitals-trend-body');
            if (tbody) {
                tbody.innerHTML = '';
                vitalsTrendData.forEach(data => {
                    const row = tbody.insertRow();
                    const status = getVitalStatus(data.hr, data.sbp, data.dbp, data.spo2, data.temp);
                    row.innerHTML = `
                        <td>${data.time}</td>
                        <td>${data.hr}</td>
                        <td>${data.sbp}</td>
                        <td>${data.dbp}</td>
                        <td>${data.spo2}</td>
                        <td>${data.temp}</td>
                        <td style="color: ${status.color};">${status.text}</td>
                    `;
                });
            }
        }

        function getVitalStatus(hr, sbp, dbp, spo2, temp) {
            if (hr > 100 || hr < 60 || sbp < 90 || sbp > 180 || spo2 < 92 || temp > 38.0 || temp < 36.0) {
                return { text: 'ABNORMAL', color: '#dc3545' };
            }
            return { text: 'NORMAL', color: '#28a745' };
        }

        function drawECGWaveform(heartRate) {
            const canvas = document.getElementById('ecgCanvas');
            if (!canvas) return;

            const ctx = canvas.getContext('2d');
            const width = canvas.width;
            const height = canvas.height;

            // Clear canvas
            ctx.fillStyle = '#000';
            ctx.fillRect(0, 0, width, height);

            // Draw grid
            ctx.strokeStyle = '#003300';
            ctx.lineWidth = 1;
            for (let x = 0; x < width; x += 20) {
                ctx.beginPath();
                ctx.moveTo(x, 0);
                ctx.lineTo(x, height);
                ctx.stroke();
            }
            for (let y = 0; y < height; y += 20) {
                ctx.beginPath();
                ctx.moveTo(0, y);
                ctx.lineTo(width, y);
                ctx.stroke();
            }

            // Draw ECG waveform
            ctx.strokeStyle = '#00ff00';
            ctx.lineWidth = 2;
            ctx.beginPath();

            const baselineY = height / 2;
            const amplitude = 40;
            const frequency = heartRate / 60; // Hz
            const samplesPerBeat = width / (frequency * 5); // 5 seconds of data

            for (let x = 0; x < width; x++) {
                const t = (x / width) * 5; // 5 seconds
                const beatPhase = (t * frequency) % 1;

                let y = baselineY;
                if (beatPhase < 0.1) {
                    // P wave
                    y = baselineY - amplitude * 0.2 * Math.sin(beatPhase * Math.PI / 0.1);
                } else if (beatPhase < 0.2) {
                    // PR segment
                    y = baselineY;
                } else if (beatPhase < 0.3) {
                    // QRS complex
                    const qrsPhase = (beatPhase - 0.2) / 0.1;
                    if (qrsPhase < 0.3) {
                        y = baselineY + amplitude * 0.3 * Math.sin(qrsPhase * Math.PI / 0.3);
                    } else if (qrsPhase < 0.7) {
                        y = baselineY - amplitude * Math.sin((qrsPhase - 0.3) * Math.PI / 0.4);
                    } else {
                        y = baselineY + amplitude * 0.4 * Math.sin((qrsPhase - 0.7) * Math.PI / 0.3);
                    }
                } else if (beatPhase < 0.5) {
                    // ST segment
                    y = baselineY;
                } else if (beatPhase < 0.7) {
                    // T wave
                    y = baselineY - amplitude * 0.3 * Math.sin((beatPhase - 0.5) * Math.PI / 0.2);
                }

                if (x === 0) {
                    ctx.moveTo(x, y);
                } else {
                    ctx.lineTo(x, y);
                }
            }
            ctx.stroke();
        }

        // Enhanced Calibration Functions
        function setPressureReference(value) {
            document.getElementById('pressure-reference').textContent = value.toFixed(1);
            document.getElementById('calibrator-display').textContent = `${value.toFixed(1)} mmHg`;

            // Simulate device reading with some error
            const error = (Math.random() - 0.5) * 4; // ±2 mmHg random error
            const deviceReading = value + error;
            const percentError = value !== 0 ? (error / value) * 100 : 0;

            document.getElementById('pressure-device').textContent = deviceReading.toFixed(1);
            document.getElementById('pressure-error').textContent = error.toFixed(1);
            document.getElementById('pressure-percent-error').textContent = percentError.toFixed(1);

            // Update error status
            const errorStatus = document.getElementById('error-status');
            if (Math.abs(error) <= 3) {
                errorStatus.textContent = 'Within Tolerance';
                errorStatus.style.color = '#28a745';
            } else {
                errorStatus.textContent = 'Out of Tolerance';
                errorStatus.style.color = '#dc3545';
            }

            // Store calibration data
            if (!calibrationData.pressure) calibrationData.pressure = {};
            calibrationData.pressure[value] = {
                reference: value,
                device: deviceReading,
                error: error,
                percentError: percentError
            };

            // Enable completion button for current step
            if (value === 0) {
                document.querySelector('#pressure-step-2 button').disabled = false;
            } else if (value === 100) {
                document.querySelector('#pressure-step-3 button').disabled = false;
            } else if (value === 200) {
                document.querySelector('#pressure-step-4 button').disabled = false;
            }
        }

        // Enhanced Uncertainty Calculation
        function calculateUncertainty() {
            const measurementType = document.getElementById('uncertaintyType').value;
            const confidenceLevel = parseFloat(document.getElementById('confidenceLevel').value);
            const numMeasurements = parseInt(document.getElementById('uncertaintyMeasurements').value);

            // Simulate uncertainty components based on measurement type
            let typeA, typeBCal, typeBRes, typeBEnv, typeBDrift;

            switch (measurementType) {
                case 'pressure':
                    typeA = 0.5 / Math.sqrt(numMeasurements);
                    typeBCal = 0.3 / 2; // Normal distribution, k=2
                    typeBRes = 0.1 / Math.sqrt(3); // Rectangular distribution
                    typeBEnv = 0.2 / Math.sqrt(3);
                    typeBDrift = 0.1 / Math.sqrt(3);
                    break;
                case 'temperature':
                    typeA = 0.1 / Math.sqrt(numMeasurements);
                    typeBCal = 0.05 / 2;
                    typeBRes = 0.01 / Math.sqrt(3);
                    typeBEnv = 0.05 / Math.sqrt(3);
                    typeBDrift = 0.02 / Math.sqrt(3);
                    break;
                default:
                    typeA = 0.3 / Math.sqrt(numMeasurements);
                    typeBCal = 0.2 / 2;
                    typeBRes = 0.05 / Math.sqrt(3);
                    typeBEnv = 0.1 / Math.sqrt(3);
                    typeBDrift = 0.05 / Math.sqrt(3);
            }

            // Calculate combined standard uncertainty
            const combined = Math.sqrt(typeA*typeA + typeBCal*typeBCal + typeBRes*typeBRes + typeBEnv*typeBEnv + typeBDrift*typeBDrift);

            // Calculate effective degrees of freedom (Welch-Satterthwaite)
            const effectiveDOF = Math.round(Math.pow(combined, 4) / (Math.pow(typeA, 4) / (numMeasurements - 1)));

            // Determine coverage factor based on confidence level and DOF
            let coverageFactor;
            if (confidenceLevel === 68.27) coverageFactor = 1.0;
            else if (confidenceLevel === 95.45) coverageFactor = effectiveDOF > 30 ? 2.0 : 2.18;
            else if (confidenceLevel === 99.73) coverageFactor = effectiveDOF > 30 ? 3.0 : 3.18;

            const expanded = combined * coverageFactor;

            // Update displays
            document.getElementById('combined-uncertainty').textContent = `±${combined.toFixed(3)}`;
            document.getElementById('effective-dof').textContent = effectiveDOF;
            document.getElementById('coverage-factor').textContent = coverageFactor.toFixed(2);
            document.getElementById('expanded-uncertainty').textContent = `±${expanded.toFixed(3)}`;

            // Update uncertainty budget table
            updateUncertaintyBudget(typeA, typeBCal, typeBRes, typeBEnv, typeBDrift, combined);
        }

        function updateUncertaintyBudget(typeA, typeBCal, typeBRes, typeBEnv, typeBDrift, combined) {
            const totalVariance = typeA*typeA + typeBCal*typeBCal + typeBRes*typeBRes + typeBEnv*typeBEnv + typeBDrift*typeBDrift;

            // Calculate contributions
            const contribA = (typeA*typeA / totalVariance * 100).toFixed(1);
            const contribCal = (typeBCal*typeBCal / totalVariance * 100).toFixed(1);
            const contribRes = (typeBRes*typeBRes / totalVariance * 100).toFixed(1);
            const contribEnv = (typeBEnv*typeBEnv / totalVariance * 100).toFixed(1);
            const contribDrift = (typeBDrift*typeBDrift / totalVariance * 100).toFixed(1);

            // Update table values
            document.getElementById('repeatability-std').textContent = `±${typeA.toFixed(3)}`;
            document.getElementById('repeatability-contrib').textContent = `${contribA}%`;
            document.getElementById('calibration-std').textContent = `±${typeBCal.toFixed(3)}`;
            document.getElementById('calibration-contrib').textContent = `${contribCal}%`;
            document.getElementById('resolution-std').textContent = `±${typeBRes.toFixed(3)}`;
            document.getElementById('resolution-contrib').textContent = `${contribRes}%`;
            document.getElementById('environmental-std').textContent = `±${typeBEnv.toFixed(3)}`;
            document.getElementById('environmental-contrib').textContent = `${contribEnv}%`;
            document.getElementById('drift-std').textContent = `±${typeBDrift.toFixed(3)}`;
            document.getElementById('drift-contrib').textContent = `${contribDrift}%`;
        }

        function generateTrendData() {
            document.getElementById('drift-rate').textContent = '+0.02 units/month';
            document.getElementById('predicted-oot').textContent = '18 months';
        }

        function analyzeTrend() {
            alert('Trend analysis complete. Device showing gradual positive drift within acceptable limits.');
        }

        function predictMaintenance() {
            alert('Predictive maintenance analysis suggests calibration due in 18 months based on current drift rate.');
        }

        // Additional Enhanced Functions
        function generateUncertaintyBudget() {
            alert('Uncertainty budget generated. This would create a comprehensive uncertainty analysis document with all components and calculations.');
        }

        function exportData() {
            if (measurementData.length === 0) {
                alert('No measurement data to export. Please run an accuracy test first.');
                return;
            }

            // Simulate data export
            let csvContent = "Measurement,Reference,Device Reading,Error,Percent Error\n";
            measurementData.forEach((data, index) => {
                csvContent += `${index + 1},${data.reference.toFixed(3)},${data.deviceReading.toFixed(3)},${data.error.toFixed(3)},${data.percentError.toFixed(3)}\n`;
            });

            alert('Data exported to CSV format. In a real system, this would download the file.');
        }

        function showTroubleshootingGuide() {
            alert('Troubleshooting Guide:\n\n1. Always start with visual inspection\n2. Follow systematic diagnostic procedures\n3. Document all findings\n4. Verify repairs before returning to service\n5. Update maintenance records');
        }

        function generateAccuracyReport() {
            if (measurementData.length === 0) {
                alert('Please run an accuracy test first.');
                return;
            }

            // Simulate comprehensive report generation
            const device = document.getElementById('accuracyDevice').value;
            const deviceNames = {
                'bp': 'Blood Pressure Monitor',
                'temp': 'Temperature Monitor',
                'spo2': 'Pulse Oximeter',
                'flow': 'Flow Meter'
            };

            alert(`Accuracy Report Generated for ${deviceNames[device]}\n\nReport includes:\n• Statistical analysis\n• Measurement data table\n• Error distribution charts\n• Bland-Altman analysis\n• Compliance assessment\n• Recommendations\n\nIn a real system, this would generate a comprehensive PDF report.`);
        }

        // Initial display setup
        document.addEventListener('DOMContentLoaded', () => {
            // Show overview section by default
            showSection('overview');

            // Initialize device simulation if elements exist
            if (document.getElementById('deviceSelector')) {
                switchDevice();
            }
        });
    </script>
</body>
</html>