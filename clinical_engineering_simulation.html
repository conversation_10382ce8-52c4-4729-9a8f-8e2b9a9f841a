<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clinical Engineering Interactive Simulations</title>
    <style>
        /* Base Styles */
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
            background-color: #f5f7fa;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        /* Header Styles */
        header {
            background: linear-gradient(135deg, #0056b3 0%, #00a0e9 100%);
            color: white;
            padding: 40px 0;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .header-content {
            max-width: 800px;
            margin: 0 auto;
        }

        h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        h2 {
            font-size: 1.8rem;
            margin-top: 30px;
            color: #0056b3;
            border-bottom: 2px solid #e1e4e8;
            padding-bottom: 10px;
        }

        h3 {
            font-size: 1.4rem;
            color: #0056b3;
            margin-top: 25px;
        }

        /* Navigation */
        .nav-bar {
            background-color: #343a40;
            padding: 15px 0;
        }

        .nav-content {
            max-width: 1400px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
        }

        .nav-links {
            display: flex;
            gap: 30px;
        }

        .nav-links a {
            color: white;
            text-decoration: none;
            padding: 10px 15px;
            border-radius: 4px;
            transition: background-color 0.3s;
        }

        .nav-links a:hover, .nav-links a.active {
            background-color: #0056b3;
        }

        /* Content Sections */
        .section {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            padding: 25px;
            margin-bottom: 30px;
        }

        /* Module Objectives */
        .objectives {
            background-color: #e8f4fd;
            border-left: 4px solid #0056b3;
            padding: 20px;
            margin: 20px 0;
            border-radius: 0 4px 4px 0;
        }

        /* Simulation Area */
        #simulation-area {
            border: 2px solid #0056b3;
            padding: 25px;
            min-height: 400px;
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        /* Simulation Controls */
        .control-panel {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            align-items: center;
            border: 1px solid #dee2e6;
        }

        .control-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .control-group label {
            font-weight: bold;
            color: #495057;
            font-size: 0.9rem;
        }

        /* Vital Signs Display */
        .vital-sign {
            font-size: 1.2em;
            margin-bottom: 8px;
            padding: 15px;
            background-color: #f8f9fa;
            border-left: 5px solid #0056b3;
            border-radius: 4px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: all 0.3s ease;
        }

        .vital-sign:hover {
            background-color: #e9ecef;
            transform: translateX(5px);
        }

        .vital-sign strong {
            color: #0056b3;
            font-weight: 600;
        }

        .vital-value {
            font-weight: bold;
            color: #28a745;
            font-size: 1.1em;
        }

        .vital-value.abnormal {
            color: #dc3545;
            animation: pulse 1s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }

        /* Device Displays */
        .device-display {
            display: none;
        }

        .device-display.active {
            display: block;
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Calibration Section */
        .calibration-panel {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }

        .calibration-step {
            background-color: white;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 15px;
            margin: 10px 0;
            position: relative;
        }

        .calibration-step.active {
            border-color: #0056b3;
            background-color: #f8f9ff;
        }

        .calibration-step.completed {
            border-color: #28a745;
            background-color: #f8fff9;
        }

        .step-number {
            position: absolute;
            top: -10px;
            left: 15px;
            background-color: #0056b3;
            color: white;
            width: 25px;
            height: 25px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .step-number.completed {
            background-color: #28a745;
        }
        /* Button Styles */
        button {
            padding: 12px 24px;
            margin: 5px;
            cursor: pointer;
            background-color: #0056b3;
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 1em;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        button:hover {
            background-color: #003d82;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }

        button:active {
            background-color: #002952;
            transform: translateY(0);
        }

        button.secondary {
            background-color: #6c757d;
        }

        button.secondary:hover {
            background-color: #545b62;
        }

        button.success {
            background-color: #28a745;
        }

        button.success:hover {
            background-color: #1e7e34;
        }

        button.warning {
            background-color: #ffc107;
            color: #212529;
        }

        button.warning:hover {
            background-color: #e0a800;
        }

        button.danger {
            background-color: #dc3545;
        }

        button.danger:hover {
            background-color: #c82333;
        }

        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
            opacity: 0.6;
        }

        button:disabled:hover {
            transform: none;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        /* Device Status */
        #device-status {
            margin-top: 20px;
            padding: 20px;
            border-radius: 8px;
            font-weight: bold;
            text-align: center;
            font-size: 1.1em;
            transition: all 0.3s ease;
        }

        .status-running {
            background-color: #d4edda;
            color: #155724;
            border: 2px solid #28a745;
            animation: statusPulse 2s infinite;
        }

        .status-stopped {
            background-color: #f8d7da;
            color: #721c24;
            border: 2px solid #dc3545;
        }

        .status-calibrating {
            background-color: #fff3cd;
            color: #856404;
            border: 2px solid #ffc107;
            animation: statusPulse 1s infinite;
        }

        @keyframes statusPulse {
            0% { opacity: 1; }
            50% { opacity: 0.8; }
            100% { opacity: 1; }
        }

        /* Form Elements */
        select, input[type="number"], input[type="text"] {
            padding: 10px;
            border: 2px solid #dee2e6;
            border-radius: 6px;
            font-size: 1em;
            transition: border-color 0.3s ease;
            background-color: white;
        }

        select:focus, input:focus {
            outline: none;
            border-color: #0056b3;
            box-shadow: 0 0 0 3px rgba(0, 86, 179, 0.1);
        }

        .device-selection {
            min-width: 200px;
        }

        .scenario-selection {
            min-width: 150px;
        }

        /* Settings and Scenario Containers */
        .scenario-container,
        .settings-container {
            margin-bottom: 20px;
            padding: 20px;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            background-color: #f8f9fa;
        }

        .settings-container label {
            display: inline-block;
            margin-right: 15px;
            margin-bottom: 10px;
            font-weight: 600;
            color: #495057;
        }

        .settings-container input[type="number"],
        .settings-container select {
            margin-right: 20px;
            margin-bottom: 10px;
        }

        /* Measurement Display */
        .measurement-display {
            background-color: #000;
            color: #00ff00;
            font-family: 'Courier New', monospace;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            font-size: 1.2em;
            text-align: center;
            border: 2px solid #333;
        }

        .measurement-value {
            font-size: 2em;
            font-weight: bold;
            margin: 10px 0;
        }

        /* Progress Indicators */
        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background-color: #0056b3;
            transition: width 0.3s ease;
            border-radius: 10px;
        }

        /* Tabs */
        .tab-container {
            margin-bottom: 20px;
        }

        .tab-buttons {
            display: flex;
            border-bottom: 2px solid #dee2e6;
            margin-bottom: 20px;
        }

        .tab-button {
            padding: 15px 25px;
            background-color: transparent;
            border: none;
            border-bottom: 3px solid transparent;
            cursor: pointer;
            font-size: 1em;
            font-weight: 500;
            color: #6c757d;
            transition: all 0.3s ease;
        }

        .tab-button.active {
            color: #0056b3;
            border-bottom-color: #0056b3;
            background-color: #f8f9fa;
        }

        .tab-button:hover {
            color: #0056b3;
            background-color: #f8f9fa;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
            animation: fadeIn 0.3s ease-in;
        }

        /* Footer */
        footer {
            background-color: #343a40;
            color: white;
            text-align: center;
            padding: 20px 0;
            margin-top: 40px;
        }
    </style>
</head>
<body>
    <header>
        <div class="header-content">
            <h1>Clinical Engineering Interactive Simulations</h1>
            <p>Hands-on learning for medical device operation, measurement, and calibration</p>
        </div>
    </header>

    <nav class="nav-bar">
        <div class="nav-content">
            <div class="nav-links">
                <a href="#overview" class="nav-link active" onclick="showSection('overview')">Module Overview</a>
                <a href="#device-simulation" class="nav-link" onclick="showSection('device-simulation')">Device Simulation</a>
                <a href="#calibration" class="nav-link" onclick="showSection('calibration')">Calibration Lab</a>
                <a href="#measurements" class="nav-link" onclick="showSection('measurements')">Measurements</a>
                <a href="#troubleshooting" class="nav-link" onclick="showSection('troubleshooting')">Troubleshooting</a>
            </div>
            <a href="index.html" style="color: white; text-decoration: none;">← Back to Course</a>
        </div>
    </nav>

    <div class="container">
        <!-- Module Overview Section -->
        <div id="overview" class="section active-section">
            <h2>Interactive Simulations Module</h2>

            <div class="objectives">
                <h3>Learning Objectives</h3>
                <p>Upon completion of this interactive simulation module, students will be able to:</p>
                <ul>
                    <li><strong>Operate</strong> various medical devices safely and effectively</li>
                    <li><strong>Perform</strong> routine calibration procedures on biomedical equipment</li>
                    <li><strong>Conduct</strong> accurate measurements using clinical engineering instruments</li>
                    <li><strong>Troubleshoot</strong> common device malfunctions and performance issues</li>
                    <li><strong>Document</strong> calibration results and maintenance activities</li>
                    <li><strong>Apply</strong> quality assurance principles in clinical engineering practice</li>
                    <li><strong>Interpret</strong> device performance data and identify anomalies</li>
                    <li><strong>Implement</strong> preventive maintenance protocols</li>
                </ul>
            </div>

            <h3>Simulation Modules Available</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 20px 0;">
                <div class="section" style="margin: 0;">
                    <h4>🏥 Device Operation Simulator</h4>
                    <p>Interactive simulation of patient monitors, ECG machines, ventilators, and other critical care equipment.</p>
                    <ul>
                        <li>Patient Monitor Systems</li>
                        <li>ECG/EKG Monitors</li>
                        <li>Respiratory Monitors</li>
                        <li>Hemodynamic Monitors</li>
                        <li>Neurological Monitors</li>
                    </ul>
                </div>

                <div class="section" style="margin: 0;">
                    <h4>⚙️ Calibration Laboratory</h4>
                    <p>Step-by-step calibration procedures for biomedical equipment with virtual instruments.</p>
                    <ul>
                        <li>Pressure Calibration</li>
                        <li>Temperature Calibration</li>
                        <li>Flow Calibration</li>
                        <li>Electrical Safety Testing</li>
                        <li>Performance Verification</li>
                    </ul>
                </div>

                <div class="section" style="margin: 0;">
                    <h4>📊 Measurement Systems</h4>
                    <p>Hands-on experience with clinical engineering measurement techniques and data analysis.</p>
                    <ul>
                        <li>Vital Signs Measurement</li>
                        <li>Signal Analysis</li>
                        <li>Accuracy Assessment</li>
                        <li>Uncertainty Calculation</li>
                        <li>Data Documentation</li>
                    </ul>
                </div>

                <div class="section" style="margin: 0;">
                    <h4>🔧 Troubleshooting Scenarios</h4>
                    <p>Real-world problem-solving scenarios to develop diagnostic and repair skills.</p>
                    <ul>
                        <li>Fault Diagnosis</li>
                        <li>Performance Issues</li>
                        <li>Safety Concerns</li>
                        <li>User Error Analysis</li>
                        <li>Corrective Actions</li>
                    </ul>
                </div>
            </div>

            <h3>How to Use This Module</h3>
            <ol>
                <li><strong>Start with Device Simulation:</strong> Familiarize yourself with equipment operation</li>
                <li><strong>Practice Calibration:</strong> Learn systematic calibration procedures</li>
                <li><strong>Master Measurements:</strong> Develop precision measurement skills</li>
                <li><strong>Apply Troubleshooting:</strong> Solve realistic equipment problems</li>
                <li><strong>Document Everything:</strong> Practice proper record-keeping</li>
            </ol>
        </div>

        <!-- Device Simulation Section -->
        <div id="device-simulation" class="section" style="display: none;">
            <h2>Medical Device Simulation</h2>
            <p>Interactive simulation of medical devices commonly found in clinical settings. Practice device operation, parameter adjustment, and data interpretation.</p>

            <div class="control-panel">
                <div class="control-group">
                    <label>Device Type:</label>
                    <select id="deviceSelector" class="device-selection" onchange="switchDevice()">
                        <option value="patientMonitor">Patient Monitor</option>
                        <option value="ecgMonitor">ECG Monitor</option>
                        <option value="respiratoryMonitor">Respiratory Monitor</option>
                        <option value="hemodynamicMonitor">Hemodynamic Monitor</option>
                        <option value="neurologicalMonitor">Neurological Monitor</option>
                    </select>
                </div>

                <div class="control-group">
                    <label>Controls:</label>
                    <button onclick="startSimulation()">Start Device</button>
                    <button onclick="stopSimulation()" class="secondary">Stop Device</button>
                    <button onclick="resetSimulation()" class="warning">Reset</button>
                </div>

                <div class="control-group">
                    <label>Simulation Speed:</label>
                    <select id="simulationSpeed" onchange="updateSimulationSpeed()">
                        <option value="1000">Slow (1s)</option>
                        <option value="2000" selected>Normal (2s)</option>
                        <option value="500">Fast (0.5s)</option>
                    </select>
                </div>
            </div>

            <div id="simulation-area">
        <p>Device simulation ready. Select a device and click "Start Device" to begin monitoring.</p>
        <div id="patientMonitorDisplay" class="device-display active">
            <div class="scenario-container">
                <strong>Patient Scenario:</strong>
                <select id="patientScenario" class="scenario-selection" onchange="updateVitalSigns()">
                    <option value="normal">Normal</option>
                    <option value="tachycardia">Tachycardia</option>
                    <option value="hypotension">Hypotension</option>
                    <option value="hypoxemia">Hypoxemia</option>
                </select>
            </div>
            <div class="vital-sign">
                <strong>Heart Rate:</strong> <span id="heartRate" class="vital-value">-- bpm</span>
            </div>
            <div class="vital-sign">
                <strong>Blood Pressure:</strong> <span id="bloodPressure" class="vital-value">--/-- mmHg</span>
            </div>
            <div class="vital-sign">
                <strong>Oxygen Saturation:</strong> <span id="oxygenSaturation" class="vital-value">-- %</span>
            </div>
        </div>

        <div id="ecgMonitorDisplay" class="device-display">
            <div class="vital-sign">
                <strong>Heart Rhythm:</strong> <span id="heartRhythm" class="vital-value">--</span>
            </div>
            <div class="vital-sign">
                <strong>QRS Duration:</strong> <span id="qrsDuration" class="vital-value">-- ms</span>
            </div>
        </div>

        <div id="respiratoryMonitorDisplay" class="device-display">
            <div class="settings-container">
                <strong>Ventilator Settings:</strong><br>
                <label for="ventilationMode">Mode:</label>
                <select id="ventilationMode" onchange="updateVitalSigns()">
                    <option value="AC">Assist-Control (AC)</option>
                    <option value="SIMV">Synchronized Intermittent Mandatory Ventilation (SIMV)</option>
                    <option value="CPAP">Continuous Positive Airway Pressure (CPAP)</option>
                </select>
                <label for="fio2">FiO2 (%):</label>
                <input type="number" id="fio2" value="21" min="21" max="100" step="1" onchange="updateVitalSigns()">
            </div>
            <div class="vital-sign">
                <strong>Tidal Volume:</strong> <span id="tidalVolume" class="vital-value">-- mL</span>
            </div>
            <div class="vital-sign">
                <strong>Respiratory Rate:</strong> <span id="respiratoryRate" class="vital-value">-- bpm</span>
            </div>
            <div class="vital-sign">
                <strong>Oxygen Saturation:</strong> <span id="respOxygenSaturation" class="vital-value">-- %</span>
            </div>
            <div class="vital-sign">
                <strong>End-tidal CO2:</strong> <span id="etCO2" class="vital-value">-- mmHg</span>
            </div>
        </div>

        <div id="hemodynamicMonitorDisplay" class="device-display">
            <div class="vital-sign">
                <strong>CVP:</strong> <span id="cvp" class="vital-value">-- mmHg</span>
            </div>
            <div class="vital-sign">
                <strong>PAP:</strong> <span id="pap" class="vital-value">--/-- mmHg</span>
            </div>
            <div class="vital-sign">
                <strong>Cardiac Output:</strong> <span id="cardiacOutput" class="vital-value">-- L/min</span>
            </div>
            <div class="vital-sign">
                <strong>SVR:</strong> <span id="svr" class="vital-value">-- dynes·s/cm⁵</span>
            </div>
        </div>

        <div id="neurologicalMonitorDisplay" class="device-display">
            <div class="vital-sign">
                <strong>ICP:</strong> <span id="icp" class="vital-value">-- mmHg</span>
            </div>
            <div class="vital-sign">
                <strong>CPP:</strong> <span id="cpp" class="vital-value">-- mmHg</span>
            </div>
            <div class="vital-sign">
                <strong>GCS:</strong> <span id="gcs" class="vital-value">--</span>
            </div>
        </div>

                <div id="device-status" class="status-stopped">Device Status: Stopped</div>
            </div>
        </div>

        <!-- Calibration Laboratory Section -->
        <div id="calibration" class="section" style="display: none;">
            <h2>Calibration Laboratory</h2>
            <p>Learn systematic calibration procedures for biomedical equipment. Follow step-by-step protocols to ensure device accuracy and compliance.</p>

            <div class="tab-container">
                <div class="tab-buttons">
                    <button class="tab-button active" onclick="showCalibrationTab('pressure')">Pressure Calibration</button>
                    <button class="tab-button" onclick="showCalibrationTab('temperature')">Temperature Calibration</button>
                    <button class="tab-button" onclick="showCalibrationTab('electrical')">Electrical Safety</button>
                    <button class="tab-button" onclick="showCalibrationTab('flow')">Flow Calibration</button>
                </div>

                <!-- Pressure Calibration Tab -->
                <div id="pressure-tab" class="tab-content active">
                    <h3>Blood Pressure Monitor Calibration</h3>
                    <p>Calibrate a non-invasive blood pressure monitor using a pressure calibrator.</p>

                    <div class="calibration-panel">
                        <h4>Equipment Required:</h4>
                        <ul>
                            <li>Blood Pressure Monitor (Device Under Test)</li>
                            <li>Pressure Calibrator (Reference Standard)</li>
                            <li>Pressure Cuff and Tubing</li>
                            <li>Calibration Documentation Forms</li>
                        </ul>

                        <h4>Calibration Procedure:</h4>
                        <div class="calibration-step active" id="pressure-step-1">
                            <div class="step-number">1</div>
                            <h5>Pre-Calibration Setup</h5>
                            <p>Connect the pressure calibrator to the blood pressure monitor. Ensure all connections are secure and leak-free.</p>
                            <button onclick="completeCalibrationStep('pressure', 1)" class="success">Complete Step 1</button>
                        </div>

                        <div class="calibration-step" id="pressure-step-2">
                            <div class="step-number">2</div>
                            <h5>Zero Point Verification</h5>
                            <p>Set the pressure calibrator to 0 mmHg. Verify that the blood pressure monitor reads 0 ± 2 mmHg.</p>
                            <div class="measurement-display">
                                <div>Reference: <span id="pressure-reference">0</span> mmHg</div>
                                <div>Device Reading: <span id="pressure-device" class="measurement-value">0</span> mmHg</div>
                                <div>Error: <span id="pressure-error">0</span> mmHg</div>
                            </div>
                            <button onclick="completeCalibrationStep('pressure', 2)" class="success" disabled>Complete Step 2</button>
                        </div>

                        <div class="calibration-step" id="pressure-step-3">
                            <div class="step-number">3</div>
                            <h5>Mid-Range Calibration</h5>
                            <p>Set calibrator to 100 mmHg. Record device reading and calculate error.</p>
                            <button onclick="setPressureReference(100)">Set 100 mmHg</button>
                            <button onclick="completeCalibrationStep('pressure', 3)" class="success" disabled>Complete Step 3</button>
                        </div>

                        <div class="calibration-step" id="pressure-step-4">
                            <div class="step-number">4</div>
                            <h5>High-Range Calibration</h5>
                            <p>Set calibrator to 200 mmHg. Record device reading and calculate error.</p>
                            <button onclick="setPressureReference(200)">Set 200 mmHg</button>
                            <button onclick="completeCalibrationStep('pressure', 4)" class="success" disabled>Complete Step 4</button>
                        </div>

                        <div class="calibration-step" id="pressure-step-5">
                            <div class="step-number">5</div>
                            <h5>Documentation</h5>
                            <p>Record all measurements and determine if the device passes calibration criteria (±3 mmHg or ±2% of reading).</p>
                            <div id="pressure-results" style="display: none;">
                                <h5>Calibration Results:</h5>
                                <table style="width: 100%; border-collapse: collapse; margin: 10px 0;">
                                    <tr style="background-color: #f8f9fa;">
                                        <th style="border: 1px solid #dee2e6; padding: 8px;">Reference (mmHg)</th>
                                        <th style="border: 1px solid #dee2e6; padding: 8px;">Device Reading (mmHg)</th>
                                        <th style="border: 1px solid #dee2e6; padding: 8px;">Error (mmHg)</th>
                                        <th style="border: 1px solid #dee2e6; padding: 8px;">Status</th>
                                    </tr>
                                    <tr id="pressure-result-0"></tr>
                                    <tr id="pressure-result-100"></tr>
                                    <tr id="pressure-result-200"></tr>
                                </table>
                                <div id="pressure-pass-fail"></div>
                            </div>
                            <button onclick="completeCalibrationStep('pressure', 5)" class="success" disabled>Complete Calibration</button>
                        </div>
                    </div>
                </div>

                <!-- Temperature Calibration Tab -->
                <div id="temperature-tab" class="tab-content">
                    <h3>Temperature Probe Calibration</h3>
                    <p>Calibrate temperature probes using a temperature calibrator with ice point and body temperature references.</p>

                    <div class="calibration-panel">
                        <h4>Equipment Required:</h4>
                        <ul>
                            <li>Temperature Monitor (Device Under Test)</li>
                            <li>Temperature Calibrator/Bath</li>
                            <li>Reference Thermometer</li>
                            <li>Temperature Probes</li>
                        </ul>

                        <h4>Calibration Points:</h4>
                        <div class="control-panel">
                            <button onclick="setTemperatureReference(0)">Ice Point (0°C)</button>
                            <button onclick="setTemperatureReference(37)">Body Temp (37°C)</button>
                            <button onclick="setTemperatureReference(42)">Hyperthermia (42°C)</button>
                        </div>

                        <div class="measurement-display">
                            <div>Reference: <span id="temp-reference">--</span> °C</div>
                            <div>Device Reading: <span id="temp-device" class="measurement-value">--</span> °C</div>
                            <div>Error: <span id="temp-error">--</span> °C</div>
                        </div>
                    </div>
                </div>

                <!-- Electrical Safety Tab -->
                <div id="electrical-tab" class="tab-content">
                    <h3>Electrical Safety Testing</h3>
                    <p>Perform electrical safety tests including leakage current, ground resistance, and insulation resistance.</p>

                    <div class="calibration-panel">
                        <h4>Safety Tests:</h4>
                        <div class="control-panel">
                            <button onclick="performSafetyTest('leakage')">Leakage Current Test</button>
                            <button onclick="performSafetyTest('ground')">Ground Resistance Test</button>
                            <button onclick="performSafetyTest('insulation')">Insulation Resistance Test</button>
                        </div>

                        <div id="safety-results">
                            <div class="measurement-display">
                                <div>Test Type: <span id="safety-test-type">--</span></div>
                                <div>Measured Value: <span id="safety-value" class="measurement-value">--</span></div>
                                <div>Limit: <span id="safety-limit">--</span></div>
                                <div>Status: <span id="safety-status">--</span></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Flow Calibration Tab -->
                <div id="flow-tab" class="tab-content">
                    <h3>Flow Meter Calibration</h3>
                    <p>Calibrate flow meters used in respiratory equipment and infusion pumps.</p>

                    <div class="calibration-panel">
                        <h4>Flow Calibration Points:</h4>
                        <div class="control-panel">
                            <button onclick="setFlowReference(1)">1 L/min</button>
                            <button onclick="setFlowReference(5)">5 L/min</button>
                            <button onclick="setFlowReference(10)">10 L/min</button>
                            <button onclick="setFlowReference(15)">15 L/min</button>
                        </div>

                        <div class="measurement-display">
                            <div>Reference: <span id="flow-reference">--</span> L/min</div>
                            <div>Device Reading: <span id="flow-device" class="measurement-value">--</span> L/min</div>
                            <div>Error: <span id="flow-error">--</span> L/min</div>
                            <div>% Error: <span id="flow-percent-error">--</span> %</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Measurements Section -->
        <div id="measurements" class="section" style="display: none;">
            <h2>Clinical Engineering Measurements</h2>
            <p>Practice precision measurement techniques and data analysis used in clinical engineering.</p>

            <div class="tab-container">
                <div class="tab-buttons">
                    <button class="tab-button active" onclick="showMeasurementTab('accuracy')">Accuracy Assessment</button>
                    <button class="tab-button" onclick="showMeasurementTab('uncertainty')">Uncertainty Analysis</button>
                    <button class="tab-button" onclick="showMeasurementTab('trending')">Performance Trending</button>
                </div>

                <!-- Accuracy Assessment Tab -->
                <div id="accuracy-tab" class="tab-content active">
                    <h3>Device Accuracy Assessment</h3>
                    <p>Measure and analyze the accuracy of medical devices compared to reference standards.</p>

                    <div class="control-panel">
                        <div class="control-group">
                            <label>Device Type:</label>
                            <select id="accuracyDevice">
                                <option value="bp">Blood Pressure Monitor</option>
                                <option value="temp">Temperature Monitor</option>
                                <option value="spo2">Pulse Oximeter</option>
                                <option value="flow">Flow Meter</option>
                            </select>
                        </div>
                        <button onclick="startAccuracyTest()">Start Accuracy Test</button>
                        <button onclick="generateAccuracyReport()" class="secondary">Generate Report</button>
                    </div>

                    <div id="accuracy-measurements" style="display: none;">
                        <h4>Measurement Data</h4>
                        <table id="accuracy-table" style="width: 100%; border-collapse: collapse; margin: 20px 0;">
                            <thead>
                                <tr style="background-color: #f8f9fa;">
                                    <th style="border: 1px solid #dee2e6; padding: 10px;">Measurement #</th>
                                    <th style="border: 1px solid #dee2e6; padding: 10px;">Reference Value</th>
                                    <th style="border: 1px solid #dee2e6; padding: 10px;">Device Reading</th>
                                    <th style="border: 1px solid #dee2e6; padding: 10px;">Error</th>
                                    <th style="border: 1px solid #dee2e6; padding: 10px;">% Error</th>
                                </tr>
                            </thead>
                            <tbody id="accuracy-tbody">
                            </tbody>
                        </table>

                        <div id="accuracy-statistics">
                            <h4>Statistical Analysis</h4>
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                                <div class="measurement-display" style="margin: 0;">
                                    <div>Mean Error</div>
                                    <div id="mean-error" class="measurement-value">--</div>
                                </div>
                                <div class="measurement-display" style="margin: 0;">
                                    <div>Std Deviation</div>
                                    <div id="std-deviation" class="measurement-value">--</div>
                                </div>
                                <div class="measurement-display" style="margin: 0;">
                                    <div>Max Error</div>
                                    <div id="max-error" class="measurement-value">--</div>
                                </div>
                                <div class="measurement-display" style="margin: 0;">
                                    <div>Accuracy</div>
                                    <div id="accuracy-percent" class="measurement-value">--</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Uncertainty Analysis Tab -->
                <div id="uncertainty-tab" class="tab-content">
                    <h3>Measurement Uncertainty Analysis</h3>
                    <p>Calculate and analyze measurement uncertainty according to GUM (Guide to the Expression of Uncertainty in Measurement).</p>

                    <div class="control-panel">
                        <div class="control-group">
                            <label>Measurement Type:</label>
                            <select id="uncertaintyType">
                                <option value="pressure">Pressure Measurement</option>
                                <option value="temperature">Temperature Measurement</option>
                                <option value="flow">Flow Measurement</option>
                            </select>
                        </div>
                        <button onclick="calculateUncertainty()">Calculate Uncertainty</button>
                    </div>

                    <div id="uncertainty-analysis">
                        <h4>Uncertainty Components</h4>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px; margin: 20px 0;">
                            <div class="section" style="margin: 0;">
                                <h5>Type A (Statistical)</h5>
                                <p>Repeatability: <span id="type-a-uncertainty">±0.5</span> units</p>
                                <p>Degrees of Freedom: <span id="type-a-dof">9</span></p>
                            </div>
                            <div class="section" style="margin: 0;">
                                <h5>Type B (Systematic)</h5>
                                <p>Calibration: <span id="type-b-cal">±0.3</span> units</p>
                                <p>Resolution: <span id="type-b-res">±0.1</span> units</p>
                                <p>Environmental: <span id="type-b-env">±0.2</span> units</p>
                            </div>
                        </div>

                        <div class="measurement-display">
                            <div>Combined Standard Uncertainty</div>
                            <div id="combined-uncertainty" class="measurement-value">±0.6 units</div>
                            <div>Expanded Uncertainty (k=2)</div>
                            <div id="expanded-uncertainty" class="measurement-value">±1.2 units</div>
                        </div>
                    </div>
                </div>

                <!-- Performance Trending Tab -->
                <div id="trending-tab" class="tab-content">
                    <h3>Device Performance Trending</h3>
                    <p>Monitor device performance over time to identify drift and predict maintenance needs.</p>

                    <div class="control-panel">
                        <button onclick="generateTrendData()">Generate Trend Data</button>
                        <button onclick="analyzeTrend()" class="secondary">Analyze Trend</button>
                        <button onclick="predictMaintenance()" class="warning">Predict Maintenance</button>
                    </div>

                    <div id="trend-analysis">
                        <div class="measurement-display">
                            <div>Current Drift Rate</div>
                            <div id="drift-rate" class="measurement-value">+0.02 units/month</div>
                            <div>Predicted Out-of-Tolerance</div>
                            <div id="predicted-oot" class="measurement-value">18 months</div>
                        </div>

                        <div style="margin: 20px 0;">
                            <h4>Performance Trend (Last 12 Months)</h4>
                            <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center;">
                                <p style="color: #6c757d; font-style: italic;">
                                    📊 Trend Chart Placeholder<br>
                                    (In a real implementation, this would show an interactive chart)
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Troubleshooting Section -->
        <div id="troubleshooting" class="section" style="display: none;">
            <h2>Equipment Troubleshooting Scenarios</h2>
            <p>Practice diagnostic skills with realistic equipment problems and systematic troubleshooting approaches.</p>

            <div class="control-panel">
                <div class="control-group">
                    <label>Scenario Type:</label>
                    <select id="troubleshootingScenario" onchange="loadTroubleshootingScenario()">
                        <option value="">Select a scenario...</option>
                        <option value="bp-error">Blood Pressure Monitor Error</option>
                        <option value="ventilator-alarm">Ventilator High Pressure Alarm</option>
                        <option value="monitor-noise">Patient Monitor Signal Noise</option>
                        <option value="pump-occlusion">Infusion Pump Occlusion</option>
                        <option value="defibrillator-charge">Defibrillator Charge Failure</option>
                    </select>
                </div>
                <button onclick="startTroubleshooting()" id="start-troubleshooting" disabled>Start Troubleshooting</button>
            </div>

            <div id="troubleshooting-content" style="display: none;">
                <div id="scenario-description" class="section">
                    <h3 id="scenario-title">Scenario Title</h3>
                    <p id="scenario-details">Scenario details will appear here...</p>

                    <div id="scenario-symptoms">
                        <h4>Observed Symptoms:</h4>
                        <ul id="symptoms-list">
                        </ul>
                    </div>
                </div>

                <div id="diagnostic-steps">
                    <h3>Diagnostic Process</h3>
                    <div id="troubleshooting-steps">
                        <!-- Steps will be populated by JavaScript -->
                    </div>
                </div>

                <div id="troubleshooting-results" style="display: none;">
                    <h3>Troubleshooting Results</h3>
                    <div id="diagnosis-summary"></div>
                    <div id="corrective-actions"></div>
                    <div id="prevention-measures"></div>
                </div>
            </div>
        </div>

    </div>

    <footer>
        <p>&copy; 2025 Clinical Engineering Interactive Simulations | Dr. Mohammed Yagoub Esmail</p>
        <p>Nahda College | All Rights Reserved</p>
    </footer>

    <script>
        // Global Variables
        let simulationInterval;
        let isRunning = false;
        let activeDevice = 'patientMonitor';
        let patientScenario = 'normal';
        let simulationSpeed = 2000;
        let calibrationData = {};
        let measurementData = [];
        let currentCalibrationStep = {};

        // Navigation Functions
        function showSection(sectionId) {
            // Hide all sections
            const sections = document.querySelectorAll('.section');
            sections.forEach(section => section.style.display = 'none');

            // Remove active class from all nav links
            const navLinks = document.querySelectorAll('.nav-link');
            navLinks.forEach(link => link.classList.remove('active'));

            // Show selected section
            document.getElementById(sectionId).style.display = 'block';

            // Add active class to clicked nav link
            event.target.classList.add('active');
        }

        // Device Simulation Functions
        function updateSimulationSpeed() {
            const speedSelector = document.getElementById('simulationSpeed');
            simulationSpeed = parseInt(speedSelector.value);

            if (isRunning) {
                clearInterval(simulationInterval);
                simulationInterval = setInterval(updateVitalSigns, simulationSpeed);
            }
        }

        function updateVitalSigns() {
            if (!isRunning) return;

            if (activeDevice === 'patientMonitor') {
                const scenarioSelector = document.getElementById('patientScenario');
                patientScenario = scenarioSelector ? scenarioSelector.value : 'normal';

                let heartRate, systolicBP, diastolicBP, oxygenSat;

                switch (patientScenario) {
                    case 'normal':
                        heartRate = Math.floor(Math.random() * (90 - 60 + 1)) + 60;
                        systolicBP = Math.floor(Math.random() * (130 - 110 + 1)) + 110;
                        diastolicBP = Math.floor(Math.random() * (85 - 70 + 1)) + 70;
                        oxygenSat = Math.floor(Math.random() * (100 - 95 + 1)) + 95;
                        break;
                    case 'tachycardia':
                        heartRate = Math.floor(Math.random() * (120 - 100 + 1)) + 100;
                        systolicBP = Math.floor(Math.random() * (120 - 100 + 1)) + 100;
                        diastolicBP = Math.floor(Math.random() * (80 - 60 + 1)) + 60;
                        oxygenSat = Math.floor(Math.random() * (98 - 95 + 1)) + 95;
                        break;
                    case 'hypotension':
                        heartRate = Math.floor(Math.random() * (100 - 80 + 1)) + 80;
                        systolicBP = Math.floor(Math.random() * (90 - 70 + 1)) + 70;
                        diastolicBP = Math.floor(Math.random() * (60 - 40 + 1)) + 40;
                        oxygenSat = Math.floor(Math.random() * (95 - 92 + 1)) + 92;
                        break;
                    case 'hypoxemia':
                        heartRate = Math.floor(Math.random() * (110 - 90 + 1)) + 90;
                        systolicBP = Math.floor(Math.random() * (130 - 110 + 1)) + 110;
                        diastolicBP = Math.floor(Math.random() * (85 - 70 + 1)) + 70;
                        oxygenSat = Math.floor(Math.random() * (90 - 85 + 1)) + 85;
                        break;
                    default:
                        heartRate = Math.floor(Math.random() * (90 - 60 + 1)) + 60;
                        systolicBP = Math.floor(Math.random() * (130 - 110 + 1)) + 110;
                        diastolicBP = Math.floor(Math.random() * (85 - 70 + 1)) + 70;
                        oxygenSat = Math.floor(Math.random() * (100 - 95 + 1)) + 95;
                }

                document.getElementById('heartRate').textContent = `${heartRate} bpm`;
                document.getElementById('bloodPressure').textContent = `${systolicBP}/${diastolicBP} mmHg`;
                document.getElementById('oxygenSaturation').textContent = `${oxygenSat} %`;

                // Update status based on values
                const heartRateElement = document.getElementById('heartRate');
                heartRateElement.className = 'vital-value' + ((heartRate > 95 || heartRate < 55) ? ' abnormal' : '');
                const bpElement = document.getElementById('bloodPressure');
                bpElement.className = 'vital-value' + ((systolicBP < 90 || diastolicBP < 50) ? ' abnormal' : '');
                const spo2Element = document.getElementById('oxygenSaturation');
                spo2Element.className = 'vital-value' + ((oxygenSat < 92) ? ' abnormal' : '');

            } else if (activeDevice === 'ecgMonitor') {
                const rhythms = ["Normal Sinus Rhythm", "Sinus Tachycardia", "Sinus Bradycardia"];
                const heartRhythm = rhythms[Math.floor(Math.random() * rhythms.length)];
                const qrsDuration = Math.floor(Math.random() * (120 - 80 + 1)) + 80;

                document.getElementById('heartRhythm').textContent = heartRhythm;
                document.getElementById('qrsDuration').textContent = `${qrsDuration} ms`;

                const heartRhythmElement = document.getElementById('heartRhythm');
                heartRhythmElement.className = 'vital-value' + (heartRhythm !== "Normal Sinus Rhythm" ? ' abnormal' : '');
            }
            // Additional device types would continue here...
        }

                // This code was moved to the enhanced updateVitalSigns function above
            } else if (activeDevice === 'respiratoryMonitor') {
                // Get ventilator settings
                const ventilationMode = document.getElementById('ventilationMode').value;
                const fio2 = parseInt(document.getElementById('fio2').value);

                let tidalVolume, respiratoryRate, respOxygenSaturation, etCO2;

                // Simulate Respiratory parameters based on settings
                // Basic logic: higher FiO2 -> higher SpO2
                // Different modes could influence RR/Vt ranges

                if (ventilationMode === 'AC') {
                    tidalVolume = Math.floor(Math.random() * (550 - 450 + 1)) + 450; // Tightly controlled
                    respiratoryRate = Math.floor(Math.random() * (18 - 14 + 1)) + 14; // Controlled rate
                } else if (ventilationMode === 'SIMV') {
                    tidalVolume = Math.floor(Math.random() * (650 - 350 + 1)) + 350; // More variability
                    respiratoryRate = Math.floor(Math.random() * (22 - 10 + 1)) + 10; // Patient can breathe spontaneously
                } else if (ventilationMode === 'CPAP') {
                    tidalVolume = Math.floor(Math.random() * (400 - 200 + 1)) + 200; // Patient's own effort
                    respiratoryRate = Math.floor(Math.random() * (25 - 12 + 1)) + 12; // Patient's own effort
                }

                // FiO2 influence on SpO2
                if (fio2 >= 80) {
                    respOxygenSaturation = Math.floor(Math.random() * (100 - 98 + 1)) + 98;
                } else if (fio2 >= 50) {
                    respOxygenSaturation = Math.floor(Math.random() * (99 - 95 + 1)) + 95;
                } else if (fio2 >= 30) {
                    respOxygenSaturation = Math.floor(Math.random() * (97 - 93 + 1)) + 93;
                } else {
                    respOxygenSaturation = Math.floor(Math.random() * (95 - 90 + 1)) + 90;
                }

                etCO2 = Math.floor(Math.random() * (45 - 35 + 1)) + 35; // 35-45 mmHg

                document.getElementById('tidalVolume').textContent = `${tidalVolume} mL`;
                document.getElementById('respiratoryRate').textContent = `${respiratoryRate} bpm`;
                document.getElementById('respOxygenSaturation').textContent = `${respOxygenSaturation} %`;
                document.getElementById('etCO2').textContent = `${etCO2} mmHg`;

                // Basic anomaly highlighting for respiratory rate and SpO2
                const rrElement = document.getElementById('respiratoryRate');
                rrElement.style.color = (respiratoryRate > 22 || respiratoryRate < 10) ? '#dc3545' : '#28a745';
                const respSpo2Element = document.getElementById('respOxygenSaturation');
                respSpo2Element.style.color = (respOxygenSaturation < 92) ? '#dc3545' : '#28a745';

            } else if (activeDevice === 'hemodynamicMonitor') {
                // Simulate Hemodynamic parameters
                const cvp = (Math.random() * (12 - 2) + 2).toFixed(1); // 2-12 mmHg
                const papSystolic = Math.floor(Math.random() * (30 - 18 + 1)) + 18; // 18-30 mmHg
                const papDiastolic = Math.floor(Math.random() * (15 - 5 + 1)) + 5; // 5-15 mmHg
                const cardiacOutput = (Math.random() * (6 - 4) + 4).toFixed(1); // 4-6 L/min
                const svr = Math.floor(Math.random() * (1200 - 800 + 1)) + 800; // 800-1200 dynes·s/cm⁵

                document.getElementById('cvp').textContent = `${cvp} mmHg`;
                document.getElementById('pap').textContent = `${papSystolic}/${papDiastolic} mmHg`;
                document.getElementById('cardiacOutput').textContent = `${cardiacOutput} L/min`;
                document.getElementById('svr').textContent = `${svr} dynes·s/cm⁵`;

                // Basic anomaly highlighting for CVP
                const cvpElement = document.getElementById('cvp');
                if (cvp > 8 || cvp < 4) {
                    cvpElement.style.color = '#dc3545';
                } else {
                    cvpElement.style.color = '#28a745';
                }
            } else if (activeDevice === 'neurologicalMonitor') {
                // Simulate Neurological parameters
                const icp = (Math.random() * (15 - 5) + 5).toFixed(1); // 5-15 mmHg
                const map = Math.floor(Math.random() * (100 - 70 + 1)) + 70; // 70-100 mmHg (for CPP calculation)
                const cpp = (map - icp).toFixed(1); // CPP = MAP - ICP
                const gcs = Math.floor(Math.random() * (15 - 3 + 1)) + 3; // 3-15

                document.getElementById('icp').textContent = `${icp} mmHg`;
                document.getElementById('cpp').textContent = `${cpp} mmHg`;
                document.getElementById('gcs').textContent = `${gcs}`;

                // Basic anomaly highlighting for ICP
                const icpElement = document.getElementById('icp');
                if (icp > 10) {
                    icpElement.style.color = '#dc3545';
                } else {
                    icpElement.style.color = '#28a745';
                }
            }
        }

        function switchDevice() {
            stopSimulation(); // Stop current simulation
            const selector = document.getElementById('deviceSelector');
            activeDevice = selector.value;

            // Hide all device displays
            document.getElementById('patientMonitorDisplay').classList.remove('active');
            document.getElementById('ecgMonitorDisplay').classList.remove('active');
            document.getElementById('respiratoryMonitorDisplay').classList.remove('active');
            document.getElementById('hemodynamicMonitorDisplay').classList.remove('active');
            document.getElementById('neurologicalMonitorDisplay').classList.remove('active');

            // Show the selected device display
            const selectedDisplay = document.getElementById(`${activeDevice}Display`);
            selectedDisplay.classList.add('active');

            document.getElementById('simulation-area').innerHTML = `<p>Device simulation ready. Select a device and click "Start Device" to begin monitoring.</p>`;
            document.getElementById('device-status').className = 'status-stopped';
            document.getElementById('device-status').textContent = 'Device Status: Stopped';

            // Re-append the selected display and status to the simulation area
            document.getElementById('simulation-area').appendChild(selectedDisplay);
            document.getElementById('simulation-area').appendChild(document.getElementById('device-status'));

            // Reset vital signs display for the new device
            if (activeDevice === 'patientMonitor') {
                document.getElementById('heartRate').textContent = '-- bpm';
                document.getElementById('bloodPressure').textContent = '--/-- mmHg';
                document.getElementById('oxygenSaturation').textContent = '-- %';
                // Reset scenario selector to normal when switching to patient monitor
                const scenarioSelector = document.getElementById('patientScenario');
                if (scenarioSelector) scenarioSelector.value = 'normal';
                patientScenario = 'normal';
            } else if (activeDevice === 'ecgMonitor') {
                document.getElementById('heartRhythm').textContent = '--';
                document.getElementById('qrsDuration').textContent = '-- ms';
            } else if (activeDevice === 'respiratoryMonitor') {
                document.getElementById('tidalVolume').textContent = '-- mL';
                document.getElementById('respiratoryRate').textContent = '-- bpm';
                document.getElementById('respOxygenSaturation').textContent = '-- %';
                document.getElementById('etCO2').textContent = '-- mmHg';
                // Reset ventilator settings to default
                document.getElementById('ventilationMode').value = 'AC';
                document.getElementById('fio2').value = '21';
            } else if (activeDevice === 'hemodynamicMonitor') {
                document.getElementById('cvp').textContent = '-- mmHg';
                document.getElementById('pap').textContent = '--/-- mmHg';
                document.getElementById('cardiacOutput').textContent = '-- L/min';
                document.getElementById('svr').textContent = '-- dynes·s/cm⁵';
            } else if (activeDevice === 'neurologicalMonitor') {
                document.getElementById('icp').textContent = '-- mmHg';
                document.getElementById('cpp').textContent = '-- mmHg';
                document.getElementById('gcs').textContent = '--';
            }
        }

        function startSimulation() {
            if (isRunning) return;
            isRunning = true;
            document.getElementById('device-status').className = 'status-running';
            document.getElementById('device-status').textContent = 'Device Status: Running';

            // Clear initial message and show relevant device display
            document.getElementById('simulation-area').innerHTML = '';
            const selectedDisplay = document.getElementById(`${activeDevice}Display`);
            document.getElementById('simulation-area').appendChild(selectedDisplay);
            selectedDisplay.classList.add('active');
            document.getElementById('simulation-area').appendChild(document.getElementById('device-status'));

            updateVitalSigns(); // Initial update
            simulationInterval = setInterval(updateVitalSigns, 2000); // Update every 2 seconds
            console.log('Simulation started for ' + activeDevice);
        }

        function stopSimulation() {
            if (!isRunning) return;
            isRunning = false;
            clearInterval(simulationInterval);
            document.getElementById('device-status').className = 'status-stopped';
            document.getElementById('device-status').textContent = 'Device Status: Stopped';
            console.log('Simulation stopped for ' + activeDevice);
        }

        function resetSimulation() {
            stopSimulation(); // Stop if running
            document.getElementById('simulation-area').innerHTML = `<p>Device simulation ready. Select a device and click "Start Device" to begin monitoring.</p>`;
            document.getElementById('device-status').className = 'status-stopped';
            document.getElementById('device-status').textContent = 'Device Status: Stopped';

            // Re-append the selected display and status to the simulation area
            const selectedDisplay = document.getElementById(`${activeDevice}Display`);
            document.getElementById('simulation-area').appendChild(selectedDisplay);
            selectedDisplay.classList.add('active'); // Ensure it's active after reset
            document.getElementById('simulation-area').appendChild(document.getElementById('device-status'));

            // Reset vital signs display for the current device
            if (activeDevice === 'patientMonitor') {
                document.getElementById('heartRate').textContent = '-- bpm';
                document.getElementById('bloodPressure').textContent = '--/-- mmHg';
                document.getElementById('oxygenSaturation').textContent = '-- %';
                const scenarioSelector = document.getElementById('patientScenario');
                if (scenarioSelector) scenarioSelector.value = 'normal';
                patientScenario = 'normal';
            } else if (activeDevice === 'ecgMonitor') {
                document.getElementById('heartRhythm').textContent = '--';
                document.getElementById('qrsDuration').textContent = '-- ms';
            } else if (activeDevice === 'respiratoryMonitor') {
                document.getElementById('tidalVolume').textContent = '-- mL';
                document.getElementById('respiratoryRate').textContent = '-- bpm';
                document.getElementById('respOxygenSaturation').textContent = '-- %';
                document.getElementById('etCO2').textContent = '-- mmHg';
                document.getElementById('ventilationMode').value = 'AC';
                document.getElementById('fio2').value = '21';
            } else if (activeDevice === 'hemodynamicMonitor') {
                document.getElementById('cvp').textContent = '-- mmHg';
                document.getElementById('pap').textContent = '--/-- mmHg';
                document.getElementById('cardiacOutput').textContent = '-- L/min';
                document.getElementById('svr').textContent = '-- dynes·s/cm⁵';
            } else if (activeDevice === 'neurologicalMonitor') {
                document.getElementById('icp').textContent = '-- mmHg';
                document.getElementById('cpp').textContent = '-- mmHg';
                document.getElementById('gcs').textContent = '--';
            }
            console.log('Simulation reset.');
        }

        // Calibration Functions
        function showCalibrationTab(tabName) {
            // Hide all tabs
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelectorAll('.tab-button').forEach(btn => {
                btn.classList.remove('active');
            });

            // Show selected tab
            document.getElementById(tabName + '-tab').classList.add('active');
            event.target.classList.add('active');
        }

        function completeCalibrationStep(type, stepNumber) {
            const stepElement = document.getElementById(`${type}-step-${stepNumber}`);
            stepElement.classList.add('completed');
            stepElement.querySelector('.step-number').classList.add('completed');

            // Enable next step
            const nextStep = document.getElementById(`${type}-step-${stepNumber + 1}`);
            if (nextStep) {
                nextStep.classList.add('active');
                nextStep.querySelector('button').disabled = false;
            }

            // Show results for final step
            if (type === 'pressure' && stepNumber === 5) {
                document.getElementById('pressure-results').style.display = 'block';
                generatePressureResults();
            }
        }

        function setPressureReference(value) {
            document.getElementById('pressure-reference').textContent = value;

            // Simulate device reading with some error
            const error = (Math.random() - 0.5) * 4; // ±2 mmHg random error
            const deviceReading = value + error;

            document.getElementById('pressure-device').textContent = deviceReading.toFixed(1);
            document.getElementById('pressure-error').textContent = error.toFixed(1);

            // Store calibration data
            if (!calibrationData.pressure) calibrationData.pressure = {};
            calibrationData.pressure[value] = {
                reference: value,
                device: deviceReading,
                error: error
            };

            // Enable completion button for current step
            if (value === 100) {
                document.querySelector('#pressure-step-3 button').disabled = false;
            } else if (value === 200) {
                document.querySelector('#pressure-step-4 button').disabled = false;
            }
        }

        function generatePressureResults() {
            const results = calibrationData.pressure;
            let allPass = true;

            Object.keys(results).forEach(ref => {
                const data = results[ref];
                const pass = Math.abs(data.error) <= 3; // ±3 mmHg tolerance
                allPass = allPass && pass;

                const row = document.getElementById(`pressure-result-${ref}`);
                row.innerHTML = `
                    <td style="border: 1px solid #dee2e6; padding: 8px;">${data.reference}</td>
                    <td style="border: 1px solid #dee2e6; padding: 8px;">${data.device.toFixed(1)}</td>
                    <td style="border: 1px solid #dee2e6; padding: 8px;">${data.error.toFixed(1)}</td>
                    <td style="border: 1px solid #dee2e6; padding: 8px; color: ${pass ? '#28a745' : '#dc3545'};">
                        ${pass ? 'PASS' : 'FAIL'}
                    </td>
                `;
            });

            document.getElementById('pressure-pass-fail').innerHTML = `
                <h5 style="color: ${allPass ? '#28a745' : '#dc3545'};">
                    Overall Result: ${allPass ? 'PASS' : 'FAIL'}
                </h5>
            `;
        }

        function setTemperatureReference(value) {
            document.getElementById('temp-reference').textContent = value;
            const error = (Math.random() - 0.5) * 0.4; // ±0.2°C random error
            const deviceReading = value + error;
            document.getElementById('temp-device').textContent = deviceReading.toFixed(1);
            document.getElementById('temp-error').textContent = error.toFixed(1);
        }

        function setFlowReference(value) {
            document.getElementById('flow-reference').textContent = value;
            const error = (Math.random() - 0.5) * 0.2; // ±0.1 L/min random error
            const deviceReading = value + error;
            const percentError = (error / value) * 100;

            document.getElementById('flow-device').textContent = deviceReading.toFixed(2);
            document.getElementById('flow-error').textContent = error.toFixed(2);
            document.getElementById('flow-percent-error').textContent = percentError.toFixed(1);
        }

        function performSafetyTest(testType) {
            let value, limit, unit, status;

            switch (testType) {
                case 'leakage':
                    value = (Math.random() * 50).toFixed(1); // 0-50 µA
                    limit = '100 µA';
                    unit = 'µA';
                    status = parseFloat(value) < 100 ? 'PASS' : 'FAIL';
                    break;
                case 'ground':
                    value = (Math.random() * 0.2).toFixed(3); // 0-0.2 Ω
                    limit = '0.1 Ω';
                    unit = 'Ω';
                    status = parseFloat(value) < 0.1 ? 'PASS' : 'FAIL';
                    break;
                case 'insulation':
                    value = (Math.random() * 50 + 50).toFixed(0); // 50-100 MΩ
                    limit = '10 MΩ';
                    unit = 'MΩ';
                    status = parseFloat(value) > 10 ? 'PASS' : 'FAIL';
                    break;
            }

            document.getElementById('safety-test-type').textContent = testType.charAt(0).toUpperCase() + testType.slice(1);
            document.getElementById('safety-value').textContent = value + ' ' + unit;
            document.getElementById('safety-limit').textContent = limit;
            document.getElementById('safety-status').textContent = status;
            document.getElementById('safety-status').style.color = status === 'PASS' ? '#28a745' : '#dc3545';
        }

        // Measurement Functions
        function showMeasurementTab(tabName) {
            document.querySelectorAll('#measurements .tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelectorAll('#measurements .tab-button').forEach(btn => {
                btn.classList.remove('active');
            });

            document.getElementById(tabName + '-tab').classList.add('active');
            event.target.classList.add('active');
        }

        function startAccuracyTest() {
            const device = document.getElementById('accuracyDevice').value;
            document.getElementById('accuracy-measurements').style.display = 'block';

            // Generate 10 measurement points
            const tbody = document.getElementById('accuracy-tbody');
            tbody.innerHTML = '';
            measurementData = [];

            for (let i = 1; i <= 10; i++) {
                let reference, deviceReading, error, percentError;

                switch (device) {
                    case 'bp':
                        reference = 80 + (i * 10); // 90-180 mmHg
                        error = (Math.random() - 0.5) * 6; // ±3 mmHg
                        deviceReading = reference + error;
                        break;
                    case 'temp':
                        reference = 35 + (i * 0.5); // 35.5-40°C
                        error = (Math.random() - 0.5) * 0.4; // ±0.2°C
                        deviceReading = reference + error;
                        break;
                    case 'spo2':
                        reference = 85 + i; // 86-95%
                        error = (Math.random() - 0.5) * 4; // ±2%
                        deviceReading = reference + error;
                        break;
                    case 'flow':
                        reference = i; // 1-10 L/min
                        error = (Math.random() - 0.5) * 0.2; // ±0.1 L/min
                        deviceReading = reference + error;
                        break;
                }

                percentError = (error / reference) * 100;
                measurementData.push({ reference, deviceReading, error, percentError });

                const row = tbody.insertRow();
                row.innerHTML = `
                    <td style="border: 1px solid #dee2e6; padding: 8px;">${i}</td>
                    <td style="border: 1px solid #dee2e6; padding: 8px;">${reference.toFixed(1)}</td>
                    <td style="border: 1px solid #dee2e6; padding: 8px;">${deviceReading.toFixed(1)}</td>
                    <td style="border: 1px solid #dee2e6; padding: 8px;">${error.toFixed(1)}</td>
                    <td style="border: 1px solid #dee2e6; padding: 8px;">${percentError.toFixed(1)}%</td>
                `;
            }

            calculateStatistics();
        }

        function calculateStatistics() {
            const errors = measurementData.map(d => Math.abs(d.error));
            const meanError = errors.reduce((a, b) => a + b, 0) / errors.length;
            const variance = errors.reduce((a, b) => a + Math.pow(b - meanError, 2), 0) / errors.length;
            const stdDev = Math.sqrt(variance);
            const maxError = Math.max(...errors);
            const accuracy = 100 - (meanError / measurementData[0].reference * 100);

            document.getElementById('mean-error').textContent = `±${meanError.toFixed(2)}`;
            document.getElementById('std-deviation').textContent = `±${stdDev.toFixed(2)}`;
            document.getElementById('max-error').textContent = `±${maxError.toFixed(2)}`;
            document.getElementById('accuracy-percent').textContent = `${accuracy.toFixed(1)}%`;
        }

        // Troubleshooting Functions
        function loadTroubleshootingScenario() {
            const scenario = document.getElementById('troubleshootingScenario').value;
            const startButton = document.getElementById('start-troubleshooting');

            if (scenario) {
                startButton.disabled = false;
            } else {
                startButton.disabled = true;
            }
        }

        function startTroubleshooting() {
            const scenario = document.getElementById('troubleshootingScenario').value;
            document.getElementById('troubleshooting-content').style.display = 'block';

            const scenarios = {
                'bp-error': {
                    title: 'Blood Pressure Monitor Error Code E02',
                    details: 'A patient monitor is displaying error code E02 and cannot obtain blood pressure readings.',
                    symptoms: [
                        'Error code E02 displayed',
                        'No blood pressure readings obtained',
                        'Cuff inflates but does not deflate properly',
                        'Intermittent beeping sounds'
                    ],
                    steps: [
                        'Check cuff connections and tubing for leaks',
                        'Verify cuff size is appropriate for patient',
                        'Inspect tubing for kinks or obstructions',
                        'Test with known good cuff and tubing',
                        'Check internal pneumatic system'
                    ]
                },
                'ventilator-alarm': {
                    title: 'Ventilator High Pressure Alarm',
                    details: 'A mechanical ventilator is triggering frequent high pressure alarms during patient ventilation.',
                    symptoms: [
                        'High pressure alarm activating',
                        'Peak pressures exceeding set limits',
                        'Patient appears to be fighting the ventilator',
                        'Decreased tidal volume delivery'
                    ],
                    steps: [
                        'Check patient airway for obstructions',
                        'Inspect ventilator circuit for kinks',
                        'Verify pressure sensor calibration',
                        'Check for water in circuit',
                        'Evaluate patient-ventilator synchrony'
                    ]
                }
            };

            const selectedScenario = scenarios[scenario];
            if (selectedScenario) {
                document.getElementById('scenario-title').textContent = selectedScenario.title;
                document.getElementById('scenario-details').textContent = selectedScenario.details;

                const symptomsList = document.getElementById('symptoms-list');
                symptomsList.innerHTML = '';
                selectedScenario.symptoms.forEach(symptom => {
                    const li = document.createElement('li');
                    li.textContent = symptom;
                    symptomsList.appendChild(li);
                });

                const stepsContainer = document.getElementById('troubleshooting-steps');
                stepsContainer.innerHTML = '';
                selectedScenario.steps.forEach((step, index) => {
                    const stepDiv = document.createElement('div');
                    stepDiv.className = 'calibration-step';
                    stepDiv.innerHTML = `
                        <div class="step-number">${index + 1}</div>
                        <h5>Step ${index + 1}</h5>
                        <p>${step}</p>
                        <button onclick="completeTroubleshootingStep(${index + 1})" class="success">Complete Step</button>
                    `;
                    stepsContainer.appendChild(stepDiv);
                });
            }
        }

        function completeTroubleshootingStep(stepNumber) {
            const steps = document.querySelectorAll('#troubleshooting-steps .calibration-step');
            if (steps[stepNumber - 1]) {
                steps[stepNumber - 1].classList.add('completed');
                steps[stepNumber - 1].querySelector('.step-number').classList.add('completed');
            }

            // Check if all steps completed
            const completedSteps = document.querySelectorAll('#troubleshooting-steps .calibration-step.completed');
            if (completedSteps.length === steps.length) {
                document.getElementById('troubleshooting-results').style.display = 'block';
                document.getElementById('diagnosis-summary').innerHTML = `
                    <h4>Diagnosis Summary</h4>
                    <p>Based on the systematic troubleshooting approach, the most likely cause has been identified and appropriate corrective actions have been determined.</p>
                `;
                document.getElementById('corrective-actions').innerHTML = `
                    <h4>Corrective Actions</h4>
                    <ul>
                        <li>Replace faulty component if identified</li>
                        <li>Recalibrate device if necessary</li>
                        <li>Document findings and actions taken</li>
                        <li>Test device functionality before returning to service</li>
                    </ul>
                `;
                document.getElementById('prevention-measures').innerHTML = `
                    <h4>Prevention Measures</h4>
                    <ul>
                        <li>Implement regular preventive maintenance schedule</li>
                        <li>Train users on proper operation procedures</li>
                        <li>Monitor device performance trends</li>
                        <li>Update maintenance protocols based on findings</li>
                    </ul>
                `;
            }
        }

        // Additional utility functions
        function calculateUncertainty() {
            // Simulate uncertainty calculation
            const typeA = 0.5;
            const typeBCal = 0.3;
            const typeBRes = 0.1;
            const typeBEnv = 0.2;

            const combined = Math.sqrt(typeA*typeA + typeBCal*typeBCal + typeBRes*typeBRes + typeBEnv*typeBEnv);
            const expanded = combined * 2; // k=2

            document.getElementById('combined-uncertainty').textContent = `±${combined.toFixed(1)} units`;
            document.getElementById('expanded-uncertainty').textContent = `±${expanded.toFixed(1)} units`;
        }

        function generateTrendData() {
            document.getElementById('drift-rate').textContent = '+0.02 units/month';
            document.getElementById('predicted-oot').textContent = '18 months';
        }

        function analyzeTrend() {
            alert('Trend analysis complete. Device showing gradual positive drift within acceptable limits.');
        }

        function predictMaintenance() {
            alert('Predictive maintenance analysis suggests calibration due in 18 months based on current drift rate.');
        }

        function generateAccuracyReport() {
            if (measurementData.length === 0) {
                alert('Please run an accuracy test first.');
                return;
            }

            alert('Accuracy report generated. In a real system, this would create a PDF report with all measurement data and statistical analysis.');
        }

        // Initial display setup
        document.addEventListener('DOMContentLoaded', () => {
            // Show overview section by default
            showSection('overview');

            // Initialize device simulation if elements exist
            if (document.getElementById('deviceSelector')) {
                switchDevice();
            }
        });
    </script>
</body>
</html>