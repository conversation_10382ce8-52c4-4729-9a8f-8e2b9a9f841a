<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hands-on Learning: Medical Device Operation, Measurement & Calibration</title>
    <style>
        /* Base Styles */
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
            background-color: #f5f7fa;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        /* Header Styles */
        header {
            background: linear-gradient(135deg, #0056b3 0%, #00a0e9 100%);
            color: white;
            padding: 40px 0;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .header-content {
            max-width: 800px;
            margin: 0 auto;
        }
        
        h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        
        h2 {
            font-size: 1.8rem;
            margin-top: 30px;
            color: #0056b3;
            border-bottom: 2px solid #e1e4e8;
            padding-bottom: 10px;
        }
        
        h3 {
            font-size: 1.4rem;
            color: #0056b3;
            margin-top: 25px;
        }
        
        /* Content Sections */
        .section {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            padding: 25px;
            margin-bottom: 30px;
        }
        
        /* Learning Module Styles */
        .learning-module {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 2px solid #0056b3;
            border-radius: 12px;
            padding: 25px;
            margin: 20px 0;
        }
        
        .hands-on-activity {
            background-color: #e8f5e9;
            border-left: 4px solid #4caf50;
            padding: 20px;
            margin: 20px 0;
            border-radius: 0 8px 8px 0;
        }
        
        .measurement-exercise {
            background-color: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 20px;
            margin: 20px 0;
            border-radius: 0 8px 8px 0;
        }
        
        .calibration-lab {
            background-color: #fff3e0;
            border-left: 4px solid #ff9800;
            padding: 20px;
            margin: 20px 0;
            border-radius: 0 8px 8px 0;
        }
        
        .safety-note {
            background-color: #ffebee;
            border-left: 4px solid #f44336;
            padding: 15px;
            margin: 15px 0;
            border-radius: 0 6px 6px 0;
        }
        
        /* Interactive Elements */
        .device-simulator {
            background: #000;
            color: #00ff00;
            font-family: 'Courier New', monospace;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            text-align: center;
        }
        
        .measurement-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background-color: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .measurement-table th {
            background-color: #0056b3;
            color: white;
            padding: 12px;
            text-align: left;
        }
        
        .measurement-table td {
            padding: 10px 12px;
            border-bottom: 1px solid #dee2e6;
        }
        
        .measurement-table tr:hover {
            background-color: #f8f9fa;
        }
        
        /* Navigation */
        .nav-bar {
            background-color: #343a40;
            padding: 15px 0;
        }
        
        .nav-content {
            max-width: 1400px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
        }
        
        .nav-links {
            display: flex;
            gap: 30px;
        }
        
        .nav-links a {
            color: white;
            text-decoration: none;
            padding: 10px 15px;
            border-radius: 4px;
            transition: background-color 0.3s;
        }
        
        .nav-links a:hover, .nav-links a.active {
            background-color: #0056b3;
        }
        
        /* Form Elements */
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #495057;
        }
        
        input, textarea, select {
            width: 100%;
            padding: 12px;
            border: 2px solid #dee2e6;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }
        
        input:focus, textarea:focus, select:focus {
            outline: none;
            border-color: #0056b3;
            box-shadow: 0 0 0 3px rgba(0, 86, 179, 0.1);
        }
        
        button {
            background-color: #0056b3;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        button:hover {
            background-color: #003d82;
            transform: translateY(-2px);
        }
        
        /* Footer */
        footer {
            background-color: #343a40;
            color: white;
            text-align: center;
            padding: 20px 0;
            margin-top: 40px;
        }
    </style>
</head>
<body>
    <header>
        <div class="header-content">
            <!-- Enhanced Navigation Bar -->
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px; padding: 15px 0; border-bottom: 1px solid rgba(255,255,255,0.2);">
                <div>
                    <a href="index.html" style="background: rgba(255,255,255,0.2); color: white; padding: 10px 20px; border-radius: 25px; text-decoration: none; font-size: 1em; transition: all 0.3s ease; display: inline-flex; align-items: center; gap: 10px; font-weight: 500;"
                       onmouseover="this.style.background='rgba(255,255,255,0.3)'; this.style.transform='translateY(-2px)'"
                       onmouseout="this.style.background='rgba(255,255,255,0.2)'; this.style.transform='translateY(0)'">
                        🏠 العودة للرئيسية | Back to Home
                    </a>
                </div>
                <div style="display: flex; gap: 12px;">
                    <a href="clinical_engineering_simulation.html" style="background: rgba(255,255,255,0.15); color: white; padding: 8px 16px; border-radius: 20px; text-decoration: none; font-size: 0.9em; transition: all 0.3s ease;"
                       onmouseover="this.style.background='rgba(255,255,255,0.25)'"
                       onmouseout="this.style.background='rgba(255,255,255,0.15)'">🚀 المحاكاة</a>
                    <a href="model_answers_activities.html" style="background: rgba(255,255,255,0.15); color: white; padding: 8px 16px; border-radius: 20px; text-decoration: none; font-size: 0.9em; transition: all 0.3s ease;"
                       onmouseover="this.style.background='rgba(255,255,255,0.25)'"
                       onmouseout="this.style.background='rgba(255,255,255,0.15)'">📝 الإجابات النموذجية</a>
                </div>
            </div>

            <h1>التعلم العملي: تشغيل الأجهزة الطبية والقياس والمعايرة</h1>
            <h2 style="color: rgba(255,255,255,0.9); font-weight: 300; margin-bottom: 15px; font-size: 1.5rem;">Hands-on Learning: Medical Device Operation, Measurement & Calibration</h2>
            <p>تدريب عملي شامل لمتخصصي الهندسة السريرية</p>
            <p style="color: rgba(255,255,255,0.8); font-size: 1.1em;">Comprehensive practical training for clinical engineering professionals</p>
        </div>
    </header>

    <nav class="nav-bar">
        <div class="nav-content">
            <div class="nav-links">
                <a href="#device-operation" class="nav-link active">تشغيل الأجهزة | Device Operation</a>
                <a href="#measurement-techniques" class="nav-link">تقنيات القياس | Measurement Techniques</a>
                <a href="#calibration-procedures" class="nav-link">إجراءات المعايرة | Calibration Procedures</a>
                <a href="#safety-protocols" class="nav-link">بروتوكولات الأمان | Safety Protocols</a>
                <a href="#practical-exercises" class="nav-link">التمارين العملية | Practical Exercises</a>
            </div>
            <div style="display: flex; gap: 10px; align-items: center;">
                <span style="color: rgba(255,255,255,0.7); font-size: 0.9em;">اختصارات | Shortcuts:</span>
                <kbd style="background: rgba(255,255,255,0.2); color: white; padding: 2px 6px; border-radius: 3px; font-size: 0.8em;">Alt+H</kbd>
                <span style="color: rgba(255,255,255,0.7); font-size: 0.8em;">للرئيسية</span>
            </div>
        </div>
    </nav>
    
    <div class="container">
        <!-- Learning Objectives -->
        <div class="section">
            <h2>🎯 Learning Objectives</h2>
            <p>Upon completion of this hands-on learning module, students will be able to:</p>
            
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 20px 0;">
                <div style="background: #e8f5e9; padding: 20px; border-radius: 8px;">
                    <h4 style="color: #2e7d32; margin-bottom: 15px;">🔧 Device Operation</h4>
                    <ul style="margin: 0; padding-left: 20px;">
                        <li>Safely operate medical devices</li>
                        <li>Understand user interfaces and controls</li>
                        <li>Interpret device displays and alarms</li>
                        <li>Perform routine operational checks</li>
                    </ul>
                </div>
                <div style="background: #e3f2fd; padding: 20px; border-radius: 8px;">
                    <h4 style="color: #1976d2; margin-bottom: 15px;">📊 Measurement Techniques</h4>
                    <ul style="margin: 0; padding-left: 20px;">
                        <li>Perform accurate measurements</li>
                        <li>Use appropriate test equipment</li>
                        <li>Calculate measurement uncertainty</li>
                        <li>Document measurement results</li>
                    </ul>
                </div>
                <div style="background: #fff3e0; padding: 20px; border-radius: 8px;">
                    <h4 style="color: #f57c00; margin-bottom: 15px;">⚙️ Calibration Procedures</h4>
                    <ul style="margin: 0; padding-left: 20px;">
                        <li>Execute calibration protocols</li>
                        <li>Use reference standards</li>
                        <li>Assess calibration results</li>
                        <li>Generate calibration certificates</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- Module 1: Device Operation -->
        <div id="device-operation" class="learning-module">
            <h2>Module 1: Medical Device Operation</h2>
            
            <div class="hands-on-activity">
                <h3>Activity 1.1: Patient Monitor Operation</h3>
                <p><strong>Objective:</strong> Learn to operate a multi-parameter patient monitor safely and effectively.</p>
                
                <div class="safety-note">
                    <h4>⚠️ Safety Precautions</h4>
                    <ul>
                        <li>Ensure device is properly grounded</li>
                        <li>Check all connections before powering on</li>
                        <li>Verify alarm settings are appropriate</li>
                        <li>Never bypass safety features</li>
                    </ul>
                </div>
                
                <h4>Equipment Required:</h4>
                <ul>
                    <li>Multi-parameter patient monitor (e.g., Philips IntelliVue MP70)</li>
                    <li>ECG electrodes and cables</li>
                    <li>Blood pressure cuff and tubing</li>
                    <li>Pulse oximetry sensor</li>
                    <li>Temperature probe</li>
                </ul>
                
                <h4>Step-by-Step Procedure:</h4>
                <ol>
                    <li><strong>Pre-operational Check:</strong>
                        <ul>
                            <li>Inspect device for physical damage</li>
                            <li>Verify power cord integrity</li>
                            <li>Check all cables and accessories</li>
                            <li>Ensure proper ventilation clearance</li>
                        </ul>
                    </li>
                    <li><strong>Power-up Sequence:</strong>
                        <ul>
                            <li>Connect to appropriate power source</li>
                            <li>Press power button and observe startup sequence</li>
                            <li>Wait for self-test completion</li>
                            <li>Verify all modules are recognized</li>
                        </ul>
                    </li>
                    <li><strong>Parameter Setup:</strong>
                        <ul>
                            <li>Configure patient demographics</li>
                            <li>Set appropriate alarm limits</li>
                            <li>Select monitoring parameters</li>
                            <li>Calibrate sensors if required</li>
                        </ul>
                    </li>
                </ol>
                
                <div class="device-simulator">
                    <h4>PATIENT MONITOR DISPLAY</h4>
                    <div style="margin: 20px 0;">
                        HR: 75 bpm | BP: 120/80 mmHg | SpO2: 98% | Temp: 37.2°C
                    </div>
                    <div style="font-size: 0.8em;">
                        ▲ ECG Lead II | 25 mm/s | 10 mm/mV ▲
                    </div>
                </div>
            </div>

            <div class="hands-on-activity">
                <h3>Activity 1.2: Ventilator Operation</h3>
                <p><strong>Objective:</strong> Understand mechanical ventilator operation and parameter adjustment.</p>

                <h4>Equipment Required:</h4>
                <ul>
                    <li>Mechanical ventilator (e.g., Dräger Evita V500)</li>
                    <li>Test lung or lung simulator</li>
                    <li>Ventilator circuit and filters</li>
                    <li>Flow analyzer</li>
                </ul>

                <h4>Key Parameters to Monitor:</h4>
                <table class="measurement-table">
                    <thead>
                        <tr>
                            <th>Parameter</th>
                            <th>Normal Range</th>
                            <th>Units</th>
                            <th>Clinical Significance</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Tidal Volume (VT)</td>
                            <td>6-8 mL/kg</td>
                            <td>mL</td>
                            <td>Volume delivered per breath</td>
                        </tr>
                        <tr>
                            <td>Respiratory Rate (RR)</td>
                            <td>12-20</td>
                            <td>breaths/min</td>
                            <td>Breathing frequency</td>
                        </tr>
                        <tr>
                            <td>PEEP</td>
                            <td>5-15</td>
                            <td>cmH2O</td>
                            <td>Positive end-expiratory pressure</td>
                        </tr>
                        <tr>
                            <td>Peak Pressure</td>
                            <td>15-30</td>
                            <td>cmH2O</td>
                            <td>Maximum airway pressure</td>
                        </tr>
                        <tr>
                            <td>FiO2</td>
                            <td>21-100</td>
                            <td>%</td>
                            <td>Fraction of inspired oxygen</td>
                        </tr>
                    </tbody>
                </table>

                <div class="form-group">
                    <label>Record your ventilator settings and observations:</label>
                    <textarea placeholder="Document mode settings, measured parameters, and any alarms encountered..."></textarea>
                </div>
            </div>
        </div>

        <!-- Module 2: Measurement Techniques -->
        <div id="measurement-techniques" class="learning-module">
            <h2>Module 2: Precision Measurement Techniques</h2>

            <div class="measurement-exercise">
                <h3>Exercise 2.1: Blood Pressure Measurement Accuracy</h3>
                <p><strong>Objective:</strong> Assess the accuracy of non-invasive blood pressure monitors using reference standards.</p>

                <h4>Equipment Required:</h4>
                <ul>
                    <li>Non-invasive blood pressure monitor (Device Under Test)</li>
                    <li>Pressure calibrator (Reference Standard)</li>
                    <li>Pressure cuff and connecting tubing</li>
                    <li>Leak tester</li>
                    <li>Calibration documentation forms</li>
                </ul>

                <h4>Measurement Protocol:</h4>
                <ol>
                    <li><strong>Pre-test Setup:</strong>
                        <ul>
                            <li>Connect pressure calibrator to BP monitor</li>
                            <li>Verify all connections are leak-free</li>
                            <li>Allow equipment to warm up (15 minutes minimum)</li>
                            <li>Record environmental conditions</li>
                        </ul>
                    </li>
                    <li><strong>Measurement Points:</strong>
                        <ul>
                            <li>Test at 0, 50, 100, 150, 200, 250 mmHg</li>
                            <li>Perform 3 measurements at each point</li>
                            <li>Record both reference and device readings</li>
                            <li>Calculate error and percent error</li>
                        </ul>
                    </li>
                </ol>

                <h4>Data Recording Table:</h4>
                <table class="measurement-table">
                    <thead>
                        <tr>
                            <th>Test Point</th>
                            <th>Reference (mmHg)</th>
                            <th>Device Reading (mmHg)</th>
                            <th>Error (mmHg)</th>
                            <th>% Error</th>
                            <th>Pass/Fail</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>1</td>
                            <td>0.0</td>
                            <td>___</td>
                            <td>___</td>
                            <td>___</td>
                            <td>___</td>
                        </tr>
                        <tr>
                            <td>2</td>
                            <td>50.0</td>
                            <td>___</td>
                            <td>___</td>
                            <td>___</td>
                            <td>___</td>
                        </tr>
                        <tr>
                            <td>3</td>
                            <td>100.0</td>
                            <td>___</td>
                            <td>___</td>
                            <td>___</td>
                            <td>___</td>
                        </tr>
                        <tr>
                            <td>4</td>
                            <td>150.0</td>
                            <td>___</td>
                            <td>___</td>
                            <td>___</td>
                            <td>___</td>
                        </tr>
                        <tr>
                            <td>5</td>
                            <td>200.0</td>
                            <td>___</td>
                            <td>___</td>
                            <td>___</td>
                            <td>___</td>
                        </tr>
                    </tbody>
                </table>

                <div class="form-group">
                    <label>Acceptance Criteria: ±3 mmHg or ±2% of reading (whichever is greater)</label>
                    <textarea placeholder="Calculate overall device accuracy and determine pass/fail status..."></textarea>
                </div>

                <!-- Interactive Error Calculator -->
                <div style="background: #f8f9ff; padding: 20px; border-radius: 8px; margin: 20px 0;">
                    <h4>📱 Interactive Error Calculator</h4>
                    <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px; align-items: end;">
                        <div>
                            <label>Reference Value (mmHg):</label>
                            <input type="number" id="reference-value" step="0.1" placeholder="100.0">
                        </div>
                        <div>
                            <label>Device Reading (mmHg):</label>
                            <input type="number" id="device-value" step="0.1" placeholder="101.5">
                        </div>
                        <div>
                            <button onclick="calculateError()" style="width: 100%;">Calculate</button>
                        </div>
                    </div>
                    <div style="margin-top: 15px; display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                        <div>
                            <strong>Error:</strong> <span id="error-result">---</span> mmHg
                        </div>
                        <div>
                            <strong>% Error:</strong> <span id="percent-error-result">---</span>%
                        </div>
                    </div>
                </div>
            </div>

            <div class="measurement-exercise">
                <h3>Exercise 2.2: Temperature Measurement and Uncertainty Analysis</h3>
                <p><strong>Objective:</strong> Perform temperature measurements with uncertainty calculation according to GUM principles.</p>

                <h4>Equipment Required:</h4>
                <ul>
                    <li>Digital thermometer (Device Under Test)</li>
                    <li>Calibrated reference thermometer</li>
                    <li>Temperature calibrator or water bath</li>
                    <li>Insulated container</li>
                    <li>Stirrer for uniform temperature</li>
                </ul>

                <h4>Uncertainty Budget Components:</h4>
                <table class="measurement-table">
                    <thead>
                        <tr>
                            <th>Source of Uncertainty</th>
                            <th>Type</th>
                            <th>Value</th>
                            <th>Distribution</th>
                            <th>Divisor</th>
                            <th>Standard Uncertainty</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Repeatability</td>
                            <td>A</td>
                            <td>±0.05°C</td>
                            <td>Normal</td>
                            <td>√n</td>
                            <td>±0.016°C</td>
                        </tr>
                        <tr>
                            <td>Reference Standard</td>
                            <td>B</td>
                            <td>±0.03°C</td>
                            <td>Normal</td>
                            <td>2</td>
                            <td>±0.015°C</td>
                        </tr>
                        <tr>
                            <td>Resolution</td>
                            <td>B</td>
                            <td>±0.01°C</td>
                            <td>Rectangular</td>
                            <td>√3</td>
                            <td>±0.006°C</td>
                        </tr>
                        <tr>
                            <td>Stability</td>
                            <td>B</td>
                            <td>±0.02°C</td>
                            <td>Rectangular</td>
                            <td>√3</td>
                            <td>±0.012°C</td>
                        </tr>
                    </tbody>
                </table>

                <div style="background: #f8f9ff; padding: 15px; border-radius: 8px; margin: 20px 0;">
                    <h4>Uncertainty Calculation:</h4>
                    <p><strong>Combined Standard Uncertainty:</strong></p>
                    <p>u<sub>c</sub> = √(u<sub>A</sub>² + u<sub>B1</sub>² + u<sub>B2</sub>² + u<sub>B3</sub>²)</p>
                    <p>u<sub>c</sub> = √(0.016² + 0.015² + 0.006² + 0.012²) = ±0.024°C</p>

                    <p><strong>Expanded Uncertainty (k=2, 95% confidence):</strong></p>
                    <p>U = k × u<sub>c</sub> = 2 × 0.024 = ±0.048°C</p>
                </div>

                <!-- Interactive Uncertainty Calculator -->
                <div style="background: #f0f9e8; padding: 20px; border-radius: 8px; margin: 20px 0;">
                    <h4>🧮 Interactive Uncertainty Calculator</h4>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                        <div>
                            <label>Type A (Repeatability):</label>
                            <input type="number" id="type-a" step="0.001" placeholder="0.016">
                        </div>
                        <div>
                            <label>Type B1 (Reference):</label>
                            <input type="number" id="type-b1" step="0.001" placeholder="0.015">
                        </div>
                        <div>
                            <label>Type B2 (Resolution):</label>
                            <input type="number" id="type-b2" step="0.001" placeholder="0.006">
                        </div>
                        <div>
                            <label>Type B3 (Stability):</label>
                            <input type="number" id="type-b3" step="0.001" placeholder="0.012">
                        </div>
                    </div>
                    <div style="text-align: center; margin: 20px 0;">
                        <button onclick="calculateUncertainty()">Calculate Uncertainty</button>
                    </div>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; text-align: center;">
                        <div style="background: white; padding: 15px; border-radius: 6px;">
                            <strong>Combined Standard Uncertainty:</strong><br>
                            ±<span id="combined-uncertainty">---</span> °C
                        </div>
                        <div style="background: white; padding: 15px; border-radius: 6px;">
                            <strong>Expanded Uncertainty (k=2):</strong><br>
                            ±<span id="expanded-uncertainty">---</span> °C
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Module 3: Calibration Procedures -->
        <div id="calibration-procedures" class="learning-module">
            <h2>Module 3: Systematic Calibration Procedures</h2>

            <div class="calibration-lab">
                <h3>Lab 3.1: Pulse Oximeter Calibration</h3>
                <p><strong>Objective:</strong> Calibrate pulse oximeter using functional tester with known saturation values.</p>

                <h4>Equipment Required:</h4>
                <ul>
                    <li>Pulse oximeter (Device Under Test)</li>
                    <li>Pulse oximeter tester (e.g., Fluke Index 2XL)</li>
                    <li>Calibration cables and adapters</li>
                    <li>Documentation forms</li>
                </ul>

                <h4>Calibration Points:</h4>
                <table class="measurement-table">
                    <thead>
                        <tr>
                            <th>Test Point</th>
                            <th>Reference SpO2 (%)</th>
                            <th>Device Reading (%)</th>
                            <th>Error (%)</th>
                            <th>Tolerance (±%)</th>
                            <th>Result</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>1</td>
                            <td>70</td>
                            <td>___</td>
                            <td>___</td>
                            <td>±3</td>
                            <td>___</td>
                        </tr>
                        <tr>
                            <td>2</td>
                            <td>80</td>
                            <td>___</td>
                            <td>___</td>
                            <td>±3</td>
                            <td>___</td>
                        </tr>
                        <tr>
                            <td>3</td>
                            <td>90</td>
                            <td>___</td>
                            <td>___</td>
                            <td>±2</td>
                            <td>___</td>
                        </tr>
                        <tr>
                            <td>4</td>
                            <td>95</td>
                            <td>___</td>
                            <td>___</td>
                            <td>±2</td>
                            <td>___</td>
                        </tr>
                        <tr>
                            <td>5</td>
                            <td>99</td>
                            <td>___</td>
                            <td>___</td>
                            <td>±2</td>
                            <td>___</td>
                        </tr>
                    </tbody>
                </table>

                <div class="form-group">
                    <label>Calibration Certificate Information:</label>
                    <textarea placeholder="Record device information, calibration results, and recommendations..."></textarea>
                </div>
            </div>

            <div class="calibration-lab">
                <h3>Lab 3.2: Electrical Safety Testing</h3>
                <p><strong>Objective:</strong> Perform electrical safety tests on medical devices according to IEC 60601 standards.</p>

                <div class="safety-note">
                    <h4>⚠️ Critical Safety Warning</h4>
                    <p>Electrical safety testing involves high voltages and currents. Only qualified personnel should perform these tests. Always follow proper safety procedures and use appropriate PPE.</p>
                </div>

                <h4>Required Tests:</h4>
                <ol>
                    <li><strong>Earth Leakage Current Test</strong>
                        <ul>
                            <li>Maximum allowable: 500 μA (Type B), 100 μA (Type BF/CF)</li>
                            <li>Test voltage: 110% of rated voltage</li>
                            <li>Test duration: 60 seconds minimum</li>
                        </ul>
                    </li>
                    <li><strong>Enclosure Leakage Current Test</strong>
                        <ul>
                            <li>Maximum allowable: 100 μA (normal condition)</li>
                            <li>Maximum allowable: 500 μA (single fault condition)</li>
                            <li>Test with and without earth connection</li>
                        </ul>
                    </li>
                    <li><strong>Patient Leakage Current Test</strong>
                        <ul>
                            <li>Type B: 100 μA (normal), 500 μA (single fault)</li>
                            <li>Type BF: 100 μA (normal), 500 μA (single fault)</li>
                            <li>Type CF: 10 μA (normal), 50 μA (single fault)</li>
                        </ul>
                    </li>
                </ol>

                <h4>Test Results Documentation:</h4>
                <table class="measurement-table">
                    <thead>
                        <tr>
                            <th>Test Type</th>
                            <th>Measured Value</th>
                            <th>Limit</th>
                            <th>Pass/Fail</th>
                            <th>Notes</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Earth Leakage</td>
                            <td>___ μA</td>
                            <td>500 μA</td>
                            <td>___</td>
                            <td>___</td>
                        </tr>
                        <tr>
                            <td>Enclosure Leakage (Normal)</td>
                            <td>___ μA</td>
                            <td>100 μA</td>
                            <td>___</td>
                            <td>___</td>
                        </tr>
                        <tr>
                            <td>Enclosure Leakage (Fault)</td>
                            <td>___ μA</td>
                            <td>500 μA</td>
                            <td>___</td>
                            <td>___</td>
                        </tr>
                        <tr>
                            <td>Patient Leakage (Normal)</td>
                            <td>___ μA</td>
                            <td>___ μA</td>
                            <td>___</td>
                            <td>___</td>
                        </tr>
                        <tr>
                            <td>Patient Leakage (Fault)</td>
                            <td>___ μA</td>
                            <td>___ μA</td>
                            <td>___</td>
                            <td>___</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Module 4: Safety Protocols -->
        <div id="safety-protocols" class="learning-module">
            <h2>Module 4: Safety Protocols and Risk Management</h2>

            <div class="safety-note">
                <h3>4.1: Personal Protective Equipment (PPE)</h3>
                <h4>Required PPE for Clinical Engineering Activities:</h4>
                <ul>
                    <li><strong>Safety Glasses:</strong> Always when working with electrical equipment</li>
                    <li><strong>Insulated Gloves:</strong> For electrical safety testing (rated for test voltage)</li>
                    <li><strong>Lab Coat:</strong> Protection from chemical and biological hazards</li>
                    <li><strong>Closed-toe Shoes:</strong> Non-conductive soles preferred</li>
                    <li><strong>Anti-static Wrist Strap:</strong> When working with sensitive electronics</li>
                </ul>
            </div>

            <div class="hands-on-activity">
                <h3>Activity 4.1: Risk Assessment Exercise</h3>
                <p><strong>Scenario:</strong> You are tasked with calibrating a defibrillator in the cardiac catheterization lab.</p>

                <h4>Identify and Assess Risks:</h4>
                <table class="measurement-table">
                    <thead>
                        <tr>
                            <th>Hazard</th>
                            <th>Risk Level</th>
                            <th>Mitigation Strategy</th>
                            <th>Residual Risk</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>High voltage exposure</td>
                            <td>High</td>
                            <td>Use proper PPE, follow lockout/tagout</td>
                            <td>Low</td>
                        </tr>
                        <tr>
                            <td>Electrical shock</td>
                            <td>___</td>
                            <td>___</td>
                            <td>___</td>
                        </tr>
                        <tr>
                            <td>Equipment damage</td>
                            <td>___</td>
                            <td>___</td>
                            <td>___</td>
                        </tr>
                        <tr>
                            <td>Patient safety impact</td>
                            <td>___</td>
                            <td>___</td>
                            <td>___</td>
                        </tr>
                    </tbody>
                </table>

                <div class="form-group">
                    <label>Develop a safety checklist for this procedure:</label>
                    <textarea placeholder="List all safety steps and precautions..."></textarea>
                </div>
            </div>
        </div>

        <!-- Module 5: Practical Exercises -->
        <div id="practical-exercises" class="learning-module">
            <h2>Module 5: Comprehensive Practical Exercises</h2>

            <div class="hands-on-activity">
                <h3>Exercise 5.1: Complete Device Assessment</h3>
                <p><strong>Objective:</strong> Perform a comprehensive assessment of a medical device including operation, measurement, and calibration.</p>

                <h4>Device Selection:</h4>
                <div class="form-group">
                    <label>Choose a device for assessment:</label>
                    <select>
                        <option value="">Select device type</option>
                        <option value="infusion-pump">Infusion Pump</option>
                        <option value="patient-monitor">Patient Monitor</option>
                        <option value="ventilator">Mechanical Ventilator</option>
                        <option value="defibrillator">Defibrillator</option>
                        <option value="electrosurgery">Electrosurgery Unit</option>
                    </select>
                </div>

                <h4>Assessment Protocol:</h4>
                <ol>
                    <li><strong>Visual Inspection (15 minutes)</strong>
                        <ul>
                            <li>Physical condition assessment</li>
                            <li>Cable and accessory inspection</li>
                            <li>Label and marking verification</li>
                            <li>Documentation review</li>
                        </ul>
                    </li>
                    <li><strong>Operational Testing (30 minutes)</strong>
                        <ul>
                            <li>Power-up sequence verification</li>
                            <li>User interface functionality</li>
                            <li>Alarm system testing</li>
                            <li>Basic parameter verification</li>
                        </ul>
                    </li>
                    <li><strong>Performance Verification (45 minutes)</strong>
                        <ul>
                            <li>Accuracy measurements</li>
                            <li>Calibration verification</li>
                            <li>Safety testing</li>
                            <li>Documentation completion</li>
                        </ul>
                    </li>
                </ol>

                <h4>Assessment Results:</h4>
                <div class="form-group">
                    <label>Device Information:</label>
                    <textarea placeholder="Manufacturer, model, serial number, software version..."></textarea>
                </div>

                <div class="form-group">
                    <label>Visual Inspection Results:</label>
                    <textarea placeholder="Document any physical damage, wear, or concerns..."></textarea>
                </div>

                <div class="form-group">
                    <label>Operational Test Results:</label>
                    <textarea placeholder="Record functionality test results and any issues..."></textarea>
                </div>

                <div class="form-group">
                    <label>Performance Measurements:</label>
                    <textarea placeholder="Document accuracy measurements and calibration results..."></textarea>
                </div>

                <div class="form-group">
                    <label>Overall Assessment and Recommendations:</label>
                    <textarea placeholder="Provide overall device status and any recommended actions..."></textarea>
                </div>
            </div>

            <div class="measurement-exercise">
                <h3>Exercise 5.2: Measurement Uncertainty Workshop</h3>
                <p><strong>Objective:</strong> Calculate measurement uncertainty for a real measurement scenario using GUM methodology.</p>

                <h4>Scenario:</h4>
                <p>You are measuring the flow rate of an infusion pump using a gravimetric method. Calculate the measurement uncertainty.</p>

                <h4>Given Data:</h4>
                <ul>
                    <li>Set flow rate: 10.0 mL/hr</li>
                    <li>Collection time: 60 minutes</li>
                    <li>Collected volume: 10.05 mL (measured 5 times)</li>
                    <li>Balance resolution: 0.01 mL</li>
                    <li>Balance calibration uncertainty: ±0.02 mL (k=2)</li>
                    <li>Timer uncertainty: ±0.1 s (k=2)</li>
                    <li>Temperature variation: ±2°C</li>
                </ul>

                <div class="form-group">
                    <label>Calculate Type A uncertainty (repeatability):</label>
                    <textarea placeholder="Show your calculation steps..."></textarea>
                </div>

                <div class="form-group">
                    <label>Calculate Type B uncertainties:</label>
                    <textarea placeholder="Calculate uncertainties for balance, timer, and temperature..."></textarea>
                </div>

                <div class="form-group">
                    <label>Calculate combined standard uncertainty:</label>
                    <textarea placeholder="Show the root sum of squares calculation..."></textarea>
                </div>

                <div class="form-group">
                    <label>Calculate expanded uncertainty (k=2):</label>
                    <textarea placeholder="Final uncertainty result..."></textarea>
                </div>
            </div>
        </div>

        <!-- Assessment and Certification -->
        <div class="section">
            <h2>🏆 Assessment and Certification</h2>

            <div style="background: linear-gradient(135deg, #e8f5e9 0%, #c8e6c9 100%); padding: 25px; border-radius: 12px; margin: 20px 0;">
                <h3>Competency Checklist</h3>
                <p>Complete all activities and demonstrate competency in the following areas:</p>

                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                    <div>
                        <h4>Device Operation (25 points)</h4>
                        <ul>
                            <li>☐ Safe device startup and shutdown</li>
                            <li>☐ Parameter configuration</li>
                            <li>☐ Alarm management</li>
                            <li>☐ Troubleshooting basic issues</li>
                            <li>☐ Documentation completion</li>
                        </ul>
                    </div>
                    <div>
                        <h4>Measurement Techniques (25 points)</h4>
                        <ul>
                            <li>☐ Proper test equipment use</li>
                            <li>☐ Accurate data collection</li>
                            <li>☐ Error calculation</li>
                            <li>☐ Uncertainty analysis</li>
                            <li>☐ Results interpretation</li>
                        </ul>
                    </div>
                    <div>
                        <h4>Calibration Procedures (25 points)</h4>
                        <ul>
                            <li>☐ Protocol execution</li>
                            <li>☐ Reference standard use</li>
                            <li>☐ Pass/fail determination</li>
                            <li>☐ Certificate generation</li>
                            <li>☐ Traceability documentation</li>
                        </ul>
                    </div>
                    <div>
                        <h4>Safety and Professionalism (25 points)</h4>
                        <ul>
                            <li>☐ PPE usage</li>
                            <li>☐ Risk assessment</li>
                            <li>☐ Safety protocol adherence</li>
                            <li>☐ Professional communication</li>
                            <li>☐ Ethical considerations</li>
                        </ul>
                    </div>
                </div>

                <div style="text-align: center; margin-top: 30px;">
                    <p><strong>Minimum passing score: 80/100 points</strong></p>
                    <p>Current Score: <span id="total-score">0</span>/100 | Status: <span id="assessment-result">NOT STARTED</span></p>
                    <button onclick="submitAssessment()" style="background: #4caf50; font-size: 1.2em; padding: 15px 30px;">Submit for Assessment</button>
                </div>
            </div>
        </div>

        <!-- Interactive Device Simulator -->
        <div class="section">
            <h2>🖥️ Virtual Device Simulator</h2>

            <div style="background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%); padding: 25px; border-radius: 12px;">
                <h3>Practice Device Readings</h3>
                <p>Use this simulator to practice interpreting device readings and calculating errors.</p>

                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 20px 0;">
                    <button onclick="simulateDeviceReading('bp')" style="background: #1976d2;">Blood Pressure</button>
                    <button onclick="simulateDeviceReading('temp')" style="background: #388e3c;">Temperature</button>
                    <button onclick="simulateDeviceReading('spo2')" style="background: #f57c00;">Pulse Oximetry</button>
                    <button onclick="simulateDeviceReading('flow')" style="background: #7b1fa2;">Flow Rate</button>
                </div>

                <div class="device-simulator">
                    <h4>DEVICE READING DISPLAY</h4>
                    <div style="font-size: 2em; margin: 20px 0;">
                        <span id="simulated-reading">Click a button to simulate</span>
                    </div>
                    <div style="font-size: 0.9em;">
                        Practice calculating errors and determining pass/fail status
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Reference Guide -->
        <div class="section">
            <h2>📚 Quick Reference Guide</h2>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                <div style="background: #fff3e0; padding: 20px; border-radius: 8px;">
                    <h4>Common Formulas</h4>
                    <ul style="margin: 0; padding-left: 20px; font-family: 'Courier New', monospace;">
                        <li>Error = Device - Reference</li>
                        <li>% Error = (Error / Reference) × 100</li>
                        <li>u<sub>c</sub> = √(u<sub>A</sub>² + u<sub>B1</sub>² + u<sub>B2</sub>² + ...)</li>
                        <li>U = k × u<sub>c</sub> (k=2 for 95% CI)</li>
                    </ul>
                </div>

                <div style="background: #e8f5e9; padding: 20px; border-radius: 8px;">
                    <h4>Typical Tolerances</h4>
                    <ul style="margin: 0; padding-left: 20px;">
                        <li>Blood Pressure: ±3 mmHg or ±2%</li>
                        <li>Temperature: ±0.1°C</li>
                        <li>SpO2: ±2% (90-100%), ±3% (70-89%)</li>
                        <li>Flow Rate: ±5% or ±0.1 mL/hr</li>
                    </ul>
                </div>

                <div style="background: #e3f2fd; padding: 20px; border-radius: 8px;">
                    <h4>Safety Limits (IEC 60601)</h4>
                    <ul style="margin: 0; padding-left: 20px;">
                        <li>Earth Leakage: 500 μA (Type B)</li>
                        <li>Enclosure Leakage: 100 μA (normal)</li>
                        <li>Patient Leakage: 10 μA (Type CF)</li>
                        <li>Applied Part: Type B/BF/CF</li>
                    </ul>
                </div>

                <div style="background: #fce4ec; padding: 20px; border-radius: 8px;">
                    <h4>Documentation Requirements</h4>
                    <ul style="margin: 0; padding-left: 20px;">
                        <li>Device identification</li>
                        <li>Test procedures used</li>
                        <li>Measurement results</li>
                        <li>Pass/fail determination</li>
                        <li>Technician signature</li>
                        <li>Date and time</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <footer>
        <p>&copy; 2025 Clinical Engineering Hands-on Learning | Dr. Mohammed Yagoub Esmail</p>
        <p>Nahda College | All Rights Reserved</p>
    </footer>

    <script>
        // Navigation functionality
        function showSection(sectionId) {
            // Remove active class from all nav links
            document.querySelectorAll('.nav-link').forEach(link => {
                link.classList.remove('active');
            });

            // Add active class to clicked link
            event.target.classList.add('active');

            // Scroll to section
            document.getElementById(sectionId).scrollIntoView({
                behavior: 'smooth'
            });
        }

        // Add click handlers to navigation links
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelectorAll('.nav-link').forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const sectionId = this.getAttribute('href').substring(1);
                    showSection(sectionId);
                });
            });
        });

        // Interactive measurement calculator
        function calculateError() {
            const reference = parseFloat(document.getElementById('reference-value').value);
            const device = parseFloat(document.getElementById('device-value').value);

            if (!isNaN(reference) && !isNaN(device)) {
                const error = device - reference;
                const percentError = (error / reference) * 100;

                document.getElementById('error-result').textContent = error.toFixed(2);
                document.getElementById('percent-error-result').textContent = percentError.toFixed(2);
            }
        }

        // Uncertainty calculator
        function calculateUncertainty() {
            const typeA = parseFloat(document.getElementById('type-a').value) || 0;
            const typeB1 = parseFloat(document.getElementById('type-b1').value) || 0;
            const typeB2 = parseFloat(document.getElementById('type-b2').value) || 0;
            const typeB3 = parseFloat(document.getElementById('type-b3').value) || 0;

            const combined = Math.sqrt(typeA*typeA + typeB1*typeB1 + typeB2*typeB2 + typeB3*typeB3);
            const expanded = combined * 2; // k=2

            document.getElementById('combined-uncertainty').textContent = combined.toFixed(4);
            document.getElementById('expanded-uncertainty').textContent = expanded.toFixed(4);
        }

        // Assessment scoring
        let totalScore = 0;
        function updateScore(category, points) {
            totalScore += points;
            document.getElementById('total-score').textContent = totalScore;

            if (totalScore >= 80) {
                document.getElementById('assessment-result').textContent = 'PASS';
                document.getElementById('assessment-result').style.color = '#4caf50';
            } else {
                document.getElementById('assessment-result').textContent = 'IN PROGRESS';
                document.getElementById('assessment-result').style.color = '#ff9800';
            }
        }

        // Form validation and submission
        function submitAssessment() {
            const checkboxes = document.querySelectorAll('input[type="checkbox"]:checked');
            const totalChecked = checkboxes.length;
            const maxPoints = 20; // 20 competency items
            const score = (totalChecked / maxPoints) * 100;

            if (score >= 80) {
                alert(`Congratulations! You have passed the assessment with a score of ${score.toFixed(1)}%. You are now certified in Medical Device Operation, Measurement, and Calibration.`);
            } else {
                alert(`Your current score is ${score.toFixed(1)}%. You need at least 80% to pass. Please complete more activities and try again.`);
            }
        }

        // Device simulator
        function simulateDeviceReading(deviceType) {
            let reading;
            switch(deviceType) {
                case 'bp':
                    reading = (Math.random() * 50 + 100).toFixed(0) + '/' + (Math.random() * 30 + 60).toFixed(0);
                    break;
                case 'temp':
                    reading = (Math.random() * 3 + 36).toFixed(1);
                    break;
                case 'spo2':
                    reading = (Math.random() * 5 + 95).toFixed(0);
                    break;
                case 'flow':
                    reading = (Math.random() * 2 + 9).toFixed(2);
                    break;
                default:
                    reading = 'N/A';
            }

            document.getElementById('simulated-reading').textContent = reading;
        }

        // Auto-save form data
        function autoSave() {
            const formData = {};
            document.querySelectorAll('textarea, input, select').forEach(element => {
                if (element.id) {
                    formData[element.id] = element.value;
                }
            });
            localStorage.setItem('handsOnLearningData', JSON.stringify(formData));
        }

        // Load saved form data
        function loadSavedData() {
            const savedData = localStorage.getItem('handsOnLearningData');
            if (savedData) {
                const formData = JSON.parse(savedData);
                Object.keys(formData).forEach(id => {
                    const element = document.getElementById(id);
                    if (element) {
                        element.value = formData[id];
                    }
                });
            }
        }

        // Initialize auto-save
        document.addEventListener('DOMContentLoaded', function() {
            loadSavedData();

            // Auto-save every 30 seconds
            setInterval(autoSave, 30000);

            // Save on form changes
            document.addEventListener('change', autoSave);
            document.addEventListener('input', autoSave);
        });

        // Print functionality
        function printSection(sectionId) {
            const section = document.getElementById(sectionId);
            const printWindow = window.open('', '_blank');
            printWindow.document.write(`
                <html>
                <head>
                    <title>Clinical Engineering Hands-on Learning</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 20px; }
                        table { border-collapse: collapse; width: 100%; }
                        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                        th { background-color: #f2f2f2; }
                    </style>
                </head>
                <body>
                    ${section.innerHTML}
                </body>
                </html>
            `);
            printWindow.document.close();
            printWindow.print();
        }
    </script>
</body>
</html>
