<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clinical Engineering Course | SUST - BME</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .lecture-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        .sidebar-link.active {
            border-left: 4px solid #3b82f6;
            background-color: #f3f4f6;
        }
        .content-section {
            transition: all 0.3s ease;
        }
        .hidden-section {
            display: none;
            opacity: 0;
        }
        .visible-section {
            display: block;
            opacity: 1;
        }
        .progress-bar {
            transition: width 0.6s ease;
        }
        .gradient-bg {
            background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
        }
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        .fade-in {
            animation: fadeIn 0.5s ease-in-out;
        }
    </style>
</head>
<body class="bg-gray-50 font-sans">
    <!-- Header -->
    <header class="gradient-bg text-white shadow-lg">
        <div class="container mx-auto px-4 py-6">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <div>
                    <h1 class="text-2xl md:text-3xl font-bold">Clinical Engineering Course</h1>
                    <p class="text-blue-200">SUST - BME | Nahda College <span class="hidden md:inline">•</span> <br class="md:hidden"> Year 2025</p>
                </div>
                <div class="mt-4 md:mt-0 text-sm md:text-base">
                    <p><i class="fas fa-user-graduate mr-2"></i> Dr. Mohammed Yagoub Esmail</p>
                    <p class="text-blue-200"><i class="fas fa-envelope mr-2"></i> <EMAIL></p>
                    <p><i class="fas fa-phone-alt mr-2"></i> +249912867327 | +966538076790</p>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Container -->
    <div class="container mx-auto px-4 py-8 flex flex-col md:flex-row">
        <!-- Sidebar -->
        <div class="w-full md:w-1/4 lg:w-1/5 mb-8 md:mb-0 md:mr-8">
            <div class="bg-white rounded-lg shadow-md p-6 sticky top-4">
                <h2 class="text-xl font-bold mb-6 text-gray-800 border-b pb-2">Course Weeks</h2>
                <ul class="space-y-2">
                    <li><a href="#" class="sidebar-link flex items-center py-2 px-3 rounded-md hover:bg-gray-100 transition" data-section="week1"><i class="fas fa-play-circle mr-3 text-blue-500"></i> Week 1: Introduction</a></li>
                    <li><a href="#" class="sidebar-link flex items-center py-2 px-3 rounded-md hover:bg-gray-100 transition" data-section="week2"><i class="fas fa-cogs mr-3 text-blue-500"></i> Week 2: Lifecycle</a></li>
                    <li><a href="#" class="sidebar-link flex items-center py-2 px-3 rounded-md hover:bg-gray-100 transition" data-section="week3"><i class="fas fa-clipboard-check mr-3 text-blue-500"></i> Week 3: Regulations</a></li>
                    <li><a href="#" class="sidebar-link flex items-center py-2 px-3 rounded-md hover:bg-gray-100 transition" data-section="week4"><i class="fas fa-shield-alt mr-3 text-blue-500"></i> Week 4: Safety</a></li>
                    <li><a href="#" class="sidebar-link flex items-center py-2 px-3 rounded-md hover:bg-gray-100 transition" data-section="week5"><i class="fas fa-network-wired mr-3 text-blue-500"></i> Week 5: Systems</a></li>
                    <li><a href="#" class="sidebar-link flex items-center py-2 px-3 rounded-md hover:bg-gray-100 transition" data-section="week6"><i class="fas fa-microscope mr-3 text-blue-500"></i> Week 6: Diagnostics</a></li>
                    <li><a href="#" class="sidebar-link flex items-center py-2 px-3 rounded-md hover:bg-gray-100 transition" data-section="week7"><i class="fas fa-heartbeat mr-3 text-blue-500"></i> Week 7: Therapeutics</a></li>
                    <li><a href="#" class="sidebar-link flex items-center py-2 px-3 rounded-md hover:bg-gray-100 transition" data-section="week8"><i class="fas fa-lock mr-3 text-blue-500"></i> Week 8: Cybersecurity</a></li>
                    <li><a href="#" class="sidebar-link flex items-center py-2 px-3 rounded-md hover:bg-gray-100 transition" data-section="week9"><i class="fas fa-robot mr-3 text-blue-500"></i> Week 9: Emerging Tech</a></li>
                    <li><a href="#" class="sidebar-link flex items-center py-2 px-3 rounded-md hover:bg-gray-100 transition" data-section="week10"><i class="fas fa-project-diagram mr-3 text-blue-500"></i> Week 10: Project Mgmt</a></li>
                    <li><a href="#" class="sidebar-link flex items-center py-2 px-3 rounded-md hover:bg-gray-100 transition" data-section="week11"><i class="fas fa-comments mr-3 text-blue-500"></i> Week 11: Communication</a></li>
                    <li><a href="#" class="sidebar-link flex items-center py-2 px-3 rounded-md hover:bg-gray-100 transition" data-section="week12"><i class="fas fa-trophy mr-3 text-blue-500"></i> Week 12: Capstone</a></li>
                </ul>

                <div class="mt-8 pt-4 border-t border-gray-200">
                    <h3 class="font-semibold mb-3">Student Progress</h3>
                    <div class="flex items-center justify-between mb-1">
                        <span class="text-sm text-gray-600">Course Completion</span>
                        <span class="text-sm font-medium">25%</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2.5">
                        <div class="progress-bar bg-blue-600 h-2.5 rounded-full" style="width: 25%"></div>
                    </div>
                    <div class="flex justify-between mt-4 space-x-4">
                        <button class="bg-blue-50 text-blue-700 px-4 py-2 rounded-lg text-sm flex items-center">
                            <i class="fas fa-book mr-2"></i> Materials
                        </button>
                        <button class="bg-green-50 text-green-700 px-4 py-2 rounded-lg text-sm flex items-center">
                            <i class="fas fa-tasks mr-2"></i> Assignments
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="w-full md:w-3/4 lg:w-4/5">
            <!-- Dashboard Overview -->
            <section id="dashboard" class="visible-section">
                <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                    <h2 class="text-2xl font-bold mb-4 text-gray-800">Welcome to Clinical Engineering</h2>
                    <p class="text-gray-600 mb-6">This course provides comprehensive training in the principles and practices of clinical engineering, preparing you for careers in healthcare technology management and medical device innovation.</p>
                    
                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                        <div class="bg-blue-50 p-5 rounded-lg border border-blue-100">
                            <h3 class="font-bold text-blue-800 mb-2 flex items-center"><i class="fas fa-graduation-cap mr-2"></i> Learning Outcomes</h3>
                            <ul class="text-sm text-gray-700 space-y-1">
                                <li><i class="fas fa-check-circle text-blue-500 mr-2"></i> Master medical technology lifecycle</li>
                                <li><i class="fas fa-check-circle text-blue-500 mr-2"></i> Apply regulatory requirements</li>
                                <li><i class="fas fa-check-circle text-blue-500 mr-2"></i> Ensure patient safety and cybersecurity</li>
                            </ul>
                        </div>
                        <div class="bg-purple-50 p-5 rounded-lg border border-purple-100">
                            <h3 class="font-bold text-purple-800 mb-2 flex items-center"><i class="fas fa-calendar-alt mr-2"></i> Course Structure</h3>
                            <ul class="text-sm text-gray-700 space-y-1">
                                <li><i class="fas fa-chalkboard text-purple-500 mr-2"></i> 12 Weekly Modules</li>
                                <li><i class="fas fa-flask text-purple-500 mr-2"></i> Hands-on Activities</li>
                                <li><i class="fas fa-file-alt text-purple-500 mr-2"></i> Capstone Project</li>
                            </ul>
                        </div>
                        <div class="bg-orange-50 p-5 rounded-lg border border-orange-100">
                            <h3 class="font-bold text-orange-800 mb-2 flex items-center"><i class="fas fa-award mr-2"></i> Certification</h3>
                            <ul class="text-sm text-gray-700 space-y-1">
                                <li><i class="fas fa-certificate text-orange-500 mr-2"></i> Earn course certificate</li>
                                <li><i class="fas fa-book-reader text-orange-500 mr-2"></i> Prep for CCE exam</li>
                                <li><i class="fas fa-network-wired text-orange-500 mr-2"></i> Professional networking</li>
                            </ul>
                        </div>
                    </div>

                    <h3 class="text-xl font-semibold mb-4 text-gray-800">Featured Lectures</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="lecture-card bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm transition cursor-pointer">
                            <div class="p-5">
                                <div class="flex justify-between items-start mb-3">
                                    <span class="bg-blue-100 text-blue-800 text-xs font-semibold px-2.5 py-0.5 rounded">Week 1</span>
                                    <span class="text-gray-500 text-sm">2hr 15min</span>
                                </div>
                                <h4 class="text-lg font-bold text-gray-900 mb-2">Introduction to Clinical Engineering</h4>
                                <p class="text-gray-600 text-sm mb-4">Learn the fundamental concepts, historical development, and professional roles in clinical engineering.</p>
                                <div class="flex items-center text-sm text-gray-500">
                                    <i class="fas fa-play-circle text-blue-500 mr-2"></i>
                                    <span>Begin Lecture</span>
                                </div>
                            </div>
                        </div>
                        <div class="lecture-card bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm transition cursor-pointer">
                            <div class="p-5">
                                <div class="flex justify-between items-start mb-3">
                                    <span class="bg-purple-100 text-purple-800 text-xs font-semibold px-2.5 py-0.5 rounded">Week 3</span>
                                    <span class="text-gray-500 text-sm">1hr 45min</span>
                                </div>
                                <h4 class="text-lg font-bold text-gray-900 mb-2">Regulatory Standards & Accreditation</h4>
                                <p class="text-gray-600 text-sm mb-4">Understand FDA classifications, IEC 60601 requirements, and quality management systems.</p>
                                <div class="flex items-center text-sm text-gray-500">
                                    <i class="fas fa-lock text-purple-500 mr-2"></i>
                                    <span>Available Week 3</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Week 1 Content -->
            <section id="week1" class="content-section hidden-section fade-in">
                <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                    <div class="flex justify-between items-center mb-4">
                        <h2 class="text-2xl font-bold text-gray-800">Week 1: Introduction to Clinical Engineering</h2>
                        <span class="bg-blue-100 text-blue-800 text-sm font-semibold px-3 py-1 rounded-full">Current</span>
                    </div>
                    
                    <div class="flex items-center text-gray-600 mb-6">
                        <i class="far fa-calendar-alt mr-2"></i>
                        <span class="text-sm">Released: Jan 10, 2025 • Due: Jan 17, 2025</span>
                    </div>
                    
                    <div class="bg-blue-50 border-l-4 border-blue-500 p-4 mb-6">
                        <h3 class="font-bold text-blue-800 flex items-center mb-2"><i class="fas fa-bullseye mr-2"></i> Learning Objectives</h3>
                        <ul class="list-disc list-inside text-gray-700 space-y-1">
                            <li>Define Clinical Engineering and explain its scope</li>
                            <li>Trace the field's evolution from early medical devices to today's smart systems</li>
                            <li>Recognize various roles in healthcare teams</li>
                            <li>Overview of modern clinical technologies and their impact on patient care</li>
                        </ul>
                    </div>
                    
                    <h3 class="text-xl font-semibold mt-6 mb-4 text-gray-800 flex items-center"><i class="fas fa-chalkboard-teacher mr-2"></i> Lecture Content</h3>
                    <div class="space-y-4">
                        <div class="border border-gray-200 rounded-lg p-4">
                            <h4 class="font-bold text-gray-800 mb-2">1. Definition & Scope</h4>
                            <p class="text-gray-700 mb-2">Clinical Engineering = applying engineering principles to healthcare technology—ensuring devices work safely, effectively, and affordably.</p>
                            <p class="text-gray-600 text-sm">Distinction from BMET (Biomedical Equipment Technician) vs. R&D engineers vs. Clinical Engineers.</p>
                        </div>
                        <div class="border border-gray-200 rounded-lg p-4">
                            <h4 class="font-bold text-gray-800 mb-2">2. Historical Milestones</h4>
                            <ul class="list-disc list-inside text-gray-700 space-y-1">
                                <li>Early 20th C: first X-ray machines → rise of radiology departments</li>
                                <li>1960s–70s: formalization of "Clinical Engineering" roles in US hospitals</li>
                                <li>1990s–2000s: digital imaging, PACS, and emergence of interoperability standards</li>
                                <li>2010s–present: AI diagnostics, remote monitoring, cybersecurity concerns</li>
                            </ul>
                        </div>
                        <div class="border border-gray-200 rounded-lg p-4">
                            <h4 class="font-bold text-gray-800 mb-2">3. Key Roles & Responsibilities</h4>
                            <ul class="list-disc list-inside text-gray-700 space-y-1">
                                <li>Tech Management: procurement, PM, repairs, decommissioning</li>
                                <li>Safety & Risk: root-cause analyses, incident investigations, user training</li>
                                <li>Systems Integration: device networking (HL7, DICOM), data workflows</li>
                                <li>Innovation & Strategy: evaluating new tech, ROI analyses, pilot programs</li>
                            </ul>
                        </div>
                        <div class="border border-gray-200 rounded-lg p-4">
                            <h4 class="font-bold text-gray-800 mb-2">4. Healthcare Technology Landscape</h4>
                            <div class="grid grid-cols-1 sm:grid-cols-2 gap-2">
                                <span class="bg-gray-100 px-3 py-1 rounded-full text-sm">Imaging (X-ray → MRI → CT)</span>
                                <span class="bg-gray-100 px-3 py-1 rounded-full text-sm">Monitoring (ECG, pulse oximetry, ICU dashboards)</span>
                                <span class="bg-gray-100 px-3 py-1 rounded-full text-sm">Therapeutics (ventilators, infusion pumps)</span>
                                <span class="bg-gray-100 px-3 py-1 rounded-full text-sm">Digital Health (telemedicine, wearables, decision-support software)</span>
                            </div>
                        </div>
                    </div>
                    
                    <h3 class="text-xl font-semibold mt-8 mb-4 text-gray-800 flex items-center"><i class="fas fa-users mr-2"></i> In-Class Activity</h3>
                    <div class="bg-purple-50 border-l-4 border-purple-500 p-4 mb-6 rounded-lg">
                        <h4 class="font-bold text-purple-800 mb-2">Role Discussion</h4>
                        <p class="text-gray-700 mb-3">Small groups (3–4 students) will discuss and analyze medical devices from personal experience to understand Clinical Engineering challenges.</p>
                        
                        <details class="mb-2">
                            <summary class="font-medium text-purple-700 cursor-pointer">Activity Details</summary>
                            <div class="mt-2 pl-4">
                                <p class="text-sm text-gray-700 mb-2">Prompt: "Identify a medical device in your own experience (clinic visit, family member's care, etc.). What challenges might a Clinical Engineer face managing that device?"</p>
                                <p class="text-sm font-medium text-gray-800">Deliverable:</p>
                                <ol class="list-decimal list-inside text-sm text-gray-700 space-y-1">
                                    <li>A 2-minute summary per group highlighting:
                                        <ul class="list-disc list-inside pl-4">
                                            <li>Key device functions</li>
                                            <li>Potential safety risks or lifecycle challenges</li>
                                            <li>One innovative improvement you'd propose</li>
                                        </ul>
                                    </li>
                                    <li>Class-wide discussion to compare insights</li>
                                </ol>
                            </div>
                        </details>
                    </div>
                    
                    <h3 class="text-xl font-semibold mt-8 mb-4 text-gray-800 flex items-center"><i class="fas fa-book-reader mr-2"></i> Preparatory & Follow-Up Work</h3>
                    <div class="space-y-4">
                        <div class="border border-gray-200 rounded-lg p-4">
                            <h4 class="font-bold text-gray-800 mb-2 flex items-center"><i class="fas fa-book-open mr-2"></i> Pre-read</h4>
                            <p class="text-gray-700">Chapter 1 of <span class="font-semibold">The Clinical Engineering Handbook</span> (Dyro)—focus on definitions and history.</p>
                            <button class="mt-2 bg-blue-100 hover:bg-blue-200 text-blue-800 text-sm px-3 py-1 rounded flex items-center">
                                <i class="fas fa-download mr-2"></i> Download Reading
                            </button>
                        </div>
                        <div class="border border-gray-200 rounded-lg p-4">
                            <h4 class="font-bold text-gray-800 mb-2 flex items-center"><i class="fas fa-question-circle mr-2"></i> Follow-up Quiz</h4>
                            <p class="text-gray-700 mb-2">Short online quiz to ensure you can:</p>
                            <ul class="list-disc list-inside text-gray-700 space-y-1">
                                <li>Accurately define Clinical Engineering</li>
                                <li>List four core responsibilities</li>
                                <li>Recall two major historical milestones</li>
                            </ul>
                            <div class="mt-3 flex justify-between items-center">
                                <span class="text-sm text-gray-500"><i class="far fa-clock mr-1"></i> Due: Jan 17, 2025</span>
                                <button class="bg-green-100 hover:bg-green-200 text-green-800 text-sm px-3 py-1 rounded flex items-center">
                                    <i class="fas fa-pencil-alt mr-2"></i> Start Quiz
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-8 pt-4 border-t border-gray-200">
                        <h3 class="text-lg font-semibold mb-3 text-gray-800">Lecture Resources</h3>
                        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                            <div class="border border-gray-200 rounded-lg p-3 flex items-start">
                                <div class="bg-blue-100 p-2 rounded-lg mr-3">
                                    <i class="fas fa-file-pdf text-blue-600"></i>
                                </div>
                                <div>
                                    <p class="font-medium text-gray-800">Lecture Slides (PDF)</p>
                                    <p class="text-xs text-gray-500">2.4 MB</p>
                                </div>
                            </div>
                            <div class="border border-gray-200 rounded-lg p-3 flex items-start">
                                <div class="bg-purple-100 p-2 rounded-lg mr-3">
                                    <i class="fas fa-film text-purple-600"></i>
                                </div>
                                <div>
                                    <p class="font-medium text-gray-800">Lecture Recording</p>
                                    <p class="text-xs text-gray-500">2hr 15min</p>
                                </div>
                            </div>
                            <div class="border border-gray-200 rounded-lg p-3 flex items-start">
                                <div class="bg-green-100 p-2 rounded-lg mr-3">
                                    <i class="fas fa-link text-green-600"></i>
                                </div>
                                <div>
                                    <p class="font-medium text-gray-800">Additional Resources</p>
                                    <p class="text-xs text-gray-500">Web links</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Week 2-12 content would go here (omitted for brevity in this example) -->
            <section id="week2" class="content-section hidden-section fade-in">
                <!-- Week 2 content structure similar to Week 1 -->
            </section>
            
            <!-- Footer -->
            <footer class="mt-12 text-center text-gray-600 text-sm">
                <p>© 2025 Clinical Engineering Course. All Rights Reserved.</p>
                <p class="mt-1">Developed by Dr. Mohammed Yagoub Esmail | SUST - BME | Nahda College</p>
            </footer>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Set initial active link
            const initialLink = document.querySelector('[data-section="week1"]');
            initialLink.classList.add('active');
            
            // Handle sidebar navigation
            const sidebarLinks = document.querySelectorAll('.sidebar-link');
            const contentSections = document.querySelectorAll('.content-section');
            
            sidebarLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    
                    // Remove active class from all links
                    sidebarLinks.forEach(l => l.classList.remove('active'));
                    
                    // Add active class to clicked link
                    this.classList.add('active');
                    
                    // Hide all content sections
                    contentSections.forEach(section => {
                        section.classList.remove('visible-section');
                        section.classList.add('hidden-section');
                    });
                    
                    // Show selected content section
                    const sectionId = this.getAttribute('data-section');
                    const activeSection = document.getElementById(sectionId);
                    activeSection.classList.remove('hidden-section');
                    activeSection.classList.add('visible-section');
                });
            });
            
            // Simulate loading animation when switching sections
            contentSections.forEach(section => {
                section.addEventListener('transitionend', function() {
                    if (this.classList.contains('visible-section')) {
                        this.classList.add('fade-in');
                    }
                });
            });
        });
    </script>
</body>
</html>