<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Model Answers - Clinical Engineering | الإجابات النموذجية - الهندسة السريرية</title>
    <style>
        body {
            font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
            line-height: 1.8;
            color: #333;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            direction: ltr;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        header {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            color: white;
            padding: 40px 0;
            text-align: center;
            border-radius: 15px;
            margin-bottom: 30px;
            position: relative;
            overflow: hidden;
        }
        
        header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 70% 30%, rgba(255,255,255,0.1) 0%, transparent 50%);
        }
        
        .header-content {
            position: relative;
            z-index: 1;
        }
        
        .lecture-category {
            background: white;
            border-radius: 15px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
            transition: all 0.3s ease;
        }
        
        .lecture-category:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
        }
        
        .category-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px 30px;
            position: relative;
        }
        
        .category-title {
            font-size: 1.8rem;
            font-weight: 600;
            margin: 0 0 10px 0;
        }
        
        .category-subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            margin: 0;
            font-weight: 300;
        }
        
        .category-stats {
            position: absolute;
            top: 50%;
            right: 30px;
            transform: translateY(-50%);
            text-align: center;
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            line-height: 1;
        }
        
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.8;
        }
        
        .answers-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 25px;
            padding: 30px;
        }
        
        .answer-card {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 25px;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .answer-card:hover {
            border-color: #667eea;
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
        }
        
        .lecture-number {
            position: absolute;
            top: -15px;
            left: 20px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 1.2rem;
        }
        
        .lecture-title {
            font-size: 1.4rem;
            font-weight: 600;
            color: #2c3e50;
            margin: 20px 0 15px 0;
        }
        
        .lecture-description {
            color: #495057;
            margin-bottom: 20px;
            line-height: 1.6;
        }
        
        .activities-section {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid #2196f3;
        }
        
        .activities-title {
            color: #1976d2;
            margin: 0 0 15px 0;
            font-size: 1.1rem;
            font-weight: 600;
        }
        
        .activity-item {
            background: rgba(255,255,255,0.8);
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 12px;
            border-left: 3px solid #2196f3;
        }
        
        .activity-title {
            font-weight: 600;
            color: #1976d2;
            margin-bottom: 8px;
        }
        
        .activity-answer {
            color: #495057;
            font-size: 0.95rem;
            line-height: 1.6;
        }
        
        .key-points {
            background: linear-gradient(135deg, #e8f5e9 0%, #c8e6c9 100%);
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid #4caf50;
        }
        
        .key-points h5 {
            color: #2e7d32;
            margin: 0 0 15px 0;
            font-size: 1.1rem;
        }
        
        .key-points ul {
            margin: 0;
            padding-left: 20px;
        }
        
        .key-points li {
            margin-bottom: 8px;
            color: #495057;
            font-size: 0.95rem;
        }
        
        .answer-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            justify-content: center;
        }
        
        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            font-size: 0.9rem;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        }
        
        .btn-success {
            background: linear-gradient(45deg, #28a745, #20c997);
        }
        
        .btn-warning {
            background: linear-gradient(45deg, #ffc107, #ffb300);
        }
        
        .btn-info {
            background: linear-gradient(45deg, #17a2b8, #138496);
        }
        
        .search-container {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .search-input {
            width: 100%;
            padding: 15px 20px;
            border: 2px solid #e9ecef;
            border-radius: 25px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        
        .search-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .filter-buttons {
            display: flex;
            gap: 10px;
            margin-top: 15px;
            flex-wrap: wrap;
            justify-content: center;
        }
        
        .filter-btn {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            color: #495057;
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }
        
        .filter-btn:hover,
        .filter-btn.active {
            background: #667eea;
            border-color: #667eea;
            color: white;
        }
        
        .home-btn {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 16px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: 600;
            font-size: 0.9em;
            line-height: 1.2;
            text-align: center;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
            transition: all 0.3s ease;
            z-index: 1000;
            min-width: 80px;
        }
        
        .home-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);
        }
        
        .language-switch {
            position: fixed;
            bottom: 20px;
            left: 20px;
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            border: none;
            padding: 12px 16px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: 600;
            font-size: 0.9em;
            line-height: 1.2;
            text-align: center;
            box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3);
            transition: all 0.3s ease;
            z-index: 1000;
            min-width: 80px;
        }
        
        .language-switch:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 16px rgba(255, 107, 107, 0.4);
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }
            
            .answers-grid {
                grid-template-columns: 1fr;
                padding: 20px;
            }
            
            .answer-card {
                padding: 20px;
            }
            
            .category-stats {
                position: static;
                transform: none;
                margin-top: 15px;
            }
        }
        
        .fade-in {
            animation: fadeIn 0.6s ease-in;
        }
        
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <div class="header-content">
                <!-- Enhanced Navigation Bar -->
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px; padding: 15px 0; border-bottom: 1px solid rgba(255,255,255,0.2);">
                    <div>
                        <a href="index.html" style="background: rgba(255,255,255,0.2); color: white; padding: 10px 20px; border-radius: 25px; text-decoration: none; font-size: 1em; transition: all 0.3s ease; display: inline-flex; align-items: center; gap: 10px; font-weight: 500;"
                           onmouseover="this.style.background='rgba(255,255,255,0.3)'; this.style.transform='translateY(-2px)'"
                           onmouseout="this.style.background='rgba(255,255,255,0.2)'; this.style.transform='translateY(0)'">
                            🏠 Back to Home | العودة للرئيسية
                        </a>
                    </div>
                    <div style="display: flex; gap: 12px;">
                        <a href="arabic_course_lectures.html" style="background: rgba(255,255,255,0.15); color: white; padding: 8px 16px; border-radius: 20px; text-decoration: none; font-size: 0.9em; transition: all 0.3s ease;"
                           onmouseover="this.style.background='rgba(255,255,255,0.25)'"
                           onmouseout="this.style.background='rgba(255,255,255,0.15)'">📚 Lectures</a>
                        <a href="arabic_practical_exercises.html" style="background: rgba(255,255,255,0.15); color: white; padding: 8px 16px; border-radius: 20px; text-decoration: none; font-size: 0.9em; transition: all 0.3s ease;"
                           onmouseover="this.style.background='rgba(255,255,255,0.25)'"
                           onmouseout="this.style.background='rgba(255,255,255,0.15)'">🔬 Exercises</a>
                        <a href="interactive_assessment.html" style="background: rgba(255,255,255,0.15); color: white; padding: 8px 16px; border-radius: 20px; text-decoration: none; font-size: 0.9em; transition: all 0.3s ease;"
                           onmouseover="this.style.background='rgba(255,255,255,0.25)'"
                           onmouseout="this.style.background='rgba(255,255,255,0.15)'">🎯 Assessment</a>
                        <a href="analytics_dashboard.html" style="background: rgba(255,255,255,0.15); color: white; padding: 8px 16px; border-radius: 20px; text-decoration: none; font-size: 0.9em; transition: all 0.3s ease;"
                           onmouseover="this.style.background='rgba(255,255,255,0.25)'"
                           onmouseout="this.style.background='rgba(255,255,255,0.15)'">📊 Analytics</a>
                    </div>
                </div>
                
                <h1>Model Answers for Clinical Engineering</h1>
                <p style="font-size: 1.3rem; color: rgba(255,255,255,0.9); font-weight: 300; margin-bottom: 15px;">الإجابات النموذجية للهندسة السريرية</p>
                <p style="font-size: 1.1rem; color: rgba(255,255,255,0.8); max-width: 800px; margin: 0 auto;">
                    Comprehensive collection of model answers for all activities and tasks of the 12 lectures
                </p>
                <p style="font-size: 1rem; color: rgba(255,255,255,0.7); max-width: 800px; margin: 10px auto 0;">
                    مجموعة شاملة من الإجابات النموذجية لجميع أنشطة ومهام المحاضرات الـ 12
                </p>
            </div>
        </header>

        <!-- Search and Filter Section -->
        <div class="search-container">
            <h3 style="text-align: center; color: #667eea; margin-bottom: 20px;">🔍 Search & Filter | البحث والتصفية</h3>
            <input type="text" class="search-input" id="searchInput" placeholder="Search in model answers... | البحث في الإجابات النموذجية..." onkeyup="searchAnswers()">
            
            <div class="filter-buttons">
                <button class="filter-btn active" onclick="filterByCategory('all')">All Lectures | جميع المحاضرات</button>
                <button class="filter-btn" onclick="filterByCategory('fundamentals')">Fundamentals | الأساسيات</button>
                <button class="filter-btn" onclick="filterByCategory('management')">Management | الإدارة</button>
                <button class="filter-btn" onclick="filterByCategory('advanced')">Advanced | المتقدم</button>
                <button class="filter-btn" onclick="filterByCategory('specialized')">Specialized | التخصصي</button>
            </div>
        </div>

        <!-- Fundamentals Category -->
        <div class="lecture-category" data-category="fundamentals">
            <div class="category-header">
                <div class="category-stats">
                    <div class="stat-number">3</div>
                    <div class="stat-label">Lectures</div>
                </div>
                <h2 class="category-title">🎯 Fundamental Lectures</h2>
                <p class="category-subtitle">Basic Principles and Foundations | الأسس والمبادئ الأساسية</p>
            </div>

            <div class="answers-grid">
                <!-- Lecture 1 -->
                <div class="answer-card" data-lecture="1">
                    <div class="lecture-number">1</div>
                    <h3 class="lecture-title">Introduction to Clinical Engineering</h3>
                    <p class="lecture-description">
                        Model answers for activities related to defining clinical engineering and the role of clinical engineers in the healthcare system.
                    </p>

                    <div class="activities-section">
                        <h5 class="activities-title">📝 Activities and Model Answers:</h5>

                        <div class="activity-item">
                            <div class="activity-title">Activity 1: Defining the Clinical Engineer Role</div>
                            <div class="activity-answer">
                                <strong>Model Answer:</strong><br>
                                A clinical engineer is a specialist who combines engineering knowledge with medical expertise to ensure the safe and effective use of medical technology. Their role includes:
                                <br>• Managing and maintaining medical devices
                                <br>• Evaluating and purchasing new technology
                                <br>• Training medical staff on device usage
                                <br>• Ensuring compliance with standards and regulations
                                <br>• Managing risks and safety protocols
                            </div>
                        </div>

                        <div class="activity-item">
                            <div class="activity-title">Activity 2: Evolution of Clinical Engineering</div>
                            <div class="activity-answer">
                                <strong>Model Answer:</strong><br>
                                Clinical engineering evolved through several phases:
                                <br>• 1960s: Beginning of complex medical device usage
                                <br>• 1970s: Establishment of first clinical engineering programs
                                <br>• 1980s: Development of standards and regulations
                                <br>• 1990s: Focus on technology management
                                <br>• 2000s+: Integration of information technology and artificial intelligence
                            </div>
                        </div>
                    </div>

                    <div class="key-points">
                        <h5>🔑 Key Points:</h5>
                        <ul>
                            <li>Clinical engineering is a multidisciplinary field</li>
                            <li>Focus on safety and effectiveness</li>
                            <li>Importance of continuous training</li>
                            <li>Vital role in improving healthcare quality</li>
                        </ul>
                    </div>

                    <div class="answer-actions">
                        <button class="btn btn-success" onclick="viewDetailedAnswer(1)">View Details | عرض التفاصيل</button>
                        <button class="btn btn-info" onclick="downloadAnswer(1)">Download PDF | تحميل PDF</button>
                        <button class="btn btn-warning" onclick="practiceActivity(1)">Practice | تمرين عملي</button>
                    </div>
                </div>

                <!-- Lecture 2 -->
                <div class="answer-card" data-lecture="2">
                    <div class="lecture-number">2</div>
                    <h3 class="lecture-title">Healthcare Technology Management</h3>
                    <p class="lecture-description">
                        Model answers for activities related to healthcare technology management and health technology assessment.
                    </p>

                    <div class="activities-section">
                        <h5 class="activities-title">📝 Activities and Model Answers:</h5>

                        <div class="activity-item">
                            <div class="activity-title">Activity 1: HTM Framework</div>
                            <div class="activity-answer">
                                <strong>Model Answer:</strong><br>
                                Healthcare Technology Management (HTM) framework includes:
                                <br>• Strategic technology planning
                                <br>• Needs assessment and requirements evaluation
                                <br>• Procurement and evaluation process
                                <br>• Installation and commissioning
                                <br>• Maintenance and lifecycle management
                                <br>• Safe disposal and replacement
                            </div>
                        </div>

                        <div class="activity-item">
                            <div class="activity-title">Activity 2: Health Technology Assessment (HTA)</div>
                            <div class="activity-answer">
                                <strong>Model Answer:</strong><br>
                                HTA process includes evaluation of:
                                <br>• Clinical effectiveness and safety
                                <br>• Cost and economic effectiveness
                                <br>• Impact on quality of life
                                <br>• Ethical and social considerations
                                <br>• Impact on healthcare system
                            </div>
                        </div>
                    </div>

                    <div class="key-points">
                        <h5>🔑 Key Points:</h5>
                        <ul>
                            <li>HTM is a comprehensive approach to technology management</li>
                            <li>Importance of continuous evaluation</li>
                            <li>Balance between cost and benefit</li>
                            <li>Role of data in decision making</li>
                        </ul>
                    </div>

                    <div class="answer-actions">
                        <button class="btn btn-success" onclick="viewDetailedAnswer(2)">View Details | عرض التفاصيل</button>
                        <button class="btn btn-info" onclick="downloadAnswer(2)">Download PDF | تحميل PDF</button>
                        <button class="btn btn-warning" onclick="practiceActivity(2)">Practice | تمرين عملي</button>
                    </div>
                </div>

                <!-- Lecture 3 -->
                <div class="answer-card" data-lecture="3">
                    <div class="lecture-number">3</div>
                    <h3 class="lecture-title">Regulatory Standards and Compliance</h3>
                    <p class="lecture-description">
                        Model answers for activities related to regulatory standards and quality management systems in medical devices.
                    </p>

                    <div class="activities-section">
                        <h5 class="activities-title">📝 Activities and Model Answers:</h5>

                        <div class="activity-item">
                            <div class="activity-title">Activity 1: FDA and ISO Standards</div>
                            <div class="activity-answer">
                                <strong>Model Answer:</strong><br>
                                Key standards include:
                                <br>• FDA 21 CFR Part 820: Quality System for Medical Devices
                                <br>• ISO 13485: Quality Management System for Medical Devices
                                <br>• ISO 14971: Risk Management for Medical Devices
                                <br>• IEC 62304: Medical Device Software Lifecycle Processes
                                <br>• ISO 27001: Information Security Management
                            </div>
                        </div>

                        <div class="activity-item">
                            <div class="activity-title">Activity 2: Compliance Strategy</div>
                            <div class="activity-answer">
                                <strong>Model Answer:</strong><br>
                                Effective compliance strategy includes:
                                <br>• Identifying applicable standards
                                <br>• Developing policies and procedures
                                <br>• Training and qualification
                                <br>• Monitoring and internal auditing
                                <br>• Continuous improvement and updates
                            </div>
                        </div>
                    </div>

                    <div class="key-points">
                        <h5>🔑 Key Points:</h5>
                        <ul>
                            <li>Compliance is essential for ensuring safety</li>
                            <li>Standards evolve continuously</li>
                            <li>Importance of documentation and record keeping</li>
                            <li>Continuous team training</li>
                        </ul>
                    </div>

                    <div class="answer-actions">
                        <button class="btn btn-success" onclick="viewDetailedAnswer(3)">View Details | عرض التفاصيل</button>
                        <button class="btn btn-info" onclick="downloadAnswer(3)">Download PDF | تحميل PDF</button>
                        <button class="btn btn-warning" onclick="practiceActivity(3)">Practice | تمرين عملي</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div style="text-align: center; margin: 40px 0; padding: 30px; background: white; border-radius: 15px; box-shadow: 0 5px 15px rgba(0,0,0,0.1);">
            <h3 style="color: #667eea; margin-bottom: 25px;">🎯 Quick Actions | إجراءات سريعة</h3>
            <div style="display: flex; gap: 15px; justify-content: center; flex-wrap: wrap;">
                <button class="btn btn-success" onclick="exportAllAnswers()">Export All Answers | تصدير جميع الإجابات</button>
                <button class="btn btn-info" onclick="printAnswers()">Print Answers | طباعة الإجابات</button>
                <button class="btn btn-warning" onclick="resetProgress()">Reset Progress | إعادة تعيين</button>
                <a href="arabic_model_answers.html" class="btn" style="background: linear-gradient(45deg, #ff6b6b, #ee5a24);">Arabic Version | النسخة العربية</a>
            </div>
        </div>
    </div>

    <!-- Home Button -->
    <button class="home-btn" onclick="window.location.href='index.html'">
        🏠 Home<br>الرئيسية
    </button>

    <!-- Language Switch Button -->
    <button class="language-switch" onclick="window.location.href='arabic_model_answers.html'">
        🌐 عربي<br>Arabic
    </button>

    <script>
        // Model answers management system (English version)
        let currentFilter = 'all';
        let searchTerm = '';

        // Initialize page
        function initializePage() {
            // Add fade-in animation to cards
            const cards = document.querySelectorAll('.answer-card');
            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.classList.add('fade-in');
                }, index * 100);
            });

            // Load user progress if available
            loadUserProgress();
        }

        // Search functionality
        function searchAnswers() {
            searchTerm = document.getElementById('searchInput').value.toLowerCase();
            filterAnswers();
        }

        // Filter by category
        function filterByCategory(category) {
            currentFilter = category;

            // Update filter buttons
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');

            filterAnswers();
        }

        // Apply filters
        function filterAnswers() {
            const categories = document.querySelectorAll('.lecture-category');
            const cards = document.querySelectorAll('.answer-card');

            categories.forEach(category => {
                const categoryType = category.dataset.category;
                let hasVisibleCards = false;

                const categoryCards = category.querySelectorAll('.answer-card');
                categoryCards.forEach(card => {
                    const title = card.querySelector('.lecture-title').textContent.toLowerCase();
                    const description = card.querySelector('.lecture-description').textContent.toLowerCase();
                    const activities = card.querySelector('.activities-section').textContent.toLowerCase();

                    const matchesSearch = searchTerm === '' ||
                                        title.includes(searchTerm) ||
                                        description.includes(searchTerm) ||
                                        activities.includes(searchTerm);

                    const matchesFilter = currentFilter === 'all' || categoryType === currentFilter;

                    if (matchesSearch && matchesFilter) {
                        card.style.display = 'block';
                        hasVisibleCards = true;
                    } else {
                        card.style.display = 'none';
                    }
                });

                // Show/hide category based on visible cards
                if (currentFilter === 'all' || categoryType === currentFilter) {
                    category.style.display = hasVisibleCards ? 'block' : 'none';
                } else {
                    category.style.display = 'none';
                }
            });
        }

        // View detailed answer
        function viewDetailedAnswer(lectureNum) {
            const detailedAnswers = {
                1: `
Detailed Model Answers - Lecture 1: Introduction to Clinical Engineering

Activity 1: Defining the Clinical Engineer Role
Detailed Answer:
A clinical engineer is a specialized professional who combines engineering expertise with medical knowledge to ensure the safe and effective use of medical technology in clinical environments.

Core Responsibilities:
1. Medical Technology Management:
   - Planning and implementing equipment management programs
   - Developing policies and procedures
   - Managing databases and inventory

2. Maintenance and Calibration:
   - Developing preventive maintenance programs
   - Performing calibration and testing
   - Managing external maintenance contracts

3. Safety and Risk Management:
   - Implementing safety standards
   - Conducting risk assessments
   - Investigating incidents and failures

4. Training and Education:
   - Training medical staff
   - Developing training materials
   - Conducting workshops

5. Regulatory Compliance:
   - Ensuring compliance with standards
   - Preparing regulatory reports
   - Managing inspections

Activity 2: Evolution of Clinical Engineering
Detailed Answer:

Phase 1 (1960-1970):
- Emergence of complex medical devices
- Need for specialized technical expertise
- Beginning of engineer employment in hospitals

Phase 2 (1970-1980):
- Establishment of first clinical engineering programs
- Development of academic curricula
- Formation of professional societies

Phase 3 (1980-1990):
- Development of standards and regulations
- Focus on safety
- Growth of professional recognition

Phase 4 (1990-2000):
- Focus on technology management
- Development of information systems
- Global expansion

Phase 5 (2000-Present):
- Integration of information technology
- Artificial intelligence and machine learning
- Digital medicine and telemedicine
                `,
                2: `
Detailed Model Answers - Lecture 2: Healthcare Technology Management

Activity 1: HTM Framework
Detailed Answer:

Healthcare Technology Management (HTM) framework is a comprehensive and systematic approach to managing medical technology throughout its lifecycle.

Core Phases:

1. Strategic Planning:
   - Defining strategic objectives
   - Assessing current situation
   - Developing long-term plans
   - Resource allocation

2. Needs Assessment:
   - Analyzing clinical needs
   - Technical feasibility study
   - Financial impact assessment
   - Reviewing available alternatives

3. Procurement Process:
   - Preparing technical specifications
   - Evaluating proposals
   - Negotiating with suppliers
   - Contract management

4. Installation and Commissioning:
   - Installation planning
   - Acceptance testing
   - Training and qualification
   - Commissioning

5. Maintenance and Lifecycle Management:
   - Preventive maintenance programs
   - Spare parts management
   - Performance monitoring
   - Updates and upgrades

6. Disposal and Replacement:
   - End-of-life assessment
   - Replacement planning
   - Safe disposal
   - Value recovery

Activity 2: Health Technology Assessment (HTA)
Detailed Answer:

Health Technology Assessment is a systematic multidisciplinary process for evaluating the characteristics and effects of health technology.

Core Assessment Areas:

1. Clinical Effectiveness:
   - Evaluating clinical outcomes
   - Comparing with existing alternatives
   - Analyzing clinical data
   - Assessing scientific evidence

2. Safety:
   - Identifying potential risks
   - Evaluating side effects
   - Reviewing safety reports
   - Analyzing incidents

3. Cost and Economic Effectiveness:
   - Cost-effectiveness analysis
   - Cost-benefit analysis
   - Budget impact analysis
   - Return on investment evaluation

4. Social and Ethical Impact:
   - Assessing social acceptance
   - Ethical considerations
   - Impact on health equity
   - Legal issues

5. Organizational Impact:
   - Impact on workflow
   - Training requirements
   - Organizational structure changes
   - Impact on human resources
                `,
                3: `
Detailed Model Answers - Lecture 3: Regulatory Standards and Compliance

Activity 1: FDA and ISO Standards
Detailed Answer:

Key regulatory standards in the medical device industry:

1. FDA 21 CFR Part 820 (Quality System):
   - Quality management system requirements
   - Design and development controls
   - Document and record management
   - Production and process controls
   - Corrective and preventive actions

2. ISO 13485 (Quality Management System for Medical Devices):
   - Comprehensive quality system requirements
   - Focus on customer requirements
   - Integrated risk management
   - Continuous improvement
   - Management review

3. ISO 14971 (Risk Management):
   - Risk management process
   - Risk analysis and evaluation
   - Risk controls
   - Production and post-production information
   - Risk management file

4. IEC 62304 (Medical Device Software):
   - Software lifecycle processes
   - Software classification by safety
   - Development and testing requirements
   - Configuration management
   - Problem resolution

5. ISO 27001 (Information Security):
   - Information security management system
   - Security risk assessment
   - Security controls
   - Monitoring and review
   - Continuous improvement

Activity 2: Compliance Strategy
Detailed Answer:

Developing an effective compliance strategy:

1. Identifying Applicable Standards:
   - Analyzing regulatory requirements
   - Identifying target markets
   - Reviewing regulatory updates
   - Assessing impact on products

2. Developing Policies and Procedures:
   - Preparing quality manual
   - Developing operating procedures
   - Creating forms and templates
   - Defining responsibilities and authorities

3. Training and Qualification:
   - Basic training programs
   - Specialized training
   - Assessment and certification
   - Continuous training

4. Implementation and Monitoring:
   - Implementing procedures
   - Performance monitoring
   - Data collection and metrics
   - Periodic reporting

5. Auditing and Review:
   - Internal auditing
   - Management review
   - External auditing
   - Corrective actions

6. Continuous Improvement:
   - Data analysis
   - Identifying improvement opportunities
   - Implementing improvements
   - Monitoring effectiveness
                `
            };

            const answer = detailedAnswers[lectureNum] || 'Details not available currently';

            // Create modal or new window to display detailed answer
            const newWindow = window.open('', '_blank', 'width=800,height=600,scrollbars=yes');
            newWindow.document.write(`
                <html dir="ltr">
                <head>
                    <title>Detailed Answer - Lecture ${lectureNum}</title>
                    <style>
                        body { font-family: Arial, sans-serif; padding: 20px; line-height: 1.6; }
                        h1 { color: #667eea; }
                        pre { white-space: pre-wrap; font-family: Arial, sans-serif; }
                    </style>
                </head>
                <body>
                    <h1>Detailed Answer - Lecture ${lectureNum}</h1>
                    <pre>${answer}</pre>
                    <button onclick="window.print()" style="margin: 20px 0; padding: 10px 20px; background: #667eea; color: white; border: none; border-radius: 5px;">Print</button>
                    <button onclick="window.close()" style="margin: 20px 0; padding: 10px 20px; background: #6c757d; color: white; border: none; border-radius: 5px;">Close</button>
                </body>
                </html>
            `);
        }

        // Download answer as PDF
        function downloadAnswer(lectureNum) {
            // Simulate PDF download
            showNotification(`Downloading lecture ${lectureNum} answers | جاري تحميل إجابات المحاضرة ${lectureNum}`, 'info');

            // In a real implementation, this would generate and download a PDF
            setTimeout(() => {
                showNotification(`File downloaded successfully | تم تحميل الملف بنجاح`, 'success');
            }, 2000);
        }

        // Practice activity
        function practiceActivity(lectureNum) {
            // Redirect to practical exercises or simulation
            window.open(`arabic_practical_exercises.html#lecture-${lectureNum}`, '_blank');
        }

        // Export all answers
        function exportAllAnswers() {
            showNotification('Exporting all answers... | جاري تصدير جميع الإجابات...', 'info');

            // Simulate export process
            setTimeout(() => {
                const content = `
Export of All Model Answers - Clinical Engineering
تصدير جميع الإجابات النموذجية - الهندسة السريرية

Date | التاريخ: ${new Date().toLocaleDateString()}

This file contains all model answers for the 12 lectures in Clinical Engineering.
هذا الملف يحتوي على جميع الإجابات النموذجية للمحاضرات الـ 12 في الهندسة السريرية.

Included Lectures | المحاضرات المتضمنة:
1. Introduction to Clinical Engineering | مقدمة في الهندسة السريرية
2. Healthcare Technology Management | إدارة التكنولوجيا الصحية
3. Regulatory Standards and Compliance | المعايير التنظيمية والامتثال
... and more | والمزيد

For complete details, please visit the website.
للحصول على التفاصيل الكاملة، يرجى زيارة الموقع الإلكتروني.
                `;

                const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `model-answers-english-${new Date().toISOString().split('T')[0]}.txt`;
                a.click();
                URL.revokeObjectURL(url);

                showNotification('File exported successfully | تم تصدير الملف بنجاح', 'success');
            }, 2000);
        }

        // Print answers
        function printAnswers() {
            window.print();
        }

        // Reset progress
        function resetProgress() {
            if (confirm('Are you sure you want to reset progress? | هل أنت متأكد من إعادة تعيين التقدم؟')) {
                localStorage.removeItem('modelAnswersProgress');
                showNotification('Progress reset successfully | تم إعادة تعيين التقدم', 'warning');
            }
        }

        // Load user progress
        function loadUserProgress() {
            const progress = JSON.parse(localStorage.getItem('modelAnswersProgress')) || {};
            // Apply any saved progress indicators
        }

        // Show notification
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            const colors = {
                success: '#28a745',
                warning: '#ffc107',
                info: '#17a2b8',
                error: '#dc3545'
            };

            notification.style.cssText = `
                position: fixed;
                top: 20px;
                left: 20px;
                background: ${colors[type] || colors.info};
                color: white;
                padding: 15px 20px;
                border-radius: 10px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                z-index: 10000;
                font-weight: bold;
                max-width: 400px;
                animation: slideIn 0.5s ease;
            `;
            notification.textContent = message;

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.animation = 'slideOut 0.5s ease';
                setTimeout(() => {
                    if (document.body.contains(notification)) {
                        document.body.removeChild(notification);
                    }
                }, 500);
            }, 3000);
        }

        // Add CSS animations
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(-100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(-100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);

        // Initialize page on load
        document.addEventListener('DOMContentLoaded', initializePage);
    </script>
</body>
</html>
